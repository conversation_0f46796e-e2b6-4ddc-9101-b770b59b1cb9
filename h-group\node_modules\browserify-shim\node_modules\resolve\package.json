{"name": "resolve", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "version": "0.6.3", "repository": {"type": "git", "url": "git://github.com/substack/node-resolve.git"}, "main": "index.js", "keywords": ["resolve", "require", "node", "module"], "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}}