# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v4.1.1](https://github.com/browserify/browserify-rsa/compare/v4.1.0...v4.1.1) - 2024-09-26

### Commits

- Only apps should have lockfiles [`9d8732b`](https://github.com/browserify/browserify-rsa/commit/9d8732b17f25a24aa1c367bf244c1e95f3a4c485)
- [eslint] switch to eslint [`7e391c4`](https://github.com/browserify/browserify-rsa/commit/7e391c4dec3407ca1a7af4284f2c5f8c4a745ff2)
- [meta] add `auto-changelog` [`2e3fdef`](https://github.com/browserify/browserify-rsa/commit/2e3fdeff72d0695b7d9b4b6dc056404ad1d9da96)
- [Tests] migrate from travis to GHA [`9d29368`](https://github.com/browserify/browserify-rsa/commit/9d29368f3b76a808cc7575533b135b113c96a219)
- [Tests] improve test organization [`21b8fe5`](https://github.com/browserify/browserify-rsa/commit/21b8fe58085fed8f0b8bd2f111f25079152fa905)
- [Tests] `crypto.privateDecrypt` is only in node 0.12+ [`52a4253`](https://github.com/browserify/browserify-rsa/commit/52a4253a5f5b19401bab450f2f29e68480c56077)
- [meta] add `npmignore` and `safe-publish-latest` [`bd5d1c2`](https://github.com/browserify/browserify-rsa/commit/bd5d1c222daafeb2121b2b79b8feb536b565f4e7)
- [Fix] use `safe-buffer` [`28d2560`](https://github.com/browserify/browserify-rsa/commit/28d256045487adb3b88f7b958fa6728702f9f4f6)
- [meta] add `exports` [`50464d7`](https://github.com/browserify/browserify-rsa/commit/50464d70d37a1c771268cd336de6424eafa4f42d)
- [Deps] update `bn.js`, `randombytes` [`d833e49`](https://github.com/browserify/browserify-rsa/commit/d833e49e520f502cf8dfce1fe159041b6a1c6005)
- [meta] raise `engines.node` to 0.10, due to `parse-asn1` [`063d27b`](https://github.com/browserify/browserify-rsa/commit/063d27b4476dd7d833a57014cf9c6c786005c5dd)
- [Dev Deps] update `parse-asn1`, `tape` [`7d19782`](https://github.com/browserify/browserify-rsa/commit/7d197823631bcb274efe52970af0f42188b9b7fe)
- [meta] add missing `engines.node` [`a2e5e94`](https://github.com/browserify/browserify-rsa/commit/a2e5e9411b902439afb1fd9e61d853469a6d1a59)
- [meta] correct git URL [`f235983`](https://github.com/browserify/browserify-rsa/commit/f23598305d24c2fb1c76f582b1cb22ffba20f374)
- [Dev Deps] add missing deps from 2e3fdef [`5ea31da`](https://github.com/browserify/browserify-rsa/commit/5ea31da9edf0ec7788c0361bc1a8a90b040f6fe2)
- [meta] add `sideEffects` flag [`4a2d873`](https://github.com/browserify/browserify-rsa/commit/4a2d8730ea1725651103b0970a6af0b3871fd9be)

## [v4.1.0](https://github.com/browserify/browserify-rsa/compare/v4.0.1...v4.1.0) - 2020-11-12

### Merged

- bump bn.js to 5.0.0 [`#13`](https://github.com/browserify/browserify-rsa/pull/13)
- update loop in ger [`#10`](https://github.com/browserify/browserify-rsa/pull/10)

### Commits

- update tests [`7b34cc7`](https://github.com/browserify/browserify-rsa/commit/7b34cc75cda375fcfb2577f6c2334b273728a632)
- update index.js [`6c2c290`](https://github.com/browserify/browserify-rsa/commit/6c2c290a5b3af41f8a2f68ca7f8d340bfb6d38ec)
- update package.json [`9f906f8`](https://github.com/browserify/browserify-rsa/commit/9f906f8f76fcbca9f0c0a412f76eab03b01976cc)
- update README.md [`f55b31a`](https://github.com/browserify/browserify-rsa/commit/f55b31aa0d15a7bf1dd6dd222314fbd0d7a5fc2e)
- update travis config [`7342836`](https://github.com/browserify/browserify-rsa/commit/73428369fe9d976f41cbbac2b60421d65ea60516)
- add .gitignore [`d0a4613`](https://github.com/browserify/browserify-rsa/commit/d0a4613bb72d8874171cf9d666ea272cc3c7bc20)

## [v4.0.1](https://github.com/browserify/browserify-rsa/compare/v4.0.0...v4.0.1) - 2016-02-26

### Merged

- package.json: fix repository URL [`#8`](https://github.com/browserify/browserify-rsa/pull/8)

## [v4.0.0](https://github.com/browserify/browserify-rsa/compare/v3.0.0...v4.0.0) - 2015-10-29

### Merged

- bump bn.js and parse-asn1 [`#6`](https://github.com/browserify/browserify-rsa/pull/6)

### Commits

- fix dev dep [`3acf6c7`](https://github.com/browserify/browserify-rsa/commit/3acf6c7729e254429cb6485747ebf54fa9ccfb29)

## [v3.0.0](https://github.com/browserify/browserify-rsa/compare/v2.0.1...v3.0.0) - 2015-10-26

### Merged

- bump bn-js [`#5`](https://github.com/browserify/browserify-rsa/pull/5)
- Adding license and updating package.json [`#4`](https://github.com/browserify/browserify-rsa/pull/4)

### Commits

- Add LICENSE [`3ee2b4d`](https://github.com/browserify/browserify-rsa/commit/3ee2b4d3428c9504860e62d7c070d75dd2710f48)
- package: adds description [`af8556a`](https://github.com/browserify/browserify-rsa/commit/af8556a0d5355f6921dbdfee4bf4ff1190b05c10)

## [v2.0.1](https://github.com/browserify/browserify-rsa/compare/v2.0.0...v2.0.1) - 2015-05-20

### Merged

- update bn.js [`#3`](https://github.com/browserify/browserify-rsa/pull/3)

### Commits

- Fix badge URL to point to crypto-browserify org [`823888a`](https://github.com/browserify/browserify-rsa/commit/823888a93b513724a43e0d56bfe9c951e84c0e60)
- update readme [`0f404ac`](https://github.com/browserify/browserify-rsa/commit/0f404ac2235c4fa4a2124a29cb6cd9a6dbb25d22)

## [v2.0.0](https://github.com/browserify/browserify-rsa/compare/v1.2.0...v2.0.0) - 2015-01-28

## [v1.2.0](https://github.com/browserify/browserify-rsa/compare/v1.1.1...v1.2.0) - 2015-01-28

### Commits

- modularize [`de640bf`](https://github.com/browserify/browserify-rsa/commit/de640bff7f5fcdc1ed68ce420d972c5c4005f70f)

## v1.1.1 - 2015-01-06

### Commits

- left pad here and clean up tests [`240e16c`](https://github.com/browserify/browserify-rsa/commit/240e16c3b116dca1a63e463f494bd3447abb3b8a)
- first [`ceb731f`](https://github.com/browserify/browserify-rsa/commit/ceb731f7d56e4aba5440b99709dda6c2cb38b5dc)
- Update bn.js [`dfcf387`](https://github.com/browserify/browserify-rsa/commit/dfcf38757fc28a7d929a208f1775c59c9ab7f008)
