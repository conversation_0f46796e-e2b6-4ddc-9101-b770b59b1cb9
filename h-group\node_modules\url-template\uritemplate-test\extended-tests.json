{"Additional Examples 1": {"level": 4, "variables": {"id": "person", "token": "12345", "fields": ["id", "name", "picture"], "format": "json", "q": "URI Templates", "page": "5", "lang": "en", "geocode": ["37.76", "-122.427"], "first_name": "<PERSON>", "last.name": "<PERSON><PERSON>", "Some%20Thing": "foo", "number": 6, "long": 37.76, "lat": -122.427, "group_id": "12345", "query": "PREFIX dc: <http://purl.org/dc/elements/1.1/> SELECT ?book ?who WHERE { ?book dc:creator ?who }", "uri": "http://example.org/?uri=http%3A%2F%2Fexample.org%2F", "word": "<PERSON><PERSON><PERSON><PERSON>", "Stra%C3%9Fe": "<PERSON><PERSON><PERSON><PERSON> Weg", "random": "šöäŸœñê€£¥‡ÑÒÓÔÕÖ×ØÙÚàáâãäåæçÿ", "assoc_special_chars": {"šöäŸœñê€£¥‡ÑÒÓÔÕ": "Ö×ØÙÚàáâãäåæçÿ"}}, "testcases": [["{/id*}", "/person"], ["{/id*}{?fields,first_name,last.name,token}", ["/person?fields=id,name,picture&first_name=<PERSON>&last.name=<PERSON><PERSON>&token=12345", "/person?fields=id,picture,name&first_name=<PERSON>&last.name=<PERSON><PERSON>&token=12345", "/person?fields=picture,name,id&first_name=<PERSON>&last.name=<PERSON><PERSON>&token=12345", "/person?fields=picture,id,name&first_name=<PERSON>&last.name=<PERSON><PERSON>&token=12345", "/person?fields=name,picture,id&first_name=<PERSON>&last.name=<PERSON><PERSON>&token=12345", "/person?fields=name,id,picture&first_name=<PERSON>&last.name=<PERSON><PERSON>&token=12345"]], ["/search.{format}{?q,geocode,lang,locale,page,result_type}", ["/search.json?q=URI%20Templates&geocode=37.76,-122.427&lang=en&page=5", "/search.json?q=URI%20Templates&geocode=-122.427,37.76&lang=en&page=5"]], ["/test{/Some%20Thing}", "/test/foo"], ["/set{?number}", "/set?number=6"], ["/loc{?long,lat}", "/loc?long=37.76&lat=-122.427"], ["/base{/group_id,first_name}/pages{/page,lang}{?format,q}", "/base/12345/John/pages/5/en?format=json&q=URI%20Templates"], ["/sparql{?query}", "/sparql?query=PREFIX%20dc%3A%20%3Chttp%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%3E%20SELECT%20%3Fbook%20%3Fwho%20WHERE%20%7B%20%3Fbook%20dc%3Acreator%20%3Fwho%20%7D"], ["/go{?uri}", "/go?uri=http%3A%2F%2Fexample.org%2F%3Furi%3Dhttp%253A%252F%252Fexample.org%252F"], ["/service{?word}", "/service?word=dr%C3%BCcken"], ["/lookup{?Stra%C3%9Fe}", "/lookup?Stra%C3%9Fe=Gr%C3%BCner%20Weg"], ["{random}", "%C5%A1%C3%B6%C3%A4%C5%B8%C5%93%C3%B1%C3%AA%E2%82%AC%C2%A3%C2%A5%E2%80%A1%C3%91%C3%92%C3%93%C3%94%C3%95%C3%96%C3%97%C3%98%C3%99%C3%9A%C3%A0%C3%A1%C3%A2%C3%A3%C3%A4%C3%A5%C3%A6%C3%A7%C3%BF"], ["{?assoc_special_chars*}", "?%C5%A1%C3%B6%C3%A4%C5%B8%C5%93%C3%B1%C3%AA%E2%82%AC%C2%A3%C2%A5%E2%80%A1%C3%91%C3%92%C3%93%C3%94%C3%95=%C3%96%C3%97%C3%98%C3%99%C3%9A%C3%A0%C3%A1%C3%A2%C3%A3%C3%A4%C3%A5%C3%A6%C3%A7%C3%BF"]]}, "Additional Examples 2": {"level": 4, "variables": {"id": ["person", "albums"], "token": "12345", "fields": ["id", "name", "picture"], "format": "atom", "q": "URI Templates", "page": "10", "start": "5", "lang": "en", "geocode": ["37.76", "-122.427"]}, "testcases": [["{/id*}", ["/person/albums", "/albums/person"]], ["{/id*}{?fields,token}", ["/person/albums?fields=id,name,picture&token=12345", "/person/albums?fields=id,picture,name&token=12345", "/person/albums?fields=picture,name,id&token=12345", "/person/albums?fields=picture,id,name&token=12345", "/person/albums?fields=name,picture,id&token=12345", "/person/albums?fields=name,id,picture&token=12345", "/albums/person?fields=id,name,picture&token=12345", "/albums/person?fields=id,picture,name&token=12345", "/albums/person?fields=picture,name,id&token=12345", "/albums/person?fields=picture,id,name&token=12345", "/albums/person?fields=name,picture,id&token=12345", "/albums/person?fields=name,id,picture&token=12345"]]]}, "Additional Examples 3: Empty Variables": {"variables": {"empty_list": [], "empty_assoc": {}}, "testcases": [["{/empty_list}", [""]], ["{/empty_list*}", [""]], ["{?empty_list}", [""]], ["{?empty_list*}", [""]], ["{?empty_assoc}", [""]], ["{?empty_assoc*}", [""]]]}, "Additional Examples 4: Numeric Keys": {"variables": {"42": "The Answer to the Ultimate Question of Life, the Universe, and Everything", "1337": ["leet", "as", "it", "can", "be"], "german": {"11": "elf", "12": "<PERSON><PERSON><PERSON><PERSON>"}}, "testcases": [["{42}", "The%20Answer%20to%20the%20Ultimate%20Question%20of%20Life%2C%20the%20Universe%2C%20and%20Everything"], ["{?42}", "?42=The%20Answer%20to%20the%20Ultimate%20Question%20of%20Life%2C%20the%20Universe%2C%20and%20Everything"], ["{1337}", "leet,as,it,can,be"], ["{?1337*}", "?1337=leet&1337=as&1337=it&1337=can&1337=be"], ["{?german*}", ["?11=elf&12=zw%C3%B6lf", "?12=zw%C3%B6lf&11=elf"]]]}}