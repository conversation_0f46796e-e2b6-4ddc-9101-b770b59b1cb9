{"name": "spdx-license-ids", "version": "3.0.21", "description": "A list of SPDX license identifiers", "repository": "jslicense/spdx-license-ids", "author": "<PERSON><PERSON><PERSON> (https://github.com/shinnn)", "license": "CC0-1.0", "scripts": {"build": "node build.js", "latest": "node latest.js", "pretest": "npm run build", "test": "node test.js"}, "files": ["deprecated.json", "index.json"], "keywords": ["spdx", "license", "licenses", "id", "identifier", "identifiers", "json", "array", "oss"]}