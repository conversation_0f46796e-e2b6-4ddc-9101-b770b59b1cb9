const React = require('react');
const { createContext, useState, useEffect, useContext } = React;

// إنشاء سياق المصادقة
const AuthContext = createContext();

// مزود سياق المصادقة
const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // التحقق من وجود مستخدم مسجل الدخول عند تحميل التطبيق
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // مؤقتاً: إنشاء مستخدم افتراضي للاختبار
        if (window.api && window.api.users && window.api.users.getCurrentUser) {
          const user = await window.api.users.getCurrentUser();
          setCurrentUser(user);
        } else {
          // مستخدم افتراضي للاختبار
          setCurrentUser({
            id: 1,
            username: 'admin',
            full_name: 'المدير',
            role: 'admin',
            permissions: 'all'
          });
        }
      } catch (error) {
        console.error('خطأ في التحقق من المستخدم:', error);
        // في حالة الخطأ، استخدم مستخدم افتراضي
        setCurrentUser({
          id: 1,
          username: 'admin',
          full_name: 'المدير',
          role: 'admin',
          permissions: 'all'
        });
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // تسجيل الدخول
  const login = async (username, password) => {
    try {
      setLoading(true);
      setError(null);
      const user = await window.api.users.login(username, password);
      setCurrentUser(user);
      return user;
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      setLoading(true);
      await window.api.users.logout();
      setCurrentUser(null);
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // تغيير كلمة المرور
  const changePassword = async (id, oldPassword, newPassword) => {
    try {
      setLoading(true);
      setError(null);
      await window.api.users.changePassword(id, oldPassword, newPassword);
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // التحقق من صلاحيات المستخدم
  const hasPermission = (permission) => {
    if (!currentUser) return false;

    // المدير لديه جميع الصلاحيات
    if (currentUser.role === 'admin') return true;

    // التحقق من وجود الصلاحية في قائمة صلاحيات المستخدم
    if (currentUser.permissions) {
      if (currentUser.permissions === 'all') return true;

      const permissions = currentUser.permissions.split(',');
      return permissions.includes(permission);
    }

    return false;
  };

  const value = {
    currentUser,
    loading,
    error,
    login,
    logout,
    changePassword,
    hasPermission
  };

  return React.createElement(AuthContext.Provider, { value }, children);
};

// هوك استخدام سياق المصادقة
const useAuth = () => {
  return useContext(AuthContext);
};

module.exports = {
  AuthContext,
  AuthProvider,
  useAuth
};
