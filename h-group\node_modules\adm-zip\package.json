{"name": "adm-zip", "version": "0.5.16", "description": "Javascript implementation of zip for nodejs with support for electron original-fs. Allows user to create or extract zip files both in memory or to/from disk", "scripts": {"test": "mocha -R spec", "test:format": "npm run format:prettier:raw -- --check", "format": "npm run format:prettier", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier \"**/*.{js,yml,json}\""}, "keywords": ["zip", "methods", "archive", "unzip"], "homepage": "https://github.com/cthackers/adm-zip", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/cthackers)", "bugs": {"email": "<EMAIL>", "url": "https://github.com/cthackers/adm-zip/issues"}, "license": "MIT", "files": ["adm-zip.js", "headers", "methods", "util", "zipEntry.js", "zipFile.js", "LICENSE"], "main": "adm-zip.js", "repository": {"type": "git", "url": "https://github.com/cthackers/adm-zip.git"}, "engines": {"node": ">=12.0"}, "devDependencies": {"chai": "^4.3.4", "iconv-lite": "^0.6.3", "mocha": "^10.2.0", "prettier": "^3.3.2", "rimraf": "^3.0.2"}}