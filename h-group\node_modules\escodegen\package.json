{"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.1.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esprima": "~1.0.4", "estraverse": "~1.5.0", "esutils": "~1.0.0"}, "optionalDependencies": {"source-map": "~0.1.30"}, "devDependencies": {"esprima-moz": "*", "commonjs-everywhere": "~0.8.0", "q": "*", "bower": "*", "semver": "*", "chai": "~1.7.2", "grunt-contrib-jshint": "~0.5.0", "grunt-cli": "~0.1.9", "grunt": "~0.4.1", "grunt-mocha-test": "~0.6.2"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "grunt travis", "unit-test": "grunt test", "lint": "grunt lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}}