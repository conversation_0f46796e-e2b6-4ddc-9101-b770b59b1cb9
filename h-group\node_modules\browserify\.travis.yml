language: node_js
node_js:
  - "10"
  - "9"
  - "8"
  - "6"
  - "4"
  - "iojs"
  - "0.12"
  - "0.10"
  - "0.8"
  - "0.6"
before_install:
  # Old npm certs are untrusted https://github.com/npm/npm/issues/20191
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ] || [ "${TRAVIS_NODE_VERSION}" = "0.8" ]; then export NPM_CONFIG_STRICT_SSL=false; fi'
  - 'nvm install-latest-npm'
install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ] || [ "${TRAVIS_NODE_VERSION}" = "0.9" ]; then nvm install --latest-npm 0.8 && npm install && nvm use "${TRAVIS_NODE_VERSION}"; else npm install; fi;'
sudo: false
matrix:
  fast_finish: true
  allow_failures:
    - node_js: "0.6"
