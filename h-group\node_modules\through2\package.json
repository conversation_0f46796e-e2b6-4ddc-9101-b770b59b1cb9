{"name": "through2", "version": "0.4.2", "description": "A tiny wrapper around Node streams2 Transform to avoid explicit subclassing noise", "main": "through2.js", "scripts": {"test": "node test/test.js", "test-local": "brtapsauce-local test/basic-test.js"}, "repository": {"type": "git", "url": "https://github.com/rvagg/through2.git"}, "keywords": ["stream", "streams2", "through", "transform"], "author": "<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "license": "MIT", "dependencies": {"readable-stream": "~1.0.17", "xtend": "~2.1.1"}, "devDependencies": {"tape": "~2.3.0", "bl": "~0.6.0", "stream-spigot": "~3.0.1", "brtapsauce": "~0.2.2"}}