const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ProductsList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [categories, setCategories] = useState([]);
  
  // تحميل المنتجات
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل جميع المنتجات
        const data = await window.api.products.getAll();
        setProducts(data);
        setFilteredProducts(data);
        
        // استخراج الفئات الفريدة
        const uniqueCategories = [...new Set(data.map(product => product.category))].filter(Boolean);
        setCategories(uniqueCategories);
      } catch (error) {
        console.error('خطأ في تحميل المنتجات:', error);
        setError('حدث خطأ أثناء تحميل المنتجات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, []);
  
  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...products];
    
    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // تطبيق فلتر الفئة
    if (categoryFilter !== 'all') {
      result = result.filter(product => product.category === categoryFilter);
    }
    
    setFilteredProducts(result);
  }, [products, searchTerm, categoryFilter]);
  
  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setCategoryFilter('all');
  };
  
  // الانتقال إلى صفحة تفاصيل المنتج
  const handleViewProduct = (id) => {
    navigate(`/products/${id}`);
  };
  
  // إنشاء منتج جديد
  const handleCreateProduct = () => {
    if (!hasPermission('products_create')) {
      alert('ليس لديك صلاحية لإنشاء منتج جديد');
      return;
    }
    
    navigate('/products/new');
  };
  
  // تعديل المنتج
  const handleEditProduct = (id) => {
    if (!hasPermission('products_edit')) {
      alert('ليس لديك صلاحية لتعديل المنتج');
      return;
    }
    
    navigate(`/products/edit/${id}`);
  };
  
  // حذف المنتج
  const handleDeleteProduct = async (id) => {
    if (!hasPermission('products_delete')) {
      alert('ليس لديك صلاحية لحذف المنتج');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        const result = await window.api.products.delete(id);
        if (result.success) {
          // تحديث القائمة بعد الحذف
          setProducts(products.filter(product => product.id !== id));
          alert('تم حذف المنتج بنجاح');
        } else {
          throw new Error('فشل في حذف المنتج');
        }
      } catch (error) {
        console.error('خطأ في حذف المنتج:', error);
        alert('حدث خطأ أثناء حذف المنتج. يرجى المحاولة مرة أخرى.');
      }
    }
  };
  
  // تصدير المنتجات إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredProducts.map(product => ({
        'اسم المنتج': product.name,
        'الوصف': product.description || '-',
        'الفئة': product.category || '-',
        'السعر الافتراضي': product.default_price ? `${product.default_price.toLocaleString()} ر.س` : '-'
      }));
      
      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'المنتجات');
      
      if (result.success) {
        alert(`تم تصدير المنتجات بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير المنتجات');
      }
    } catch (error) {
      console.error('خطأ في تصدير المنتجات:', error);
      alert('حدث خطأ أثناء تصدير المنتجات. يرجى المحاولة مرة أخرى.');
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل المنتجات...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  return (
    <div className="products-list-page">
      <div className="page-header">
        <h2>المنتجات</h2>
        <div className="page-actions">
          {hasPermission('products_create') && (
            <button className="btn btn-primary" onClick={handleCreateProduct}>
              <i className="fas fa-plus"></i> إضافة منتج جديد
            </button>
          )}
          
          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>
      
      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">فلاتر البحث</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">بحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="اسم المنتج أو الوصف"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">الفئة</label>
                <select
                  className="form-control"
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  <option value="all">جميع الفئات</option>
                  {categories.map((category, index) => (
                    <option key={index} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          
          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>
      
      {/* قائمة المنتجات */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة المنتجات</h5>
            <span className="badge bg-primary">{filteredProducts.length} منتج</span>
          </div>
        </div>
        <div className="card-body">
          {filteredProducts.length === 0 ? (
            <div className="alert alert-info">لا توجد منتجات مطابقة للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>اسم المنتج</th>
                    <th>الوصف</th>
                    <th>الفئة</th>
                    <th>السعر الافتراضي</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map(product => (
                    <tr key={product.id}>
                      <td>{product.name}</td>
                      <td>{product.description || '-'}</td>
                      <td>{product.category || '-'}</td>
                      <td>{product.default_price ? `${product.default_price.toLocaleString()} ر.س` : '-'}</td>
                      <td>
                        <div className="btn-group">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => handleViewProduct(product.id)}
                            title="عرض التفاصيل"
                          >
                            <i className="fas fa-eye"></i>
                          </button>
                          
                          {hasPermission('products_edit') && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleEditProduct(product.id)}
                              title="تعديل"
                            >
                              <i className="fas fa-edit"></i>
                            </button>
                          )}
                          
                          {hasPermission('products_delete') && (
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteProduct(product.id)}
                              title="حذف"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = ProductsList;
