const React = require('react');

// استيراد المكونات المطلوبة فقط
const Dashboard = require('./pages/Dashboard');

// مكون الحماية للمسارات (مبسط)
const ProtectedRoute = ({ children }) => {
  // للتبسيط، سنعتبر المستخدم مسجل الدخول دائماً
  return children;
};

// المكون الرئيسي للتطبيق (مبسط)
const App = () => {
  return React.createElement('div', { className: 'app' },
    React.createElement('div', { className: 'app-container' },
      React.createElement('div', { className: 'content-container' },
        React.createElement('main', { className: 'main-content' },
          React.createElement(Dashboard)
        )
      )
    )
  );
};

module.exports = App;
