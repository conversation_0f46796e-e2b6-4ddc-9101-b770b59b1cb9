import React, { useState, useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// استيراد السياقات
import { AuthProvider } from './contexts/AuthContext';
import { SettingsProvider } from './contexts/SettingsContext';
import { NotificationsProvider } from './contexts/NotificationsContext';

// استيراد المكونات
import Navbar from './components/layout/Navbar';
import Sidebar from './components/layout/Sidebar';
import Login from './pages/auth/Login';
import Dashboard from './pages/Dashboard';
import NotFound from './pages/NotFound';

// استيراد صفحات الطلبات
import OrdersList from './pages/orders/OrdersList';
import OrderDetails from './pages/orders/OrderDetails';
import OrderForm from './pages/orders/OrderForm';

// استيراد صفحات العملاء
import CustomersList from './pages/customers/CustomersList';
import CustomerDetails from './pages/customers/CustomerDetails';
import CustomerForm from './pages/customers/CustomerForm';

// استيراد صفحات المنتجات
import ProductsList from './pages/products/ProductsList';
import ProductDetails from './pages/products/ProductDetails';
import ProductForm from './pages/products/ProductForm';

// استيراد صفحات المواد الخام
import MaterialsList from './pages/materials/MaterialsList';
import MaterialDetails from './pages/materials/MaterialDetails';
import MaterialForm from './pages/materials/MaterialForm';

// استيراد صفحات المخزون
import InventoryList from './pages/inventory/InventoryList';
import InventoryForm from './pages/inventory/InventoryForm';

// استيراد صفحات العمال
import WorkersList from './pages/workers/WorkersList';
import WorkerDetails from './pages/workers/WorkerDetails';
import WorkerForm from './pages/workers/WorkerForm';

// استيراد صفحات المصنع والتكاليف
import ExpensesList from './pages/expenses/ExpensesList';
import ExpenseForm from './pages/expenses/ExpenseForm';

// استيراد صفحات الفواتير
import InvoicesList from './pages/invoices/InvoicesList';
import InvoiceDetails from './pages/invoices/InvoiceDetails';
import InvoiceForm from './pages/invoices/InvoiceForm';

// استيراد صفحات التقارير
import SalesReport from './pages/reports/SalesReport';
import ExpensesReport from './pages/reports/ExpensesReport';
import ProfitReport from './pages/reports/ProfitReport';
import WorkersReport from './pages/reports/WorkersReport';
import MaterialsReport from './pages/reports/MaterialsReport';
import ProductsReport from './pages/reports/ProductsReport';

// استيراد صفحات الإعدادات
import Settings from './pages/settings/Settings';
import UsersList from './pages/settings/UsersList';
import UserForm from './pages/settings/UserForm';
import BackupRestore from './pages/settings/BackupRestore';

// مكون الحماية للمسارات
const ProtectedRoute = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // التحقق من وجود مستخدم مسجل الدخول
    const checkAuth = async () => {
      try {
        const user = await window.api.users.getCurrentUser();
        setIsAuthenticated(!!user);
      } catch (error) {
        console.error('خطأ في التحقق من المستخدم:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  if (loading) {
    return <div className="loading">جاري التحميل...</div>;
  }

  return isAuthenticated ? children : <Navigate to="/login" />;
};

// المكون الرئيسي للتطبيق
const App = () => {
  return (
    <AuthProvider>
      <SettingsProvider>
        <NotificationsProvider>
          <Router>
            <div className="app">
              <Routes>
                <Route path="/login" element={<Login />} />

                <Route path="/" element={
                  <ProtectedRoute>
                    <div className="app-container">
                      <Navbar />
                      <div className="content-container">
                        <Sidebar />
                        <main className="main-content">
                          <Dashboard />
                        </main>
                      </div>
                    </div>
                  </ProtectedRoute>
                } />

              {/* مسارات الطلبات */}
              <Route path="/orders" element={
                <ProtectedRoute>
                  <div className="app-container">
                    <Navbar />
                    <div className="content-container">
                      <Sidebar />
                      <main className="main-content">
                        <OrdersList />
                      </main>
                    </div>
                  </div>
                </ProtectedRoute>
              } />

              <Route path="/orders/:id" element={
                <ProtectedRoute>
                  <div className="app-container">
                    <Navbar />
                    <div className="content-container">
                      <Sidebar />
                      <main className="main-content">
                        <OrderDetails />
                      </main>
                    </div>
                  </div>
                </ProtectedRoute>
              } />

              <Route path="/orders/new" element={
                <ProtectedRoute>
                  <div className="app-container">
                    <Navbar />
                    <div className="content-container">
                      <Sidebar />
                      <main className="main-content">
                        <OrderForm />
                      </main>
                    </div>
                  </div>
                </ProtectedRoute>
              } />

              <Route path="/orders/edit/:id" element={
                <ProtectedRoute>
                  <div className="app-container">
                    <Navbar />
                    <div className="content-container">
                      <Sidebar />
                      <main className="main-content">
                        <OrderForm />
                      </main>
                    </div>
                  </div>
                </ProtectedRoute>
              } />

              {/* مسارات أخرى مماثلة للعملاء والمنتجات والمواد والعمال والفواتير والتقارير والإعدادات */}

              {/* مسار غير موجود */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </div>
        </Router>
        </NotificationsProvider>
      </SettingsProvider>
    </AuthProvider>
  );
};

export default App;
