const React = require('react');
const { useState, useEffect } = React;
const { HashRouter: Router, Routes, Route, Navigate } = require('react-router-dom');

// استيراد السياقات
const { AuthProvider } = require('./contexts/AuthContext');
const { SettingsProvider } = require('./contexts/SettingsContext');
const { NotificationsProvider } = require('./contexts/NotificationsContext');

// استيراد المكونات
const Navbar = require('./components/layout/Navbar');
const Sidebar = require('./components/layout/Sidebar');
const Login = require('./pages/auth/Login');
const Dashboard = require('./pages/Dashboard');
const NotFound = require('./pages/NotFound');

// استيراد صفحات الطلبات
const OrdersList = require('./pages/orders/OrdersList');
const OrderDetails = require('./pages/orders/OrderDetails');
const OrderForm = require('./pages/orders/OrderFormSimple');

// استيراد صفحات العملاء
const CustomersList = require('./pages/customers/CustomersListSimple');

// استيراد الصفحات البسيطة
const {
  CustomerDetails,
  CustomerForm,
  ProductsList,
  ProductDetails,
  ProductForm,
  MaterialsList,
  MaterialDetails,
  MaterialForm,
  InventoryList,
  InventoryForm,
  WorkersList,
  WorkerDetails,
  WorkerForm,
  ExpensesList,
  ExpenseForm,
  InvoicesList,
  InvoiceDetails,
  InvoiceForm,
  SalesReport,
  ExpensesReport,
  ProfitReport,
  WorkersReport,
  MaterialsReport,
  ProductsReport,
  Settings,
  UsersList,
  UserForm,
  BackupRestore
} = require('./pages/SimplePageTemplate');

// مكون الحماية للمسارات
const ProtectedRoute = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(true); // مؤقتاً نعتبر المستخدم مسجل الدخول

  return isAuthenticated ? children : React.createElement(Navigate, { to: '/login' });
};

// تخطيط الصفحة مع الشريط الجانبي والعلوي
const Layout = ({ children }) => {
  return React.createElement('div', { className: 'app-container' },
    React.createElement(Navbar),
    React.createElement('div', { className: 'content-container' },
      React.createElement(Sidebar),
      React.createElement('main', { className: 'main-content' },
        children
      )
    )
  );
};

// المكون الرئيسي للتطبيق
const App = () => {
  return React.createElement(AuthProvider, null,
    React.createElement(SettingsProvider, null,
      React.createElement(NotificationsProvider, null,
        React.createElement(Router, null,
          React.createElement('div', { className: 'app' },
            React.createElement(Routes, null,
              // صفحة تسجيل الدخول
              React.createElement(Route, {
                path: '/login',
                element: React.createElement(Login)
              }),

              // الصفحة الرئيسية
              React.createElement(Route, {
                path: '/',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(Dashboard)
                  )
                )
              }),

              // مسارات الطلبات
              React.createElement(Route, {
                path: '/orders',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrdersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrderForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrderDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/orders/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(OrderForm)
                  )
                )
              }),

              // مسارات العملاء
              React.createElement(Route, {
                path: '/customers',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/customers/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomerForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/customers/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomerDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/customers/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(CustomerForm)
                  )
                )
              }),

              // مسارات المنتجات
              React.createElement(Route, {
                path: '/products',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductsList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/products/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/products/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/products/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductForm)
                  )
                )
              }),

              // مسارات المواد الخام
              React.createElement(Route, {
                path: '/materials',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialsList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/materials/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialForm)
                  )
                )
              }),

              // مسارات المخزون
              React.createElement(Route, {
                path: '/inventory',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InventoryList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/inventory/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InventoryForm)
                  )
                )
              }),

              // مسارات العمال
              React.createElement(Route, {
                path: '/workers',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/workers/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkerForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/workers/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkerDetails)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/workers/edit/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkerForm)
                  )
                )
              }),

              // مسارات المصروفات
              React.createElement(Route, {
                path: '/expenses',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ExpensesList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/expenses/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ExpenseForm)
                  )
                )
              }),

              // مسارات الفواتير
              React.createElement(Route, {
                path: '/invoices',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InvoicesList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/invoices/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InvoiceForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/invoices/:id',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(InvoiceDetails)
                  )
                )
              }),

              // مسارات التقارير
              React.createElement(Route, {
                path: '/reports',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(SalesReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/sales',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(SalesReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/expenses',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ExpensesReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/profit',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProfitReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/workers',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(WorkersReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/materials',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(MaterialsReport)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/reports/products',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(ProductsReport)
                  )
                )
              }),

              // مسارات الإعدادات
              React.createElement(Route, {
                path: '/settings',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(Settings)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/settings/users',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(UsersList)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/settings/users/new',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(UserForm)
                  )
                )
              }),
              React.createElement(Route, {
                path: '/settings/backup',
                element: React.createElement(ProtectedRoute, null,
                  React.createElement(Layout, null,
                    React.createElement(BackupRestore)
                  )
                )
              }),

              // صفحة غير موجودة
              React.createElement(Route, {
                path: '*',
                element: React.createElement(NotFound)
              })
            )
          )
        )
      )
    )
  );
};

module.exports = App;
