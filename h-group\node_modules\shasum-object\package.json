{"name": "shasum-object", "description": "get the shasum of a buffer or object", "version": "1.0.0", "author": "<PERSON><PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/goto-bus-stop/shasum-object/issues"}, "dependencies": {"fast-safe-stringify": "^2.0.7"}, "devDependencies": {"safe-buffer": "^5.2.0", "standard": "^14.3.1", "tape": "^4.10.0"}, "homepage": "https://github.com/goto-bus-stop/shasum-object", "keywords": ["buffer", "hash", "object", "sha1", "shasum"], "license": "Apache-2.0", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/goto-bus-stop/shasum-object.git"}, "scripts": {"test": "standard && node test"}}