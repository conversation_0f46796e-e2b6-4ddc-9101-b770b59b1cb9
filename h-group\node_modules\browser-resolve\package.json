{"name": "browser-resolve", "version": "2.0.0", "description": "resolve which handles browser field support in package.json", "main": "index.js", "files": ["index.js", "empty.js"], "scripts": {"test": "node scripts/setup-symlinks.js && mocha --reporter list test/*.js"}, "repository": {"type": "git", "url": "git://github.com/browserify/browser-resolve.git"}, "keywords": ["resolve", "browser"], "author": "<PERSON>ylman <<EMAIL>>", "license": "MIT", "dependencies": {"resolve": "^1.17.0"}, "devDependencies": {"mocha": "^2.5.3"}}