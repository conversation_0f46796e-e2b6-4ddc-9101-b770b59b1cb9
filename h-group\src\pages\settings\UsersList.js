const React = require('react');
const { useState, useEffect } = React;

const UsersList = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل المستخدمين
      const mockUsers = [
        {
          id: 1,
          username: 'admin',
          full_name: 'المدير العام',
          role: 'admin',
          permissions: 'all',
          created_at: '2024-01-01'
        },
        {
          id: 2,
          username: 'manager',
          full_name: 'مدير المبيعات',
          role: 'manager',
          permissions: 'orders,customers,reports',
          created_at: '2024-02-01'
        }
      ];
      setUsers(mockUsers);
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      try {
        // محاكاة حذف المستخدم
        setUsers(prev => prev.filter(user => user.id !== id));
      } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
      }
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل المستخدمين...');
  }

  return React.createElement('div', { className: 'users-list' },
    React.createElement('div', { className: 'page-header' },
      React.createElement('h1', null, 'إدارة المستخدمين'),
      React.createElement('button', { className: 'btn btn-primary' }, 'إضافة مستخدم جديد')
    ),

    React.createElement('table', { className: 'table' },
      React.createElement('thead', null,
        React.createElement('tr', null,
          React.createElement('th', null, 'اسم المستخدم'),
          React.createElement('th', null, 'الاسم الكامل'),
          React.createElement('th', null, 'الدور'),
          React.createElement('th', null, 'الصلاحيات'),
          React.createElement('th', null, 'تاريخ الإنشاء'),
          React.createElement('th', null, 'الإجراءات')
        )
      ),
      React.createElement('tbody', null,
        users.map(user =>
          React.createElement('tr', { key: user.id },
            React.createElement('td', null, user.username),
            React.createElement('td', null, user.full_name),
            React.createElement('td', null, user.role === 'admin' ? 'مدير' : 'مستخدم'),
            React.createElement('td', null, user.permissions === 'all' ? 'جميع الصلاحيات' : user.permissions),
            React.createElement('td', null, new Date(user.created_at).toLocaleDateString('ar-SA')),
            React.createElement('td', null,
              React.createElement('button', { className: 'btn btn-sm btn-outline' }, 'تعديل'),
              ' ',
              React.createElement('button', {
                className: 'btn btn-sm btn-danger',
                onClick: () => handleDeleteUser(user.id),
                disabled: user.role === 'admin'
              }, 'حذف')
            )
          )
        )
      )
    )
  );
};

module.exports = UsersList;
