const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const CostPrediction = require('../../components/orders/CostPrediction');
const OrderCostCalculator = require('../../components/orders/OrderCostCalculator');

const OrderForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [isEditMode, setIsEditMode] = useState(!!id);
  const [order, setOrder] = useState({
    customer_id: '',
    product_id: '',
    order_date: new Date().toISOString().split('T')[0],
    delivery_date: '',
    status: 'جديد',
    specifications: '',
    materials: [],
    worker_fee: 0,
    factory_fee: 0,
    designer_fee: 0,
    owner_margin: 0,
    total_cost: 0,
    final_price: 0,
    notes: ''
  });

  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showPrediction, setShowPrediction] = useState(false);

  // التحقق من الصلاحيات
  useEffect(() => {
    if (isEditMode && !hasPermission('orders_edit')) {
      alert('ليس لديك صلاحية لتعديل الطلبات');
      navigate('/orders');
      return;
    }

    if (!isEditMode && !hasPermission('orders_create')) {
      alert('ليس لديك صلاحية لإنشاء طلبات جديدة');
      navigate('/orders');
      return;
    }
  }, [isEditMode, hasPermission, navigate]);

  // تحميل البيانات الأولية
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل العملاء
        const customersData = await window.api.customers.getAll();
        setCustomers(customersData);

        // تحميل المنتجات
        const productsData = await window.api.products.getAll();
        setProducts(productsData);

        // تحميل بيانات الطلب في حالة التعديل
        if (isEditMode) {
          const orderData = await window.api.orders.getById(id);

          if (!orderData) {
            throw new Error('الطلب غير موجود');
          }

          // تنسيق التواريخ
          orderData.order_date = orderData.order_date ? orderData.order_date.split('T')[0] : '';
          orderData.delivery_date = orderData.delivery_date ? orderData.delivery_date.split('T')[0] : '';

          // تحميل المواد المستخدمة في الطلب
          const materialsData = await window.api.orders.getMaterials(id);
          orderData.materials = materialsData;

          setOrder(orderData);
        }
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        setError('حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [id, isEditMode]);

  // تغيير قيمة في نموذج الطلب
  const handleChange = (e) => {
    const { name, value } = e.target;

    // معالجة خاصة للقيم العددية
    if (['worker_fee', 'factory_fee', 'designer_fee', 'owner_margin'].includes(name)) {
      setOrder(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else {
      setOrder(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // تحديث تكاليف الطلب
  const handleCostUpdate = (costData) => {
    setOrder(prev => ({
      ...prev,
      materials: costData.materials,
      worker_fee: costData.worker_fee,
      factory_fee: costData.factory_fee,
      designer_fee: costData.designer_fee,
      owner_margin: costData.owner_margin,
      total_cost: costData.total_cost,
      final_price: costData.final_price
    }));
  };

  // تطبيق التنبؤ بالتكاليف
  const handleApplyPrediction = (prediction) => {
    setOrder(prev => ({
      ...prev,
      materials: prediction.materials || [],
      worker_fee: prediction.worker_fee || 0,
      factory_fee: prediction.factory_fee || 0,
      designer_fee: prediction.designer_fee || 0,
      owner_margin: prediction.owner_margin || 0
    }));

    setShowPrediction(false);
  };

  // إرسال نموذج الطلب
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // التحقق من صحة المدخلات
      if (!order.customer_id) {
        alert('يجب اختيار عميل');
        return;
      }

      if (!order.order_date) {
        alert('يجب تحديد تاريخ الطلب');
        return;
      }

      // إنشاء أو تعديل الطلب
      let result;

      if (isEditMode) {
        result = await window.api.orders.update(id, order);

        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'update_order',
          action_details: `تعديل الطلب رقم ${order.order_number}`
        });

        alert('تم تعديل الطلب بنجاح');
      } else {
        result = await window.api.orders.create(order);

        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'create_order',
          action_details: `إنشاء طلب جديد رقم ${result.order_number}`
        });

        alert('تم إنشاء الطلب بنجاح');
      }

      // الانتقال إلى صفحة تفاصيل الطلب
      navigate(`/orders/${result.id}`);
    } catch (error) {
      console.error('خطأ في حفظ الطلب:', error);
      alert('حدث خطأ أثناء حفظ الطلب. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="order-form-page">
      <div className="page-header">
        <h2>{isEditMode ? `تعديل الطلب رقم ${order.order_number}` : 'إنشاء طلب جديد'}</h2>
        <button className="btn btn-secondary" onClick={() => navigate('/orders')}>
          <i className="fas fa-times"></i> إلغاء
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">بيانات الطلب</div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6">
                    <div className="form-group">
                      <label className="form-label">العميل</label>
                      <select
                        className="form-control"
                        name="customer_id"
                        value={order.customer_id}
                        onChange={handleChange}
                        required
                      >
                        <option value="">اختر العميل</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6">
                    <div className="form-group">
                      <label className="form-label">المنتج</label>
                      <select
                        className="form-control"
                        name="product_id"
                        value={order.product_id}
                        onChange={handleChange}
                      >
                        <option value="">اختر المنتج</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <div className="form-group">
                      <label className="form-label">تاريخ الطلب</label>
                      <input
                        type="date"
                        className="form-control"
                        name="order_date"
                        value={order.order_date}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="col-md-6">
                    <div className="form-group">
                      <label className="form-label">تاريخ التسليم المتوقع</label>
                      <input
                        type="date"
                        className="form-control"
                        name="delivery_date"
                        value={order.delivery_date}
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <div className="form-group">
                      <label className="form-label">حالة الطلب</label>
                      <select
                        className="form-control"
                        name="status"
                        value={order.status}
                        onChange={handleChange}
                        required
                      >
                        <option value="جديد">جديد</option>
                        <option value="قيد التنفيذ">قيد التنفيذ</option>
                        <option value="مكتمل">مكتمل</option>
                        <option value="ملغي">ملغي</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">المواصفات</label>
                  <textarea
                    className="form-control"
                    name="specifications"
                    value={order.specifications}
                    onChange={handleChange}
                    rows="4"
                  ></textarea>
                </div>

                <div className="form-group">
                  <label className="form-label">ملاحظات</label>
                  <textarea
                    className="form-control"
                    name="notes"
                    value={order.notes}
                    onChange={handleChange}
                    rows="3"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <div className="d-flex justify-content-between align-items-center">
                  <h5>التنبؤ بالتكاليف</h5>
                  <button
                    type="button"
                    className="btn btn-sm btn-primary"
                    onClick={() => setShowPrediction(!showPrediction)}
                  >
                    <i className="fas fa-magic"></i> {showPrediction ? 'إخفاء التنبؤ' : 'عرض التنبؤ'}
                  </button>
                </div>
              </div>
              <div className="card-body">
                {showPrediction ? (
                  <CostPrediction
                    productId={order.product_id}
                    specifications={order.specifications}
                    onApplyPrediction={handleApplyPrediction}
                  />
                ) : (
                  <div className="alert alert-info">
                    يمكنك استخدام ميزة التنبؤ بالتكاليف للحصول على تقدير تلقائي للتكاليف بناءً على الطلبات المشابهة.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4">
          <OrderCostCalculator
            order={order}
            onChange={handleCostUpdate}
          />
        </div>

        <div className="form-actions mt-4">
          <button type="submit" className="btn btn-primary">
            <i className="fas fa-save"></i> {isEditMode ? 'حفظ التعديلات' : 'إنشاء الطلب'}
          </button>
          <button type="button" className="btn btn-secondary" onClick={() => navigate('/orders')}>
            <i className="fas fa-times"></i> إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

module.exports = OrderForm;
