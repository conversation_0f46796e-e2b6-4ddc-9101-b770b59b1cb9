{"version": 3, "sources": ["../src/index.js", "../src/utils.js", "../src/parse-chunked.js", "../src/stringify-chunked.js", "../src/stringify-info.js", "../src/web-streams.js"], "sourcesContent": ["export { parseChunked } from './parse-chunked.js';\nexport { stringifyChunked } from './stringify-chunked.js';\nexport { stringifyInfo } from './stringify-info.js';\nexport { createStringifyWebStream, parseFromWebStream } from './web-streams.js';\n", "export function isIterable(value) {\n    return (\n        typeof value === 'object' &&\n        value !== null &&\n        (\n            typeof value[Symbol.iterator] === 'function' ||\n            typeof value[Symbol.asyncIterator] === 'function'\n        )\n    );\n}\n\nexport function replaceValue(holder, key, value, replacer) {\n    if (value && typeof value.toJSON === 'function') {\n        value = value.toJSON();\n    }\n\n    if (replacer !== null) {\n        value = replacer.call(holder, String(key), value);\n    }\n\n    switch (typeof value) {\n        case 'function':\n        case 'symbol':\n            value = undefined;\n            break;\n\n        case 'object':\n            if (value !== null) {\n                const cls = value.constructor;\n                if (cls === String || cls === Number || cls === Boolean) {\n                    value = value.valueOf();\n                }\n            }\n            break;\n    }\n\n    return value;\n}\n\nexport function normalizeReplacer(replacer) {\n    if (typeof replacer === 'function') {\n        return replacer;\n    }\n\n    if (Array.isArray(replacer)) {\n        const allowlist = new Set(replacer\n            .map(item => {\n                const cls = item && item.constructor;\n                return cls === String || cls === Number ? String(item) : null;\n            })\n            .filter(item => typeof item === 'string')\n        );\n\n        return [...allowlist];\n    }\n\n    return null;\n}\n\nexport function normalizeSpace(space) {\n    if (typeof space === 'number') {\n        if (!Number.isFinite(space) || space < 1) {\n            return false;\n        }\n\n        return ' '.repeat(Math.min(space, 10));\n    }\n\n    if (typeof space === 'string') {\n        return space.slice(0, 10) || false;\n    }\n\n    return false;\n}\n\nexport function normalizeStringifyOptions(optionsOrReplacer, space) {\n    if (optionsOrReplacer === null || Array.isArray(optionsOrReplacer) || typeof optionsOrReplacer !== 'object') {\n        optionsOrReplacer = {\n            replacer: optionsOrReplacer,\n            space\n        };\n    }\n\n    let replacer = normalizeReplacer(optionsOrReplacer.replacer);\n    let getKeys = Object.keys;\n\n    if (Array.isArray(replacer)) {\n        const allowlist = replacer;\n\n        getKeys = () => allowlist;\n        replacer = null;\n    }\n\n    return {\n        ...optionsOrReplacer,\n        replacer,\n        getKeys,\n        space: normalizeSpace(optionsOrReplacer.space)\n    };\n}\n", "import { isIterable } from './utils.js';\n\nconst STACK_OBJECT = 1;\nconst STACK_ARRAY = 2;\nconst decoder = new TextDecoder();\n\nfunction adjustPosition(error, parser) {\n    if (error.name === 'SyntaxError' && parser.jsonParseOffset) {\n        error.message = error.message.replace(/at position (\\d+)/, (_, pos) =>\n            'at position ' + (Number(pos) + parser.jsonParseOffset)\n        );\n    }\n\n    return error;\n}\n\nfunction append(array, elements) {\n    // Note: Avoid to use array.push(...elements) since it may lead to\n    // \"RangeError: Maximum call stack size exceeded\" for a long arrays\n    const initialLength = array.length;\n    array.length += elements.length;\n\n    for (let i = 0; i < elements.length; i++) {\n        array[initialLength + i] = elements[i];\n    }\n}\n\nexport async function parseChunked(chunkEmitter) {\n    const iterable = typeof chunkEmitter === 'function'\n        ? chunkEmitter()\n        : chunkEmitter;\n\n    if (isIterable(iterable)) {\n        let parser = new ChunkParser();\n\n        try {\n            for await (const chunk of iterable) {\n                if (typeof chunk !== 'string' && !ArrayBuffer.isView(chunk)) {\n                    throw new TypeError('Invalid chunk: Expected string, TypedArray or Buffer');\n                }\n\n                parser.push(chunk);\n            }\n\n            return parser.finish();\n        } catch (e) {\n            throw adjustPosition(e, parser);\n        }\n    }\n\n    throw new TypeError(\n        'Invalid chunk emitter: Expected an Iterable, AsyncIterable, generator, ' +\n        'async generator, or a function returning an Iterable or AsyncIterable'\n    );\n};\n\nclass ChunkParser {\n    constructor() {\n        this.value = undefined;\n        this.valueStack = null;\n\n        this.stack = new Array(100);\n        this.lastFlushDepth = 0;\n        this.flushDepth = 0;\n        this.stateString = false;\n        this.stateStringEscape = false;\n        this.pendingByteSeq = null;\n        this.pendingChunk = null;\n        this.chunkOffset = 0;\n        this.jsonParseOffset = 0;\n    }\n\n    parseAndAppend(fragment, wrap) {\n        // Append new entries or elements\n        if (this.stack[this.lastFlushDepth - 1] === STACK_OBJECT) {\n            if (wrap) {\n                this.jsonParseOffset--;\n                fragment = '{' + fragment + '}';\n            }\n\n            Object.assign(this.valueStack.value, JSON.parse(fragment));\n        } else {\n            if (wrap) {\n                this.jsonParseOffset--;\n                fragment = '[' + fragment + ']';\n            }\n\n            append(this.valueStack.value, JSON.parse(fragment));\n        }\n    }\n\n    prepareAddition(fragment) {\n        const { value } = this.valueStack;\n        const expectComma = Array.isArray(value)\n            ? value.length !== 0\n            : Object.keys(value).length !== 0;\n\n        if (expectComma) {\n            // Skip a comma at the beginning of fragment, otherwise it would\n            // fail to parse\n            if (fragment[0] === ',') {\n                this.jsonParseOffset++;\n                return fragment.slice(1);\n            }\n\n            // When value (an object or array) is not empty and a fragment\n            // doesn't start with a comma, a single valid fragment starting\n            // is a closing bracket. If it's not, a prefix is adding to fail\n            // parsing. Otherwise, the sequence of chunks can be successfully\n            // parsed, although it should not, e.g. [\"[{}\", \"{}]\"]\n            if (fragment[0] !== '}' && fragment[0] !== ']') {\n                this.jsonParseOffset -= 3;\n                return '[[]' + fragment;\n            }\n        }\n\n        return fragment;\n    }\n\n    flush(chunk, start, end) {\n        let fragment = chunk.slice(start, end);\n\n        // Save position correction an error in JSON.parse() if any\n        this.jsonParseOffset = this.chunkOffset + start;\n\n        // Prepend pending chunk if any\n        if (this.pendingChunk !== null) {\n            fragment = this.pendingChunk + fragment;\n            this.jsonParseOffset -= this.pendingChunk.length;\n            this.pendingChunk = null;\n        }\n\n        if (this.flushDepth === this.lastFlushDepth) {\n            // Depth didn't changed, so it's a root value or entry/element set\n            if (this.flushDepth > 0) {\n                this.parseAndAppend(this.prepareAddition(fragment), true);\n            } else {\n                // That's an entire value on a top level\n                this.value = JSON.parse(fragment);\n                this.valueStack = {\n                    value: this.value,\n                    prev: null\n                };\n            }\n        } else if (this.flushDepth > this.lastFlushDepth) {\n            // Add missed closing brackets/parentheses\n            for (let i = this.flushDepth - 1; i >= this.lastFlushDepth; i--) {\n                fragment += this.stack[i] === STACK_OBJECT ? '}' : ']';\n            }\n\n            if (this.lastFlushDepth === 0) {\n                // That's a root value\n                this.value = JSON.parse(fragment);\n                this.valueStack = {\n                    value: this.value,\n                    prev: null\n                };\n            } else {\n                this.parseAndAppend(this.prepareAddition(fragment), true);\n            }\n\n            // Move down to the depths to the last object/array, which is current now\n            for (let i = this.lastFlushDepth || 1; i < this.flushDepth; i++) {\n                let value = this.valueStack.value;\n\n                if (this.stack[i - 1] === STACK_OBJECT) {\n                    // find last entry\n                    let key;\n                    // eslint-disable-next-line curly\n                    for (key in value);\n                    value = value[key];\n                } else {\n                    // last element\n                    value = value[value.length - 1];\n                }\n\n                this.valueStack = {\n                    value,\n                    prev: this.valueStack\n                };\n            }\n        } else /* this.flushDepth < this.lastFlushDepth */ {\n            fragment = this.prepareAddition(fragment);\n\n            // Add missed opening brackets/parentheses\n            for (let i = this.lastFlushDepth - 1; i >= this.flushDepth; i--) {\n                this.jsonParseOffset--;\n                fragment = (this.stack[i] === STACK_OBJECT ? '{' : '[') + fragment;\n            }\n\n            this.parseAndAppend(fragment, false);\n\n            for (let i = this.lastFlushDepth - 1; i >= this.flushDepth; i--) {\n                this.valueStack = this.valueStack.prev;\n            }\n        }\n\n        this.lastFlushDepth = this.flushDepth;\n    }\n\n    push(chunk) {\n        if (typeof chunk !== 'string') {\n            // Suppose chunk is Buffer or Uint8Array\n\n            // Prepend uncompleted byte sequence if any\n            if (this.pendingByteSeq !== null) {\n                const origRawChunk = chunk;\n                chunk = new Uint8Array(this.pendingByteSeq.length + origRawChunk.length);\n                chunk.set(this.pendingByteSeq);\n                chunk.set(origRawChunk, this.pendingByteSeq.length);\n                this.pendingByteSeq = null;\n            }\n\n            // In case Buffer/Uint8Array, an input is encoded in UTF8\n            // Seek for parts of uncompleted UTF8 symbol on the ending\n            // This makes sense only if we expect more chunks and last char is not multi-bytes\n            if (chunk[chunk.length - 1] > 127) {\n                for (let seqLength = 0; seqLength < chunk.length; seqLength++) {\n                    const byte = chunk[chunk.length - 1 - seqLength];\n\n                    // 10xxxxxx - 2nd, 3rd or 4th byte\n                    // 110xxxxx – first byte of 2-byte sequence\n                    // 1110xxxx - first byte of 3-byte sequence\n                    // 11110xxx - first byte of 4-byte sequence\n                    if (byte >> 6 === 3) {\n                        seqLength++;\n\n                        // If the sequence is really incomplete, then preserve it\n                        // for the future chunk and cut off it from the current chunk\n                        if ((seqLength !== 4 && byte >> 3 === 0b11110) ||\n                            (seqLength !== 3 && byte >> 4 === 0b1110) ||\n                            (seqLength !== 2 && byte >> 5 === 0b110)) {\n                            this.pendingByteSeq = chunk.slice(chunk.length - seqLength);\n                            chunk = chunk.slice(0, -seqLength);\n                        }\n\n                        break;\n                    }\n                }\n            }\n\n            // Convert chunk to a string, since single decode per chunk\n            // is much effective than decode multiple small substrings\n            chunk = decoder.decode(chunk);\n        }\n\n        const chunkLength = chunk.length;\n        let lastFlushPoint = 0;\n        let flushPoint = 0;\n\n        // Main scan loop\n        scan: for (let i = 0; i < chunkLength; i++) {\n            if (this.stateString) {\n                for (; i < chunkLength; i++) {\n                    if (this.stateStringEscape) {\n                        this.stateStringEscape = false;\n                    } else {\n                        switch (chunk.charCodeAt(i)) {\n                            case 0x22: /* \" */\n                                this.stateString = false;\n                                continue scan;\n\n                            case 0x5C: /* \\ */\n                                this.stateStringEscape = true;\n                        }\n                    }\n                }\n\n                break;\n            }\n\n            switch (chunk.charCodeAt(i)) {\n                case 0x22: /* \" */\n                    this.stateString = true;\n                    this.stateStringEscape = false;\n                    break;\n\n                case 0x2C: /* , */\n                    flushPoint = i;\n                    break;\n\n                case 0x7B: /* { */\n                    // Open an object\n                    flushPoint = i + 1;\n                    this.stack[this.flushDepth++] = STACK_OBJECT;\n                    break;\n\n                case 0x5B: /* [ */\n                    // Open an array\n                    flushPoint = i + 1;\n                    this.stack[this.flushDepth++] = STACK_ARRAY;\n                    break;\n\n                case 0x5D: /* ] */\n                case 0x7D: /* } */\n                    // Close an object or array\n                    flushPoint = i + 1;\n                    this.flushDepth--;\n\n                    if (this.flushDepth < this.lastFlushDepth) {\n                        this.flush(chunk, lastFlushPoint, flushPoint);\n                        lastFlushPoint = flushPoint;\n                    }\n\n                    break;\n\n                case 0x09: /* \\t */\n                case 0x0A: /* \\n */\n                case 0x0D: /* \\r */\n                case 0x20: /* space */\n                    // Move points forward when they points on current position and it's a whitespace\n                    if (lastFlushPoint === i) {\n                        lastFlushPoint++;\n                    }\n\n                    if (flushPoint === i) {\n                        flushPoint++;\n                    }\n\n                    break;\n            }\n        }\n\n        if (flushPoint > lastFlushPoint) {\n            this.flush(chunk, lastFlushPoint, flushPoint);\n        }\n\n        // Produce pendingChunk if something left\n        if (flushPoint < chunkLength) {\n            if (this.pendingChunk !== null) {\n                // When there is already a pending chunk then no flush happened,\n                // appending entire chunk to pending one\n                this.pendingChunk += chunk;\n            } else {\n                // Create a pending chunk, it will start with non-whitespace since\n                // flushPoint was moved forward away from whitespaces on scan\n                this.pendingChunk = chunk.slice(flushPoint, chunkLength);\n            }\n        }\n\n        this.chunkOffset += chunkLength;\n    }\n\n    finish() {\n        if (this.pendingChunk !== null) {\n            this.flush('', 0, 0);\n            this.pendingChunk = null;\n        }\n\n        return this.value;\n    }\n};\n", "import { normalizeStringifyOptions, replaceValue } from './utils.js';\n\nfunction encodeString(value) {\n    if (/[^\\x20\\x21\\x23-\\x5B\\x5D-\\uD799]/.test(value)) { // [^\\x20-\\uD799]|[\\x22\\x5c]\n        return JSON.stringify(value);\n    }\n\n    return '\"' + value + '\"';\n}\n\nexport function* stringifyChunked(value, ...args) {\n    const { replacer, getKeys, space, ...options } = normalizeStringifyOptions(...args);\n    const highWaterMark = Number(options.highWaterMark) || 0x4000; // 16kb by default\n\n    const keyStrings = new Map();\n    const stack = [];\n    const rootValue = { '': value };\n    let prevState = null;\n    let state = () => printEntry('', value);\n    let stateValue = rootValue;\n    let stateEmpty = true;\n    let stateKeys = [''];\n    let stateIndex = 0;\n    let buffer = '';\n\n    while (true) {\n        state();\n\n        if (buffer.length >= highWaterMark || prevState === null) {\n            // flush buffer\n            yield buffer;\n            buffer = '';\n\n            if (prevState === null) {\n                break;\n            }\n        }\n    }\n\n    function printObject() {\n        if (stateIndex === 0) {\n            stateKeys = getKeys(stateValue);\n            buffer += '{';\n        }\n\n        // when no keys left\n        if (stateIndex === stateKeys.length) {\n            buffer += space && !stateEmpty\n                ? `\\n${space.repeat(stack.length - 1)}}`\n                : '}';\n\n            popState();\n            return;\n        }\n\n        const key = stateKeys[stateIndex++];\n        printEntry(key, stateValue[key]);\n    }\n\n    function printArray() {\n        if (stateIndex === 0) {\n            buffer += '[';\n        }\n\n        if (stateIndex === stateValue.length) {\n            buffer += space && !stateEmpty\n                ? `\\n${space.repeat(stack.length - 1)}]`\n                : ']';\n\n            popState();\n            return;\n        }\n\n        printEntry(stateIndex, stateValue[stateIndex++]);\n    }\n\n    function printEntryPrelude(key) {\n        if (stateEmpty) {\n            stateEmpty = false;\n        } else {\n            buffer += ',';\n        }\n\n        if (space && prevState !== null) {\n            buffer += `\\n${space.repeat(stack.length)}`;\n        }\n\n        if (state === printObject) {\n            let keyString = keyStrings.get(key);\n\n            if (keyString === undefined) {\n                keyStrings.set(key, keyString = encodeString(key) + (space ? ': ' : ':'));\n            }\n\n            buffer += keyString;\n        }\n    }\n\n    function printEntry(key, value) {\n        value = replaceValue(stateValue, key, value, replacer);\n\n        if (value === null || typeof value !== 'object') {\n            // primitive\n            if (state !== printObject || value !== undefined) {\n                printEntryPrelude(key);\n                pushPrimitive(value);\n            }\n        } else {\n            // If the visited set does not change after adding a value, then it is already in the set\n            if (stack.includes(value)) {\n                throw new TypeError('Converting circular structure to JSON');\n            }\n\n            printEntryPrelude(key);\n            stack.push(value);\n\n            pushState();\n            state = Array.isArray(value) ? printArray : printObject;\n            stateValue = value;\n            stateEmpty = true;\n            stateIndex = 0;\n        }\n    }\n\n    function pushPrimitive(value) {\n        switch (typeof value) {\n            case 'string':\n                buffer += encodeString(value);\n                break;\n\n            case 'number':\n                buffer += Number.isFinite(value) ? String(value) : 'null';\n                break;\n\n            case 'boolean':\n                buffer += value ? 'true' : 'false';\n                break;\n\n            case 'undefined':\n            case 'object': // typeof null === 'object'\n                buffer += 'null';\n                break;\n\n            default:\n                throw new TypeError(`Do not know how to serialize a ${value.constructor?.name || typeof value}`);\n        }\n    }\n\n    function pushState() {\n        prevState = {\n            keys: stateKeys,\n            index: stateIndex,\n            prev: prevState\n        };\n    }\n\n    function popState() {\n        stack.pop();\n        const value = stack.length > 0 ? stack[stack.length - 1] : rootValue;\n\n        // restore state\n        state = Array.isArray(value) ? printArray : printObject;\n        stateValue = value;\n        stateEmpty = false;\n        stateKeys = prevState.keys;\n        stateIndex = prevState.index;\n\n        // pop state\n        prevState = prevState.prev;\n    }\n};\n", "import { normalizeStringifyOptions, replaceValue } from './utils.js';\n\nconst hasOwn = typeof Object.hasOwn === 'function'\n    ? Object.hasOwn\n    : (object, key) => Object.hasOwnProperty.call(object, key);\n\n// https://tc39.es/ecma262/#table-json-single-character-escapes\nconst escapableCharCodeSubstitution = { // JSON Single Character Escape Sequences\n    0x08: '\\\\b',\n    0x09: '\\\\t',\n    0x0a: '\\\\n',\n    0x0c: '\\\\f',\n    0x0d: '\\\\r',\n    0x22: '\\\\\\\"',\n    0x5c: '\\\\\\\\'\n};\n\nconst charLength2048 = Uint8Array.from({ length: 2048 }, (_, code) => {\n    if (hasOwn(escapableCharCodeSubstitution, code)) {\n        return 2; // \\X\n    }\n\n    if (code < 0x20) {\n        return 6; // \\uXXXX\n    }\n\n    return code < 128 ? 1 : 2; // UTF8 bytes\n});\n\nfunction isLeadingSurrogate(code) {\n    return code >= 0xD800 && code <= 0xDBFF;\n}\n\nfunction isTrailingSurrogate(code) {\n    return code >= 0xDC00 && code <= 0xDFFF;\n}\n\nfunction stringLength(str) {\n    // Fast path to compute length when a string contains only characters encoded as single bytes\n    if (!/[^\\x20\\x21\\x23-\\x5B\\x5D-\\x7F]/.test(str)) {\n        return str.length + 2;\n    }\n\n    let len = 0;\n    let prevLeadingSurrogate = false;\n\n    for (let i = 0; i < str.length; i++) {\n        const code = str.charCodeAt(i);\n\n        if (code < 2048) {\n            len += charLength2048[code];\n        } else if (isLeadingSurrogate(code)) {\n            len += 6; // \\uXXXX since no pair with trailing surrogate yet\n            prevLeadingSurrogate = true;\n            continue;\n        } else if (isTrailingSurrogate(code)) {\n            len = prevLeadingSurrogate\n                ? len - 2  // surrogate pair (4 bytes), since we calculate prev leading surrogate as 6 bytes, substruct 2 bytes\n                : len + 6; // \\uXXXX\n        } else {\n            len += 3; // code >= 2048 is 3 bytes length for UTF8\n        }\n\n        prevLeadingSurrogate = false;\n    }\n\n    return len + 2; // +2 for quotes\n}\n\n// avoid producing a string from a number\nfunction intLength(num) {\n    let len = 0;\n\n    if (num < 0) {\n        len = 1;\n        num = -num;\n    }\n\n    if (num >= 1e9) {\n        len += 9;\n        num = (num - num % 1e9) / 1e9;\n    }\n\n    if (num >= 1e4) {\n        if (num >= 1e6) {\n            return len + (num >= 1e8\n                ? 9\n                : num >= 1e7 ? 8 : 7\n            );\n        }\n        return len + (num >= 1e5 ? 6 : 5);\n    }\n\n    return len + (num >= 1e2\n        ? num >= 1e3 ? 4 : 3\n        : num >= 10 ? 2 : 1\n    );\n};\n\nfunction primitiveLength(value) {\n    switch (typeof value) {\n        case 'string':\n            return stringLength(value);\n\n        case 'number':\n            return Number.isFinite(value)\n                ? Number.isInteger(value)\n                    ? intLength(value)\n                    : String(value).length\n                : 4 /* null */;\n\n        case 'boolean':\n            return value ? 4 /* true */ : 5 /* false */;\n\n        case 'undefined':\n        case 'object':\n            return 4; /* null */\n\n        default:\n            return 0;\n    }\n}\n\nexport function stringifyInfo(value, ...args) {\n    const { replacer, getKeys, ...options } = normalizeStringifyOptions(...args);\n    const continueOnCircular = Boolean(options.continueOnCircular);\n    const space = options.space?.length || 0;\n\n    const keysLength = new Map();\n    const visited = new Map();\n    const circular = new Set();\n    const stack = [];\n    const root = { '': value };\n    let stop = false;\n    let bytes = 0;\n    let spaceBytes = 0;\n    let objects = 0;\n\n    walk(root, '', value);\n\n    // when value is undefined or replaced for undefined\n    if (bytes === 0) {\n        bytes += 9; // FIXME: that's the length of undefined, should we normalize behaviour to convert it to null?\n    }\n\n    return {\n        bytes: isNaN(bytes) ? Infinity : bytes + spaceBytes,\n        spaceBytes: space > 0 && isNaN(bytes) ? Infinity : spaceBytes,\n        circular: [...circular]\n    };\n\n    function walk(holder, key, value) {\n        if (stop) {\n            return;\n        }\n\n        value = replaceValue(holder, key, value, replacer);\n\n        if (value === null || typeof value !== 'object') {\n            // primitive\n            if (value !== undefined || Array.isArray(holder)) {\n                bytes += primitiveLength(value);\n            }\n        } else {\n            // check for circular references\n            if (stack.includes(value)) {\n                circular.add(value);\n                bytes += 4; // treat as null\n\n                if (!continueOnCircular) {\n                    stop = true;\n                }\n\n                return;\n            }\n\n            // Using 'visited' allows avoiding hang-ups in cases of highly interconnected object graphs;\n            // for example, a list of git commits with references to parents can lead to N^2 complexity for traversal,\n            // and N when 'visited' is used\n            if (visited.has(value)) {\n                bytes += visited.get(value);\n\n                return;\n            }\n\n            objects++;\n\n            const prevObjects = objects;\n            const valueBytes = bytes;\n            let valueLength = 0;\n\n            stack.push(value);\n\n            if (Array.isArray(value)) {\n                // array\n                valueLength = value.length;\n\n                for (let i = 0; i < valueLength; i++) {\n                    walk(value, i, value[i]);\n                }\n            } else {\n                // object\n                let prevLength = bytes;\n\n                for (const key of getKeys(value)) {\n                    walk(value, key, value[key]);\n\n                    if (prevLength !== bytes) {\n                        let keyLen = keysLength.get(key);\n\n                        if (keyLen === undefined) {\n                            keysLength.set(key, keyLen = stringLength(key) + 1); // \"key\":\n                        }\n\n                        // value is printed\n                        bytes += keyLen;\n                        valueLength++;\n                        prevLength = bytes;\n                    }\n                }\n            }\n\n            bytes += valueLength === 0\n                ? 2 // {} or []\n                : 1 + valueLength; // {} or [] + commas\n\n            if (space > 0 && valueLength > 0) {\n                spaceBytes +=\n                    // a space between \":\" and a value for each object entry\n                    (Array.isArray(value) ? 0 : valueLength) +\n                    // the formula results from folding the following components:\n                    // - for each key-value or element: ident + newline\n                    //   (1 + stack.length * space) * valueLength\n                    // - ident (one space less) before \"}\" or \"]\" + newline\n                    //   (stack.length - 1) * space + 1\n                    (1 + stack.length * space) * (valueLength + 1) - space;\n            }\n\n            stack.pop();\n\n            // add to 'visited' only objects that contain nested objects\n            if (prevObjects !== objects) {\n                visited.set(value, bytes - valueBytes);\n            }\n        }\n    }\n};\n", "/* eslint-env browser */\nimport { parseChunked } from './parse-chunked.js';\nimport { stringifyChunked } from './stringify-chunked.js';\nimport { isIterable } from './utils.js';\n\nexport function parseFromWebStream(stream) {\n    // 2024/6/17: currently, an @@asyncIterator on a ReadableStream is not widely supported,\n    // therefore use a fallback using a reader\n    // https://caniuse.com/mdn-api_readablestream_--asynciterator\n    return parseChunked(isIterable(stream) ? stream : async function*() {\n        const reader = stream.getReader();\n\n        try {\n            while (true) {\n                const { value, done } = await reader.read();\n\n                if (done) {\n                    break;\n                }\n\n                yield value;\n            }\n        } finally {\n            reader.releaseLock();\n        }\n    });\n}\n\nexport function createStringifyWebStream(value, replacer, space) {\n    // 2024/6/17: the ReadableStream.from() static method is supported\n    // in Node.js 20.6+ and Firefox only\n    if (typeof ReadableStream.from === 'function') {\n        return ReadableStream.from(stringifyChunked(value, replacer, space));\n    }\n\n    // emulate ReadableStream.from()\n    return new ReadableStream({\n        start() {\n            this.generator = stringifyChunked(value, replacer, space);\n        },\n        pull(controller) {\n            const { value, done } = this.generator.next();\n\n            if (done) {\n                controller.close();\n            } else {\n                controller.enqueue(value);\n            }\n        },\n        cancel() {\n            this.generator = null;\n        }\n    });\n};\n"], "mappings": ";;;;;8aAAA,IAAAA,GAAA,GAAAC,EAAAD,GAAA,8BAAAE,EAAA,iBAAAC,EAAA,uBAAAC,EAAA,qBAAAC,EAAA,kBAAAC,ICAO,SAASC,EAAWC,EAAO,CAC9B,OACI,OAAOA,GAAU,UACjBA,IAAU,OAEN,OAAOA,EAAM,OAAO,QAAQ,GAAM,YAClC,OAAOA,EAAM,OAAO,aAAa,GAAM,WAGnD,CAEO,SAASC,EAAaC,EAAQC,EAAKH,EAAOI,EAAU,CASvD,OARIJ,GAAS,OAAOA,EAAM,QAAW,aACjCA,EAAQA,EAAM,OAAO,GAGrBI,IAAa,OACbJ,EAAQI,EAAS,KAAKF,EAAQ,OAAOC,CAAG,EAAGH,CAAK,GAG5C,OAAOA,EAAO,CAClB,IAAK,WACL,IAAK,SACDA,EAAQ,OACR,MAEJ,IAAK,SACD,GAAIA,IAAU,KAAM,CAChB,IAAMK,EAAML,EAAM,aACdK,IAAQ,QAAUA,IAAQ,QAAUA,IAAQ,WAC5CL,EAAQA,EAAM,QAAQ,EAE9B,CACA,KACR,CAEA,OAAOA,CACX,CAEO,SAASM,EAAkBF,EAAU,CACxC,OAAI,OAAOA,GAAa,WACbA,EAGP,MAAM,QAAQA,CAAQ,EASf,CAAC,GARU,IAAI,IAAIA,EACrB,IAAIG,GAAQ,CACT,IAAMF,EAAME,GAAQA,EAAK,YACzB,OAAOF,IAAQ,QAAUA,IAAQ,OAAS,OAAOE,CAAI,EAAI,IAC7D,CAAC,EACA,OAAOA,GAAQ,OAAOA,GAAS,QAAQ,CAC5C,CAEoB,EAGjB,IACX,CAEO,SAASC,EAAeC,EAAO,CAClC,OAAI,OAAOA,GAAU,SACb,CAAC,OAAO,SAASA,CAAK,GAAKA,EAAQ,EAC5B,GAGJ,IAAI,OAAO,KAAK,IAAIA,EAAO,EAAE,CAAC,EAGrC,OAAOA,GAAU,UACVA,EAAM,MAAM,EAAG,EAAE,GAAK,EAIrC,CAEO,SAASC,EAA0BC,EAAmBF,EAAO,EAC5DE,IAAsB,MAAQ,MAAM,QAAQA,CAAiB,GAAK,OAAOA,GAAsB,YAC/FA,EAAoB,CAChB,SAAUA,EACV,MAAAF,CACJ,GAGJ,IAAIL,EAAWE,EAAkBK,EAAkB,QAAQ,EACvDC,EAAU,OAAO,KAErB,GAAI,MAAM,QAAQR,CAAQ,EAAG,CACzB,IAAMS,EAAYT,EAElBQ,EAAU,IAAMC,EAChBT,EAAW,IACf,CAEA,MAAO,CACH,GAAGO,EACH,SAAAP,EACA,QAAAQ,EACA,MAAOJ,EAAeG,EAAkB,KAAK,CACjD,CACJ,CCjGA,IAAMG,EAAe,EACfC,EAAc,EACdC,EAAU,IAAI,YAEpB,SAASC,EAAeC,EAAOC,EAAQ,CACnC,OAAID,EAAM,OAAS,eAAiBC,EAAO,kBACvCD,EAAM,QAAUA,EAAM,QAAQ,QAAQ,oBAAqB,CAACE,EAAGC,IAC3D,gBAAkB,OAAOA,CAAG,EAAIF,EAAO,gBAC3C,GAGGD,CACX,CAEA,SAASI,EAAOC,EAAOC,EAAU,CAG7B,IAAMC,EAAgBF,EAAM,OAC5BA,EAAM,QAAUC,EAAS,OAEzB,QAASE,EAAI,EAAGA,EAAIF,EAAS,OAAQE,IACjCH,EAAME,EAAgBC,CAAC,EAAIF,EAASE,CAAC,CAE7C,CAEA,eAAsBC,EAAaC,EAAc,CAC7C,IAAMC,EAAW,OAAOD,GAAiB,WACnCA,EAAa,EACbA,EAEN,GAAIE,EAAWD,CAAQ,EAAG,CACtB,IAAIV,EAAS,IAAIY,EAEjB,GAAI,CACA,cAAiBC,KAASH,EAAU,CAChC,GAAI,OAAOG,GAAU,UAAY,CAAC,YAAY,OAAOA,CAAK,EACtD,MAAM,IAAI,UAAU,sDAAsD,EAG9Eb,EAAO,KAAKa,CAAK,CACrB,CAEA,OAAOb,EAAO,OAAO,CACzB,OAASc,EAAG,CACR,MAAMhB,EAAegB,EAAGd,CAAM,CAClC,CACJ,CAEA,MAAM,IAAI,UACN,8IAEJ,CACJ,CAEA,IAAMY,EAAN,KAAkB,CACd,aAAc,CACV,KAAK,MAAQ,OACb,KAAK,WAAa,KAElB,KAAK,MAAQ,IAAI,MAAM,GAAG,EAC1B,KAAK,eAAiB,EACtB,KAAK,WAAa,EAClB,KAAK,YAAc,GACnB,KAAK,kBAAoB,GACzB,KAAK,eAAiB,KACtB,KAAK,aAAe,KACpB,KAAK,YAAc,EACnB,KAAK,gBAAkB,CAC3B,CAEA,eAAeG,EAAUC,EAAM,CAEvB,KAAK,MAAM,KAAK,eAAiB,CAAC,IAAMrB,GACpCqB,IACA,KAAK,kBACLD,EAAW,IAAMA,EAAW,KAGhC,OAAO,OAAO,KAAK,WAAW,MAAO,KAAK,MAAMA,CAAQ,CAAC,IAErDC,IACA,KAAK,kBACLD,EAAW,IAAMA,EAAW,KAGhCZ,EAAO,KAAK,WAAW,MAAO,KAAK,MAAMY,CAAQ,CAAC,EAE1D,CAEA,gBAAgBA,EAAU,CACtB,GAAM,CAAE,MAAAE,CAAM,EAAI,KAAK,WAKvB,GAJoB,MAAM,QAAQA,CAAK,EACjCA,EAAM,SAAW,EACjB,OAAO,KAAKA,CAAK,EAAE,SAAW,EAEnB,CAGb,GAAIF,EAAS,CAAC,IAAM,IAChB,YAAK,kBACEA,EAAS,MAAM,CAAC,EAQ3B,GAAIA,EAAS,CAAC,IAAM,KAAOA,EAAS,CAAC,IAAM,IACvC,YAAK,iBAAmB,EACjB,MAAQA,CAEvB,CAEA,OAAOA,CACX,CAEA,MAAMF,EAAOK,EAAOC,EAAK,CACrB,IAAIJ,EAAWF,EAAM,MAAMK,EAAOC,CAAG,EAYrC,GATA,KAAK,gBAAkB,KAAK,YAAcD,EAGtC,KAAK,eAAiB,OACtBH,EAAW,KAAK,aAAeA,EAC/B,KAAK,iBAAmB,KAAK,aAAa,OAC1C,KAAK,aAAe,MAGpB,KAAK,aAAe,KAAK,eAErB,KAAK,WAAa,EAClB,KAAK,eAAe,KAAK,gBAAgBA,CAAQ,EAAG,EAAI,GAGxD,KAAK,MAAQ,KAAK,MAAMA,CAAQ,EAChC,KAAK,WAAa,CACd,MAAO,KAAK,MACZ,KAAM,IACV,WAEG,KAAK,WAAa,KAAK,eAAgB,CAE9C,QAASR,EAAI,KAAK,WAAa,EAAGA,GAAK,KAAK,eAAgBA,IACxDQ,GAAY,KAAK,MAAMR,CAAC,IAAMZ,EAAe,IAAM,IAGnD,KAAK,iBAAmB,GAExB,KAAK,MAAQ,KAAK,MAAMoB,CAAQ,EAChC,KAAK,WAAa,CACd,MAAO,KAAK,MACZ,KAAM,IACV,GAEA,KAAK,eAAe,KAAK,gBAAgBA,CAAQ,EAAG,EAAI,EAI5D,QAASR,EAAI,KAAK,gBAAkB,EAAGA,EAAI,KAAK,WAAYA,IAAK,CAC7D,IAAIU,EAAQ,KAAK,WAAW,MAE5B,GAAI,KAAK,MAAMV,EAAI,CAAC,IAAMZ,EAAc,CAEpC,IAAIyB,EAEJ,IAAKA,KAAOH,EAAM,CAClBA,EAAQA,EAAMG,CAAG,CACrB,MAEIH,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAGlC,KAAK,WAAa,CACd,MAAAA,EACA,KAAM,KAAK,UACf,CACJ,CACJ,KAAmD,CAC/CF,EAAW,KAAK,gBAAgBA,CAAQ,EAGxC,QAASR,EAAI,KAAK,eAAiB,EAAGA,GAAK,KAAK,WAAYA,IACxD,KAAK,kBACLQ,GAAY,KAAK,MAAMR,CAAC,IAAMZ,EAAe,IAAM,KAAOoB,EAG9D,KAAK,eAAeA,EAAU,EAAK,EAEnC,QAASR,EAAI,KAAK,eAAiB,EAAGA,GAAK,KAAK,WAAYA,IACxD,KAAK,WAAa,KAAK,WAAW,IAE1C,CAEA,KAAK,eAAiB,KAAK,UAC/B,CAEA,KAAKM,EAAO,CACR,GAAI,OAAOA,GAAU,SAAU,CAI3B,GAAI,KAAK,iBAAmB,KAAM,CAC9B,IAAMQ,EAAeR,EACrBA,EAAQ,IAAI,WAAW,KAAK,eAAe,OAASQ,EAAa,MAAM,EACvER,EAAM,IAAI,KAAK,cAAc,EAC7BA,EAAM,IAAIQ,EAAc,KAAK,eAAe,MAAM,EAClD,KAAK,eAAiB,IAC1B,CAKA,GAAIR,EAAMA,EAAM,OAAS,CAAC,EAAI,IAC1B,QAASS,EAAY,EAAGA,EAAYT,EAAM,OAAQS,IAAa,CAC3D,IAAMC,EAAOV,EAAMA,EAAM,OAAS,EAAIS,CAAS,EAM/C,GAAIC,GAAQ,IAAM,EAAG,CACjBD,KAIKA,IAAc,GAAKC,GAAQ,IAAM,IACjCD,IAAc,GAAKC,GAAQ,IAAM,IACjCD,IAAc,GAAKC,GAAQ,IAAM,KAClC,KAAK,eAAiBV,EAAM,MAAMA,EAAM,OAASS,CAAS,EAC1DT,EAAQA,EAAM,MAAM,EAAG,CAACS,CAAS,GAGrC,KACJ,CACJ,CAKJT,EAAQhB,EAAQ,OAAOgB,CAAK,CAChC,CAEA,IAAMW,EAAcX,EAAM,OACtBY,EAAiB,EACjBC,EAAa,EAGjBC,EAAM,QAASpB,EAAI,EAAGA,EAAIiB,EAAajB,IAAK,CACxC,GAAI,KAAK,YAAa,CAClB,KAAOA,EAAIiB,EAAajB,IACpB,GAAI,KAAK,kBACL,KAAK,kBAAoB,OAEzB,QAAQM,EAAM,WAAWN,CAAC,EAAG,CACzB,IAAK,IACD,KAAK,YAAc,GACnB,SAASoB,EAEb,IAAK,IACD,KAAK,kBAAoB,EACjC,CAIR,KACJ,CAEA,OAAQd,EAAM,WAAWN,CAAC,EAAG,CACzB,IAAK,IACD,KAAK,YAAc,GACnB,KAAK,kBAAoB,GACzB,MAEJ,IAAK,IACDmB,EAAanB,EACb,MAEJ,IAAK,KAEDmB,EAAanB,EAAI,EACjB,KAAK,MAAM,KAAK,YAAY,EAAIZ,EAChC,MAEJ,IAAK,IAED+B,EAAanB,EAAI,EACjB,KAAK,MAAM,KAAK,YAAY,EAAIX,EAChC,MAEJ,IAAK,IACL,IAAK,KAED8B,EAAanB,EAAI,EACjB,KAAK,aAED,KAAK,WAAa,KAAK,iBACvB,KAAK,MAAMM,EAAOY,EAAgBC,CAAU,EAC5CD,EAAiBC,GAGrB,MAEJ,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IAEGD,IAAmBlB,GACnBkB,IAGAC,IAAenB,GACfmB,IAGJ,KACR,CACJ,CAEIA,EAAaD,GACb,KAAK,MAAMZ,EAAOY,EAAgBC,CAAU,EAI5CA,EAAaF,IACT,KAAK,eAAiB,KAGtB,KAAK,cAAgBX,EAIrB,KAAK,aAAeA,EAAM,MAAMa,EAAYF,CAAW,GAI/D,KAAK,aAAeA,CACxB,CAEA,QAAS,CACL,OAAI,KAAK,eAAiB,OACtB,KAAK,MAAM,GAAI,EAAG,CAAC,EACnB,KAAK,aAAe,MAGjB,KAAK,KAChB,CACJ,EC7VA,SAASI,EAAaC,EAAO,CACzB,MAAI,kCAAkC,KAAKA,CAAK,EACrC,KAAK,UAAUA,CAAK,EAGxB,IAAMA,EAAQ,GACzB,CAEO,SAAUC,EAAiBD,KAAUE,EAAM,CAC9C,GAAM,CAAE,SAAAC,EAAU,QAAAC,EAAS,MAAAC,EAAO,GAAGC,CAAQ,EAAIC,EAA0B,GAAGL,CAAI,EAC5EM,EAAgB,OAAOF,EAAQ,aAAa,GAAK,MAEjDG,EAAa,IAAI,IACjBC,EAAQ,CAAC,EACTC,EAAY,CAAE,GAAIX,CAAM,EAC1BY,EAAY,KACZC,EAAQ,IAAMC,EAAW,GAAId,CAAK,EAClCe,EAAaJ,EACbK,EAAa,GACbC,EAAY,CAAC,EAAE,EACfC,EAAa,EACbC,EAAS,GAEb,KACIN,EAAM,EAEF,GAAAM,EAAO,QAAUX,GAAiBI,IAAc,QAEhD,MAAMO,EACNA,EAAS,GAELP,IAAc,QALtB,CAWJ,SAASQ,GAAc,CAOnB,GANIF,IAAe,IACfD,EAAYb,EAAQW,CAAU,EAC9BI,GAAU,KAIVD,IAAeD,EAAU,OAAQ,CACjCE,GAAUd,GAAS,CAACW,EACd;AAAA,EAAKX,EAAM,OAAOK,EAAM,OAAS,CAAC,CAAC,IACnC,IAENW,EAAS,EACT,MACJ,CAEA,IAAMC,EAAML,EAAUC,GAAY,EAClCJ,EAAWQ,EAAKP,EAAWO,CAAG,CAAC,CACnC,CAEA,SAASC,GAAa,CAKlB,GAJIL,IAAe,IACfC,GAAU,KAGVD,IAAeH,EAAW,OAAQ,CAClCI,GAAUd,GAAS,CAACW,EACd;AAAA,EAAKX,EAAM,OAAOK,EAAM,OAAS,CAAC,CAAC,IACnC,IAENW,EAAS,EACT,MACJ,CAEAP,EAAWI,EAAYH,EAAWG,GAAY,CAAC,CACnD,CAEA,SAASM,EAAkBF,EAAK,CAW5B,GAVIN,EACAA,EAAa,GAEbG,GAAU,IAGVd,GAASO,IAAc,OACvBO,GAAU;AAAA,EAAKd,EAAM,OAAOK,EAAM,MAAM,CAAC,IAGzCG,IAAUO,EAAa,CACvB,IAAIK,EAAYhB,EAAW,IAAIa,CAAG,EAE9BG,IAAc,QACdhB,EAAW,IAAIa,EAAKG,EAAY1B,EAAauB,CAAG,GAAKjB,EAAQ,KAAO,IAAI,EAG5Ec,GAAUM,CACd,CACJ,CAEA,SAASX,EAAWQ,EAAKtB,EAAO,CAG5B,GAFAA,EAAQ0B,EAAaX,EAAYO,EAAKtB,EAAOG,CAAQ,EAEjDH,IAAU,MAAQ,OAAOA,GAAU,UAE/Ba,IAAUO,GAAepB,IAAU,UACnCwB,EAAkBF,CAAG,EACrBK,EAAc3B,CAAK,OAEpB,CAEH,GAAIU,EAAM,SAASV,CAAK,EACpB,MAAM,IAAI,UAAU,uCAAuC,EAG/DwB,EAAkBF,CAAG,EACrBZ,EAAM,KAAKV,CAAK,EAEhB4B,EAAU,EACVf,EAAQ,MAAM,QAAQb,CAAK,EAAIuB,EAAaH,EAC5CL,EAAaf,EACbgB,EAAa,GACbE,EAAa,CACjB,CACJ,CAEA,SAASS,EAAc3B,EAAO,CAC1B,OAAQ,OAAOA,EAAO,CAClB,IAAK,SACDmB,GAAUpB,EAAaC,CAAK,EAC5B,MAEJ,IAAK,SACDmB,GAAU,OAAO,SAASnB,CAAK,EAAI,OAAOA,CAAK,EAAI,OACnD,MAEJ,IAAK,UACDmB,GAAUnB,EAAQ,OAAS,QAC3B,MAEJ,IAAK,YACL,IAAK,SACDmB,GAAU,OACV,MAEJ,QACI,MAAM,IAAI,UAAU,kCAAkCnB,EAAM,aAAa,MAAQ,OAAOA,CAAK,EAAE,CACvG,CACJ,CAEA,SAAS4B,GAAY,CACjBhB,EAAY,CACR,KAAMK,EACN,MAAOC,EACP,KAAMN,CACV,CACJ,CAEA,SAASS,GAAW,CAChBX,EAAM,IAAI,EACV,IAAMV,EAAQU,EAAM,OAAS,EAAIA,EAAMA,EAAM,OAAS,CAAC,EAAIC,EAG3DE,EAAQ,MAAM,QAAQb,CAAK,EAAIuB,EAAaH,EAC5CL,EAAaf,EACbgB,EAAa,GACbC,EAAYL,EAAU,KACtBM,EAAaN,EAAU,MAGvBA,EAAYA,EAAU,IAC1B,CACJ,CCxKA,IAAMiB,EAAS,OAAO,OAAO,QAAW,WAClC,OAAO,OACP,CAACC,EAAQC,IAAQ,OAAO,eAAe,KAAKD,EAAQC,CAAG,EAGvDC,EAAgC,CAClC,EAAM,MACN,EAAM,MACN,GAAM,MACN,GAAM,MACN,GAAM,MACN,GAAM,MACN,GAAM,MACV,EAEMC,EAAiB,WAAW,KAAK,CAAE,OAAQ,IAAK,EAAG,CAACC,EAAGC,IACrDN,EAAOG,EAA+BG,CAAI,EACnC,EAGPA,EAAO,GACA,EAGJA,EAAO,IAAM,EAAI,CAC3B,EAED,SAASC,GAAmBD,EAAM,CAC9B,OAAOA,GAAQ,OAAUA,GAAQ,KACrC,CAEA,SAASE,GAAoBF,EAAM,CAC/B,OAAOA,GAAQ,OAAUA,GAAQ,KACrC,CAEA,SAASG,EAAaC,EAAK,CAEvB,GAAI,CAAC,gCAAgC,KAAKA,CAAG,EACzC,OAAOA,EAAI,OAAS,EAGxB,IAAIC,EAAM,EACNC,EAAuB,GAE3B,QAASC,EAAI,EAAGA,EAAIH,EAAI,OAAQG,IAAK,CACjC,IAAMP,EAAOI,EAAI,WAAWG,CAAC,EAE7B,GAAIP,EAAO,KACPK,GAAOP,EAAeE,CAAI,UACnBC,GAAmBD,CAAI,EAAG,CACjCK,GAAO,EACPC,EAAuB,GACvB,QACJ,MAAWJ,GAAoBF,CAAI,EAC/BK,EAAMC,EACAD,EAAM,EACNA,EAAM,EAEZA,GAAO,EAGXC,EAAuB,EAC3B,CAEA,OAAOD,EAAM,CACjB,CAGA,SAASG,GAAUC,EAAK,CACpB,IAAIJ,EAAM,EAYV,OAVII,EAAM,IACNJ,EAAM,EACNI,EAAM,CAACA,GAGPA,GAAO,MACPJ,GAAO,EACPI,GAAOA,EAAMA,EAAM,KAAO,KAG1BA,GAAO,IACHA,GAAO,IACAJ,GAAOI,GAAO,IACf,EACAA,GAAO,IAAM,EAAI,GAGpBJ,GAAOI,GAAO,IAAM,EAAI,GAG5BJ,GAAOI,GAAO,IACfA,GAAO,IAAM,EAAI,EACjBA,GAAO,GAAK,EAAI,EAE1B,CAEA,SAASC,GAAgBC,EAAO,CAC5B,OAAQ,OAAOA,EAAO,CAClB,IAAK,SACD,OAAOR,EAAaQ,CAAK,EAE7B,IAAK,SACD,OAAO,OAAO,SAASA,CAAK,EACtB,OAAO,UAAUA,CAAK,EAClBH,GAAUG,CAAK,EACf,OAAOA,CAAK,EAAE,OAClB,EAEV,IAAK,UACD,OAAOA,EAAQ,EAAe,EAElC,IAAK,YACL,IAAK,SACD,MAAO,GAEX,QACI,MAAO,EACf,CACJ,CAEO,SAASC,EAAcD,KAAUE,EAAM,CAC1C,GAAM,CAAE,SAAAC,EAAU,QAAAC,EAAS,GAAGC,CAAQ,EAAIC,EAA0B,GAAGJ,CAAI,EACrEK,EAAqB,EAAQF,EAAQ,mBACrCG,EAAQH,EAAQ,OAAO,QAAU,EAEjCI,EAAa,IAAI,IACjBC,EAAU,IAAI,IACdC,EAAW,IAAI,IACfC,EAAQ,CAAC,EACTC,EAAO,CAAE,GAAIb,CAAM,EACrBc,EAAO,GACPC,EAAQ,EACRC,EAAa,EACbC,EAAU,EAEd,OAAAC,EAAKL,EAAM,GAAIb,CAAK,EAGhBe,IAAU,IACVA,GAAS,GAGN,CACH,MAAO,MAAMA,CAAK,EAAI,IAAWA,EAAQC,EACzC,WAAYR,EAAQ,GAAK,MAAMO,CAAK,EAAI,IAAWC,EACnD,SAAU,CAAC,GAAGL,CAAQ,CAC1B,EAEA,SAASO,EAAKC,EAAQlC,EAAKe,EAAO,CAC9B,GAAI,CAAAc,EAMJ,GAFAd,EAAQoB,EAAaD,EAAQlC,EAAKe,EAAOG,CAAQ,EAE7CH,IAAU,MAAQ,OAAOA,GAAU,UAE/BA,IAAU,QAAa,MAAM,QAAQmB,CAAM,KAC3CJ,GAAShB,GAAgBC,CAAK,OAE/B,CAEH,GAAIY,EAAM,SAASZ,CAAK,EAAG,CACvBW,EAAS,IAAIX,CAAK,EAClBe,GAAS,EAEJR,IACDO,EAAO,IAGX,MACJ,CAKA,GAAIJ,EAAQ,IAAIV,CAAK,EAAG,CACpBe,GAASL,EAAQ,IAAIV,CAAK,EAE1B,MACJ,CAEAiB,IAEA,IAAMI,EAAcJ,EACdK,EAAaP,EACfQ,EAAc,EAIlB,GAFAX,EAAM,KAAKZ,CAAK,EAEZ,MAAM,QAAQA,CAAK,EAAG,CAEtBuB,EAAcvB,EAAM,OAEpB,QAASJ,EAAI,EAAGA,EAAI2B,EAAa3B,IAC7BsB,EAAKlB,EAAOJ,EAAGI,EAAMJ,CAAC,CAAC,CAE/B,KAAO,CAEH,IAAI4B,EAAaT,EAEjB,QAAW9B,KAAOmB,EAAQJ,CAAK,EAG3B,GAFAkB,EAAKlB,EAAOf,EAAKe,EAAMf,CAAG,CAAC,EAEvBuC,IAAeT,EAAO,CACtB,IAAIU,EAAShB,EAAW,IAAIxB,CAAG,EAE3BwC,IAAW,QACXhB,EAAW,IAAIxB,EAAKwC,EAASjC,EAAaP,CAAG,EAAI,CAAC,EAItD8B,GAASU,EACTF,IACAC,EAAaT,CACjB,CAER,CAEAA,GAASQ,IAAgB,EACnB,EACA,EAAIA,EAENf,EAAQ,GAAKe,EAAc,IAC3BP,IAEK,MAAM,QAAQhB,CAAK,EAAI,EAAIuB,IAM3B,EAAIX,EAAM,OAASJ,IAAUe,EAAc,GAAKf,GAGzDI,EAAM,IAAI,EAGNS,IAAgBJ,GAChBP,EAAQ,IAAIV,EAAOe,EAAQO,CAAU,CAE7C,CACJ,CACJ,CCjPO,SAASI,EAAmBC,EAAQ,CAIvC,OAAOC,EAAaC,EAAWF,CAAM,EAAIA,EAAS,iBAAkB,CAChE,IAAMG,EAASH,EAAO,UAAU,EAEhC,GAAI,CACA,OAAa,CACT,GAAM,CAAE,MAAAI,EAAO,KAAAC,CAAK,EAAI,MAAMF,EAAO,KAAK,EAE1C,GAAIE,EACA,MAGJ,MAAMD,CACV,CACJ,QAAE,CACED,EAAO,YAAY,CACvB,CACJ,CAAC,CACL,CAEO,SAASG,EAAyBF,EAAOG,EAAUC,EAAO,CAG7D,OAAI,OAAO,eAAe,MAAS,WACxB,eAAe,KAAKC,EAAiBL,EAAOG,EAAUC,CAAK,CAAC,EAIhE,IAAI,eAAe,CACtB,OAAQ,CACJ,KAAK,UAAYC,EAAiBL,EAAOG,EAAUC,CAAK,CAC5D,EACA,KAAKE,EAAY,CACb,GAAM,CAAE,MAAAN,EAAO,KAAAC,CAAK,EAAI,KAAK,UAAU,KAAK,EAExCA,EACAK,EAAW,MAAM,EAEjBA,EAAW,QAAQN,CAAK,CAEhC,EACA,QAAS,CACL,KAAK,UAAY,IACrB,CACJ,CAAC,CACL", "names": ["src_exports", "__export", "createStringifyWebStream", "parseChunked", "parseFromWebStream", "stringifyChunked", "stringifyInfo", "isIterable", "value", "replaceValue", "holder", "key", "replacer", "cls", "normalizeReplacer", "item", "normalizeSpace", "space", "normalizeStringifyOptions", "optionsOrReplacer", "get<PERSON><PERSON><PERSON>", "allowlist", "STACK_OBJECT", "STACK_ARRAY", "decoder", "adjustPosition", "error", "parser", "_", "pos", "append", "array", "elements", "initialLength", "i", "parseChunked", "chunkEmitter", "iterable", "isIterable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunk", "e", "fragment", "wrap", "value", "start", "end", "key", "origRawChunk", "seq<PERSON><PERSON><PERSON>", "byte", "chunkLength", "lastFlushPoint", "flushPoint", "scan", "encodeString", "value", "stringifyChunked", "args", "replacer", "get<PERSON><PERSON><PERSON>", "space", "options", "normalizeStringifyOptions", "highWaterMark", "keyStrings", "stack", "rootValue", "prevState", "state", "printEntry", "stateValue", "stateEmpty", "stateKeys", "stateIndex", "buffer", "printObject", "popState", "key", "printArray", "printEntryPrelude", "keyString", "replaceValue", "pushPrimitive", "pushState", "hasOwn", "object", "key", "escapableCharCodeSubstitution", "charLength2048", "_", "code", "isLeadingSurrogate", "isTrailingSurrogate", "stringLength", "str", "len", "prevLeadingSurrogate", "i", "intLength", "num", "<PERSON><PERSON><PERSON><PERSON>", "value", "stringifyInfo", "args", "replacer", "get<PERSON><PERSON><PERSON>", "options", "normalizeStringifyOptions", "continueOnCircular", "space", "<PERSON><PERSON><PERSON><PERSON>", "visited", "circular", "stack", "root", "stop", "bytes", "spaceBytes", "objects", "walk", "holder", "replaceValue", "prevObjects", "valueBytes", "valueLength", "prevLength", "keyLen", "parseFromWebStream", "stream", "parseChunked", "isIterable", "reader", "value", "done", "createStringifyWebStream", "replacer", "space", "stringifyChunked", "controller"]}