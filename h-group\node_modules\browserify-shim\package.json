{"name": "browserify-shim", "version": "3.8.16", "description": "Makes CommonJS-incompatible modules browserifyable.", "main": "index.js", "scripts": {"test-main": "tap test/*.js && tap test/shim/*.js", "test": "test-peer-range browserify", "shim-jquery": "npm install opener && cd ./examples/shim-jquery && npm install && node build.js && opener index.html", "shim-jquery-diag": "npm install opener && cd ./examples/shim-jquery && npm install && node build-diag.js && opener index.html", "expose-jquery": "npm install opener && cd ./examples/expose-jquery && npm install && node build.js && opener index.html", "expose-jquery-diag": "npm install opener && cd ./examples/expose-jquery && npm install && node build-diag.js && opener index.html", "dependency-with-global": "npm install opener && cd ./examples/dependency-with-global && npm install && node build.js && opener index.html", "dependency-with-global-diag": "npm install opener && cd ./examples/dependency-with-global && npm install && node build-diag.js && opener index.html"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/browserify-shim.git"}, "keywords": ["browserify", "browserify-transform", "shim", "global", "globals", "transform", "window", "commonjs"], "author": "<PERSON><PERSON> <<EMAIL>> (thlorenz.com)", "license": "MIT", "readmeFilename": "README.md", "devDependencies": {"browserify": ">= 13", "jsdom": "^5.4.2", "ncp": "~0.5.0", "proxyquire": "~0.5.1", "request": "~2.88.0", "rimraf": "~2.2.6", "tap": "^1.1.0", "test-peer-range": "1.0.1"}, "dependencies": {"exposify": "~0.5.0", "mothership": "~0.3.0", "rename-function-calls": "~0.1.0", "resolve": "~0.6.1", "through": "~2.3.4"}, "peerDependencies": {"browserify": ">= 2.3"}}