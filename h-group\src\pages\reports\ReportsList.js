const React = require('react');
const { Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ReportsList = () => {
  const { hasPermission } = useAuth();
  
  // قائمة التقارير المتاحة
  const reports = [
    {
      id: 'sales',
      title: 'تقرير المبيعات',
      description: 'عرض تقرير مفصل عن المبيعات والإيرادات حسب الفترة الزمنية',
      icon: 'fas fa-chart-line',
      color: 'primary',
      permission: 'reports_sales',
      path: '/reports/sales'
    },
    {
      id: 'expenses',
      title: 'تقرير المصروفات',
      description: 'عرض تقرير مفصل عن مصروفات المصنع حسب الفترة والفئة',
      icon: 'fas fa-money-bill-wave',
      color: 'danger',
      permission: 'reports_expenses',
      path: '/reports/expenses'
    },
    {
      id: 'profit',
      title: 'تقرير الأرباح',
      description: 'عرض تقرير مفصل عن الأرباح والخسائر حسب الفترة الزمنية',
      icon: 'fas fa-chart-pie',
      color: 'success',
      permission: 'reports_profit',
      path: '/reports/profit'
    },
    {
      id: 'inventory',
      title: 'تقرير المخزون',
      description: 'عرض تقرير مفصل عن حالة المخزون واستهلاك المواد الخام',
      icon: 'fas fa-boxes',
      color: 'warning',
      permission: 'reports_inventory',
      path: '/reports/inventory'
    },
    {
      id: 'workers',
      title: 'تقرير العمال',
      description: 'عرض تقرير مفصل عن أداء العمال والمصممين والمدفوعات',
      icon: 'fas fa-users',
      color: 'info',
      permission: 'reports_workers',
      path: '/reports/workers'
    },
    {
      id: 'customers',
      title: 'تقرير العملاء',
      description: 'عرض تقرير مفصل عن العملاء وطلباتهم ومدفوعاتهم',
      icon: 'fas fa-user-tie',
      color: 'secondary',
      permission: 'reports_customers',
      path: '/reports/customers'
    },
    {
      id: 'orders',
      title: 'تقرير الطلبات',
      description: 'عرض تقرير مفصل عن حالة الطلبات ومواعيد التسليم',
      icon: 'fas fa-clipboard-list',
      color: 'dark',
      permission: 'reports_orders',
      path: '/reports/orders'
    },
    {
      id: 'invoices',
      title: 'تقرير الفواتير',
      description: 'عرض تقرير مفصل عن الفواتير والمدفوعات والمستحقات',
      icon: 'fas fa-file-invoice-dollar',
      color: 'primary',
      permission: 'reports_invoices',
      path: '/reports/invoices'
    }
  ];
  
  // تصفية التقارير حسب الصلاحيات
  const filteredReports = reports.filter(report => hasPermission(report.permission));
  
  return (
    <div className="reports-list-page">
      <div className="page-header">
        <h2>التقارير</h2>
      </div>
      
      {filteredReports.length === 0 ? (
        <div className="alert alert-warning">
          ليس لديك صلاحية للوصول إلى أي تقارير. يرجى التواصل مع مدير النظام.
        </div>
      ) : (
        <div className="row">
          {filteredReports.map(report => (
            <div key={report.id} className="col-md-6 col-lg-4 mb-4">
              <div className={`card report-card border-${report.color}`}>
                <div className={`card-header bg-${report.color} text-white`}>
                  <h5 className="mb-0">
                    <i className={`${report.icon} me-2`}></i>
                    {report.title}
                  </h5>
                </div>
                <div className="card-body">
                  <p className="card-text">{report.description}</p>
                  <Link to={report.path} className={`btn btn-${report.color}`}>
                    <i className="fas fa-external-link-alt me-1"></i>
                    عرض التقرير
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* قسم التصدير الشامل */}
      {hasPermission('reports_export') && (
        <div className="card mt-4">
          <div className="card-header">
            <h5>تصدير البيانات</h5>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6">
                <div className="export-option">
                  <h6>تصدير جميع البيانات</h6>
                  <p>تصدير جميع بيانات النظام إلى ملفات Excel منفصلة مضغوطة في ملف ZIP واحد.</p>
                  <button className="btn btn-success">
                    <i className="fas fa-file-export me-1"></i>
                    تصدير جميع البيانات
                  </button>
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="export-option">
                  <h6>نسخة احتياطية للنظام</h6>
                  <p>إنشاء نسخة احتياطية كاملة لقاعدة البيانات يمكن استعادتها لاحقًا.</p>
                  <button className="btn btn-primary">
                    <i className="fas fa-database me-1"></i>
                    إنشاء نسخة احتياطية
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

module.exports = ReportsList;
