const fs = require('fs');
const path = require('path');

// قائمة الملفات المتبقية
const files = [
    'src/pages/materials/MaterialForm.js',
    'src/pages/products/ProductsList.js',
    'src/pages/products/ProductDetails.js',
    'src/pages/products/ProductForm.js',
    'src/pages/workers/WorkersList.js',
    'src/pages/workers/WorkerDetails.js',
    'src/pages/workers/WorkerForm.js',
    'src/pages/expenses/ExpenseForm.js',
    'src/pages/inventory/InventoryList.js',
    'src/pages/inventory/InventoryForm.js',
    'src/pages/invoices/InvoicesList.js',
    'src/pages/invoices/InvoiceDetails.js',
    'src/pages/invoices/CreateInvoice.js',
    'src/pages/reports/SalesReport.js',
    'src/pages/reports/ReportsList.js',
    'src/pages/settings/SettingsList.js',
    'src/pages/settings/PricingSettings.js'
];

files.forEach(filePath => {
    if (fs.existsSync(filePath)) {
        console.log(`إصلاح ملف: ${filePath}`);
        
        let content = fs.readFileSync(filePath, 'utf8');
        
        // استبدال import statements
        content = content.replace(/import React, \{ ([^}]+) \} from 'react';/g, "const React = require('react');\nconst { $1 } = React;");
        content = content.replace(/import React from 'react';/g, "const React = require('react');");
        content = content.replace(/import \{ ([^}]+) \} from 'react';/g, "const React = require('react');\nconst { $1 } = React;");
        content = content.replace(/import \{ ([^}]+) \} from 'react-router-dom';/g, "const { $1 } = require('react-router-dom');");
        content = content.replace(/import \{ ([^}]+) \} from '([^']+)';/g, "const { $1 } = require('$2');");
        content = content.replace(/import ([^\s]+) from '([^']+)';/g, "const $1 = require('$2');");
        
        // استبدال export statements
        content = content.replace(/export default ([^;]+);/g, "module.exports = $1;");
        
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`تم إصلاح: ${filePath}`);
    } else {
        console.log(`الملف غير موجود: ${filePath}`);
    }
});

console.log('تم الانتهاء من إصلاح جميع الملفات');
