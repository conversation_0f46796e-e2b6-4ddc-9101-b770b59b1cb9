{"name": "section-iterator", "version": "2.0.0", "description": "Simple iterator for flat and multi section lists", "main": "dist/index.js", "repository": {"type": "git", "url": "https://github.com/moroshko/section-iterator.git"}, "author": "<PERSON><PERSON> <michael.morosh<PERSON>@gmail.com>", "scripts": {"lint": "eslint index.js index.test.js", "test": "mocha index.test.js --compilers js:babel-register", "dist": "rm -rf dist && mkdir dist && babel index.js --out-file dist/index.js", "prebuild": "npm run lint && npm test", "build": "npm run dist", "postversion": "git push && git push --tags", "prepublish": "npm run dist"}, "devDependencies": {"babel-cli": "^6.7.5", "babel-core": "^6.7.6", "babel-eslint": "^6.0.3", "babel-preset-es2015": "^6.6.0", "babel-preset-stage-0": "^6.5.0", "babel-register": "^6.7.2", "chai": "^3.5.0", "eslint": "^2.8.0", "mocha": "^2.4.5"}, "files": ["dist"], "keywords": ["iterator", "array"], "license": "MIT"}