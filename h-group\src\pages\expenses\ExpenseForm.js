const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ExpenseForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  // تحديد ما إذا كان النموذج للتعديل أو للإضافة
  const isEditMode = !!id;
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    expense_date: new Date().toISOString().split('T')[0],
    category: '',
    description: '',
    amount: '',
    recurring: false,
    period: 'شهري'
  });
  
  const [categories, setCategories] = useState(['إيجار', 'رواتب', 'كهرباء', 'ماء', 'صيانة', 'مستلزمات', 'أخرى']);
  const [periods, setPeriods] = useState(['يومي', 'أسبوعي', 'شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي']);
  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // تحميل بيانات المصروف في حالة التعديل
  useEffect(() => {
    const fetchExpense = async () => {
      if (!isEditMode) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const expenseData = await window.api.factoryExpenses.getById(id);
        if (!expenseData) {
          throw new Error('لم يتم العثور على المصروف');
        }
        
        setFormData({
          expense_date: new Date(expenseData.expense_date).toISOString().split('T')[0],
          category: expenseData.category || '',
          description: expenseData.description || '',
          amount: expenseData.amount ? expenseData.amount.toString() : '',
          recurring: expenseData.recurring || false,
          period: expenseData.period || 'شهري'
        });
        
        // إضافة الفئة إلى القائمة إذا لم تكن موجودة
        if (expenseData.category && !categories.includes(expenseData.category)) {
          setCategories(prev => [...prev, expenseData.category]);
        }
        
        // إضافة الفترة إلى القائمة إذا لم تكن موجودة
        if (expenseData.period && !periods.includes(expenseData.period)) {
          setPeriods(prev => [...prev, expenseData.period]);
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات المصروف:', error);
        setError('حدث خطأ أثناء تحميل بيانات المصروف. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchExpense();
  }, [id, isEditMode]);
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (isEditMode && !hasPermission('expenses_edit')) {
      alert('ليس لديك صلاحية لتعديل المصروفات');
      navigate('/expenses');
    } else if (!isEditMode && !hasPermission('expenses_create')) {
      alert('ليس لديك صلاحية لإضافة مصروفات جديدة');
      navigate('/expenses');
    }
  }, [isEditMode, hasPermission, navigate]);
  
  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  // معالجة تغيير المبلغ (للتأكد من أنه رقم)
  const handleAmountChange = (e) => {
    const value = e.target.value;
    if (value === '' || /^\d+(\.\d{0,2})?$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        amount: value
      }));
    }
  };
  
  // إضافة فئة جديدة
  const handleAddCategory = () => {
    const newCategory = prompt('أدخل الفئة الجديدة:');
    if (newCategory && newCategory.trim() && !categories.includes(newCategory.trim())) {
      setCategories([...categories, newCategory.trim()]);
      setFormData(prev => ({
        ...prev,
        category: newCategory.trim()
      }));
    }
  };
  
  // إضافة فترة جديدة
  const handleAddPeriod = () => {
    const newPeriod = prompt('أدخل الفترة الجديدة:');
    if (newPeriod && newPeriod.trim() && !periods.includes(newPeriod.trim())) {
      setPeriods([...periods, newPeriod.trim()]);
      setFormData(prev => ({
        ...prev,
        period: newPeriod.trim()
      }));
    }
  };
  
  // التحقق من صحة البيانات
  const validateForm = () => {
    if (!formData.expense_date) {
      alert('يرجى إدخال تاريخ المصروف');
      return false;
    }
    
    if (!formData.category) {
      alert('يرجى اختيار فئة المصروف');
      return false;
    }
    
    if (!formData.description.trim()) {
      alert('يرجى إدخال وصف المصروف');
      return false;
    }
    
    if (!formData.amount.trim() || parseFloat(formData.amount) <= 0) {
      alert('يرجى إدخال مبلغ صحيح أكبر من صفر');
      return false;
    }
    
    if (formData.recurring && !formData.period) {
      alert('يرجى اختيار فترة التكرار للمصروف المتكرر');
      return false;
    }
    
    return true;
  };
  
  // حفظ البيانات
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setSubmitting(true);
      setError(null);
      
      // تحويل المبلغ إلى رقم
      const expenseData = {
        ...formData,
        amount: parseFloat(formData.amount)
      };
      
      let result;
      
      if (isEditMode) {
        // تعديل المصروف
        result = await window.api.factoryExpenses.update(id, expenseData);
        if (result.success) {
          alert('تم تعديل المصروف بنجاح');
          navigate('/expenses');
        } else {
          throw new Error('فشل في تعديل المصروف');
        }
      } else {
        // إضافة مصروف جديد
        result = await window.api.factoryExpenses.create(expenseData);
        if (result.success) {
          alert('تم إضافة المصروف بنجاح');
          navigate('/expenses');
        } else {
          throw new Error('فشل في إضافة المصروف');
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ بيانات المصروف:', error);
      setError('حدث خطأ أثناء حفظ بيانات المصروف. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  return (
    <div className="expense-form-page">
      <div className="page-header">
        <h2>{isEditMode ? 'تعديل المصروف' : 'إضافة مصروف جديد'}</h2>
        <div className="page-actions">
          <Link to="/expenses" className="btn btn-secondary">
            <i className="fas fa-times"></i> إلغاء
          </Link>
        </div>
      </div>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">تاريخ المصروف <span className="text-danger">*</span></label>
                  <input
                    type="date"
                    className="form-control"
                    name="expense_date"
                    value={formData.expense_date}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">الفئة <span className="text-danger">*</span></label>
                  <div className="input-group">
                    <select
                      className="form-control"
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      required
                    >
                      <option value="">اختر الفئة</option>
                      {categories.map((category, index) => (
                        <option key={index} value={category}>{category}</option>
                      ))}
                    </select>
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={handleAddCategory}
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="form-group mb-3">
              <label className="form-label">الوصف <span className="text-danger">*</span></label>
              <input
                type="text"
                className="form-control"
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">المبلغ (ر.س) <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="amount"
                    value={formData.amount}
                    onChange={handleAmountChange}
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <div className="form-check mt-4">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="recurring"
                      name="recurring"
                      checked={formData.recurring}
                      onChange={handleChange}
                    />
                    <label className="form-check-label" htmlFor="recurring">
                      مصروف متكرر
                    </label>
                  </div>
                </div>
              </div>
            </div>
            
            {formData.recurring && (
              <div className="form-group mb-3">
                <label className="form-label">فترة التكرار <span className="text-danger">*</span></label>
                <div className="input-group">
                  <select
                    className="form-control"
                    name="period"
                    value={formData.period}
                    onChange={handleChange}
                    required={formData.recurring}
                  >
                    <option value="">اختر الفترة</option>
                    {periods.map((period, index) => (
                      <option key={index} value={period}>{period}</option>
                    ))}
                  </select>
                  <button
                    type="button"
                    className="btn btn-outline-secondary"
                    onClick={handleAddPeriod}
                  >
                    <i className="fas fa-plus"></i>
                  </button>
                </div>
              </div>
            )}
            
            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span className="ms-1">جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    <span className="ms-1">{isEditMode ? 'حفظ التعديلات' : 'إضافة المصروف'}</span>
                  </>
                )}
              </button>
              
              <Link to="/expenses" className="btn btn-secondary">
                <i className="fas fa-times"></i> إلغاء
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = ExpenseForm;
