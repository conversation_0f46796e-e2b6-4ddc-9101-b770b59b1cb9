<!doctype html>
<html>
<head>
    <title>Code coverage report for esprima/esprima.js</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta http-equiv="content-language" content="en-gb">
    
        <link rel='stylesheet' href='../prettify.css'>
    
    <style type='text/css'>
        body, html {
            margin:0; padding: 0;
        }
        body {
            font-family: "Helvetic Neue", Helvetica,Arial;
            font-size: 10pt;
        }
        div.header, div.footer {
            background: #eee;
            padding: 1em;
        }
        div.header {
            z-index: 100;
            position: fixed;
            top: 0;
            border-bottom: 1px solid #666;
            width: 100%;
        }
        div.footer {
            border-top: 1px solid #666;
        }
        div.body {
            margin-top: 10em;
        }
        div.meta {
            font-size: 90%;
            text-align: center;
        }
        h1, h2, h3 {
            font-weight: normal;
        }
        h1 {
            font-size: 12pt;
        }
        h2 {
            font-size: 10pt;
        }
        pre {
            font-family: consolas, menlo, monaco, monospace;
            margin: 0;
            padding: 0;
            line-height: 14px;
            font-size: 14px;
        }

        div.path { font-size: 110%; }
        div.path a:link, div.path a:visited { color: #000; }
        table.coverage { border-collapse: collapse; margin:0; padding: 0 }

        table.coverage td {
            margin: 0;
            padding: 0;
            color: #111;
            vertical-align: top;
        }
        table.coverage td.line-count {
            width: 50px;
            text-align: right;
            padding-right: 5px;
        }
        table.coverage td.line-coverage {
            color: #777 !important;
            text-align: right;
            border-left: 1px solid #666;
            border-right: 1px solid #666;
        }

        table.coverage td.text {
        }

        table.coverage td span.cline-any {
            display: inline-block;
            padding: 0 5px;
            width: 40px;
        }
        table.coverage td span.cline-neutral {
            background: #eee;
        }
        table.coverage td span.cline-yes {
            background: #b5d592;
            color: #999;
        }
        table.coverage td span.cline-no {
            background: #fc8c84;
        }

        .cstat-yes { color: #111; }
        .cstat-no { background: #fc8c84; color: #111; }
        .fstat-no { background: #ffc520; color: #111 !important; }
        .cbranch-no { background:  yellow !important; color: #111; }
        .missing-if-branch {
            display: inline-block;
            margin-right: 10px;
            position: relative;
            padding: 0 4px;
            background: black;
            color: yellow;
            xtext-decoration: line-through;
        }
        .missing-if-branch .typ {
            color: inherit !important;
        }

        .entity, .metric { font-weight: bold; }
        .metric { display: inline-block; border: 1px solid #333; padding: 0.3em; background: white; }
        .metric small { font-size: 80%; font-weight: normal; color: #666; }

        div.coverage-summary table { border-collapse: collapse; margin: 3em; font-size: 110%; }
        div.coverage-summary td, div.coverage-summary table  th { margin: 0; padding: 0.25em 1em; border-top: 1px solid #666; border-bottom: 1px solid #666; }
        div.coverage-summary th { text-align: left; border: 1px solid #666; background: #eee; font-weight: normal; }
        div.coverage-summary th.file { border-right: none !important; }
        div.coverage-summary th.pic { border-left: none !important; text-align: right; }
        div.coverage-summary th.pct { border-right: none !important; }
        div.coverage-summary th.abs { border-left: none !important; text-align: right; }
        div.coverage-summary td.pct { text-align: right; border-left: 1px solid #666; }
        div.coverage-summary td.abs { text-align: right; font-size: 90%; color: #444; border-right: 1px solid #666; }
        div.coverage-summary td.file { text-align: right; border-left: 1px solid #666; white-space: nowrap;  }
        div.coverage-summary td.pic { min-width: 120px !important;  }
        div.coverage-summary a:link { text-decoration: none; color: #000; }
        div.coverage-summary a:visited { text-decoration: none; color: #333; }
        div.coverage-summary a:hover { text-decoration: underline; }
        div.coverage-summary tfoot td { border-top: 1px solid #666; }

        div.coverage-summary .yui3-datatable-sort-indicator, div.coverage-summary .dummy-sort-indicator {
            height: 10px;
            width: 7px;
            display: inline-block;
            margin-left: 0.5em;
        }
        div.coverage-summary .yui3-datatable-sort-indicator {
            background: url("http://yui.yahooapis.com/3.6.0/build/datatable-sort/assets/skins/sam/sort-arrow-sprite.png") no-repeat scroll 0 0 transparent;
        }
        div.coverage-summary .yui3-datatable-sorted .yui3-datatable-sort-indicator {
            background-position: 0 -20px;
        }
        div.coverage-summary .yui3-datatable-sorted-desc .yui3-datatable-sort-indicator {
            background-position: 0 -10px;
        }

        .high { background: #b5d592 !important; }
        .medium { background: #ffe87c !important; }
        .low { background: #fc8c84 !important; }

        span.cover-fill, span.cover-empty {
            display:inline-block;
            border:1px solid #444;
            background: white;
            height: 12px;
        }
        span.cover-fill {
            background: #ccc;
            border-right: 1px solid #444;
        }
        span.cover-empty {
            background: white;
            border-left: none;
        }
        span.cover-full {
            border-right: none !important;
        }
        pre.prettyprint {
            border: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        .com { color: #999 !important; }
    </style>
</head>
<body>
<div class='header high'>
    <h1>Code coverage report for <span class='entity'>esprima/esprima.js</span></h1>
    <h2>
        
        Statements: <span class='metric'>99.72% <small>(1760 / 1765)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        
        Branches: <span class='metric'>98.49% <small>(1107 / 1124)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        
        Functions: <span class='metric'>100% <small>(154 / 154)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        
        Lines: <span class='metric'>99.72% <small>(1760 / 1765)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
    </h2>
</div>
<div class='body'>
<pre><table class="coverage">
<tr><td class="line-count">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721
722
723
724
725
726
727
728
729
730
731
732
733
734
735
736
737
738
739
740
741
742
743
744
745
746
747
748
749
750
751
752
753
754
755
756
757
758
759
760
761
762
763
764
765
766
767
768
769
770
771
772
773
774
775
776
777
778
779
780
781
782
783
784
785
786
787
788
789
790
791
792
793
794
795
796
797
798
799
800
801
802
803
804
805
806
807
808
809
810
811
812
813
814
815
816
817
818
819
820
821
822
823
824
825
826
827
828
829
830
831
832
833
834
835
836
837
838
839
840
841
842
843
844
845
846
847
848
849
850
851
852
853
854
855
856
857
858
859
860
861
862
863
864
865
866
867
868
869
870
871
872
873
874
875
876
877
878
879
880
881
882
883
884
885
886
887
888
889
890
891
892
893
894
895
896
897
898
899
900
901
902
903
904
905
906
907
908
909
910
911
912
913
914
915
916
917
918
919
920
921
922
923
924
925
926
927
928
929
930
931
932
933
934
935
936
937
938
939
940
941
942
943
944
945
946
947
948
949
950
951
952
953
954
955
956
957
958
959
960
961
962
963
964
965
966
967
968
969
970
971
972
973
974
975
976
977
978
979
980
981
982
983
984
985
986
987
988
989
990
991
992
993
994
995
996
997
998
999
1000
1001
1002
1003
1004
1005
1006
1007
1008
1009
1010
1011
1012
1013
1014
1015
1016
1017
1018
1019
1020
1021
1022
1023
1024
1025
1026
1027
1028
1029
1030
1031
1032
1033
1034
1035
1036
1037
1038
1039
1040
1041
1042
1043
1044
1045
1046
1047
1048
1049
1050
1051
1052
1053
1054
1055
1056
1057
1058
1059
1060
1061
1062
1063
1064
1065
1066
1067
1068
1069
1070
1071
1072
1073
1074
1075
1076
1077
1078
1079
1080
1081
1082
1083
1084
1085
1086
1087
1088
1089
1090
1091
1092
1093
1094
1095
1096
1097
1098
1099
1100
1101
1102
1103
1104
1105
1106
1107
1108
1109
1110
1111
1112
1113
1114
1115
1116
1117
1118
1119
1120
1121
1122
1123
1124
1125
1126
1127
1128
1129
1130
1131
1132
1133
1134
1135
1136
1137
1138
1139
1140
1141
1142
1143
1144
1145
1146
1147
1148
1149
1150
1151
1152
1153
1154
1155
1156
1157
1158
1159
1160
1161
1162
1163
1164
1165
1166
1167
1168
1169
1170
1171
1172
1173
1174
1175
1176
1177
1178
1179
1180
1181
1182
1183
1184
1185
1186
1187
1188
1189
1190
1191
1192
1193
1194
1195
1196
1197
1198
1199
1200
1201
1202
1203
1204
1205
1206
1207
1208
1209
1210
1211
1212
1213
1214
1215
1216
1217
1218
1219
1220
1221
1222
1223
1224
1225
1226
1227
1228
1229
1230
1231
1232
1233
1234
1235
1236
1237
1238
1239
1240
1241
1242
1243
1244
1245
1246
1247
1248
1249
1250
1251
1252
1253
1254
1255
1256
1257
1258
1259
1260
1261
1262
1263
1264
1265
1266
1267
1268
1269
1270
1271
1272
1273
1274
1275
1276
1277
1278
1279
1280
1281
1282
1283
1284
1285
1286
1287
1288
1289
1290
1291
1292
1293
1294
1295
1296
1297
1298
1299
1300
1301
1302
1303
1304
1305
1306
1307
1308
1309
1310
1311
1312
1313
1314
1315
1316
1317
1318
1319
1320
1321
1322
1323
1324
1325
1326
1327
1328
1329
1330
1331
1332
1333
1334
1335
1336
1337
1338
1339
1340
1341
1342
1343
1344
1345
1346
1347
1348
1349
1350
1351
1352
1353
1354
1355
1356
1357
1358
1359
1360
1361
1362
1363
1364
1365
1366
1367
1368
1369
1370
1371
1372
1373
1374
1375
1376
1377
1378
1379
1380
1381
1382
1383
1384
1385
1386
1387
1388
1389
1390
1391
1392
1393
1394
1395
1396
1397
1398
1399
1400
1401
1402
1403
1404
1405
1406
1407
1408
1409
1410
1411
1412
1413
1414
1415
1416
1417
1418
1419
1420
1421
1422
1423
1424
1425
1426
1427
1428
1429
1430
1431
1432
1433
1434
1435
1436
1437
1438
1439
1440
1441
1442
1443
1444
1445
1446
1447
1448
1449
1450
1451
1452
1453
1454
1455
1456
1457
1458
1459
1460
1461
1462
1463
1464
1465
1466
1467
1468
1469
1470
1471
1472
1473
1474
1475
1476
1477
1478
1479
1480
1481
1482
1483
1484
1485
1486
1487
1488
1489
1490
1491
1492
1493
1494
1495
1496
1497
1498
1499
1500
1501
1502
1503
1504
1505
1506
1507
1508
1509
1510
1511
1512
1513
1514
1515
1516
1517
1518
1519
1520
1521
1522
1523
1524
1525
1526
1527
1528
1529
1530
1531
1532
1533
1534
1535
1536
1537
1538
1539
1540
1541
1542
1543
1544
1545
1546
1547
1548
1549
1550
1551
1552
1553
1554
1555
1556
1557
1558
1559
1560
1561
1562
1563
1564
1565
1566
1567
1568
1569
1570
1571
1572
1573
1574
1575
1576
1577
1578
1579
1580
1581
1582
1583
1584
1585
1586
1587
1588
1589
1590
1591
1592
1593
1594
1595
1596
1597
1598
1599
1600
1601
1602
1603
1604
1605
1606
1607
1608
1609
1610
1611
1612
1613
1614
1615
1616
1617
1618
1619
1620
1621
1622
1623
1624
1625
1626
1627
1628
1629
1630
1631
1632
1633
1634
1635
1636
1637
1638
1639
1640
1641
1642
1643
1644
1645
1646
1647
1648
1649
1650
1651
1652
1653
1654
1655
1656
1657
1658
1659
1660
1661
1662
1663
1664
1665
1666
1667
1668
1669
1670
1671
1672
1673
1674
1675
1676
1677
1678
1679
1680
1681
1682
1683
1684
1685
1686
1687
1688
1689
1690
1691
1692
1693
1694
1695
1696
1697
1698
1699
1700
1701
1702
1703
1704
1705
1706
1707
1708
1709
1710
1711
1712
1713
1714
1715
1716
1717
1718
1719
1720
1721
1722
1723
1724
1725
1726
1727
1728
1729
1730
1731
1732
1733
1734
1735
1736
1737
1738
1739
1740
1741
1742
1743
1744
1745
1746
1747
1748
1749
1750
1751
1752
1753
1754
1755
1756
1757
1758
1759
1760
1761
1762
1763
1764
1765
1766
1767
1768
1769
1770
1771
1772
1773
1774
1775
1776
1777
1778
1779
1780
1781
1782
1783
1784
1785
1786
1787
1788
1789
1790
1791
1792
1793
1794
1795
1796
1797
1798
1799
1800
1801
1802
1803
1804
1805
1806
1807
1808
1809
1810
1811
1812
1813
1814
1815
1816
1817
1818
1819
1820
1821
1822
1823
1824
1825
1826
1827
1828
1829
1830
1831
1832
1833
1834
1835
1836
1837
1838
1839
1840
1841
1842
1843
1844
1845
1846
1847
1848
1849
1850
1851
1852
1853
1854
1855
1856
1857
1858
1859
1860
1861
1862
1863
1864
1865
1866
1867
1868
1869
1870
1871
1872
1873
1874
1875
1876
1877
1878
1879
1880
1881
1882
1883
1884
1885
1886
1887
1888
1889
1890
1891
1892
1893
1894
1895
1896
1897
1898
1899
1900
1901
1902
1903
1904
1905
1906
1907
1908
1909
1910
1911
1912
1913
1914
1915
1916
1917
1918
1919
1920
1921
1922
1923
1924
1925
1926
1927
1928
1929
1930
1931
1932
1933
1934
1935
1936
1937
1938
1939
1940
1941
1942
1943
1944
1945
1946
1947
1948
1949
1950
1951
1952
1953
1954
1955
1956
1957
1958
1959
1960
1961
1962
1963
1964
1965
1966
1967
1968
1969
1970
1971
1972
1973
1974
1975
1976
1977
1978
1979
1980
1981
1982
1983
1984
1985
1986
1987
1988
1989
1990
1991
1992
1993
1994
1995
1996
1997
1998
1999
2000
2001
2002
2003
2004
2005
2006
2007
2008
2009
2010
2011
2012
2013
2014
2015
2016
2017
2018
2019
2020
2021
2022
2023
2024
2025
2026
2027
2028
2029
2030
2031
2032
2033
2034
2035
2036
2037
2038
2039
2040
2041
2042
2043
2044
2045
2046
2047
2048
2049
2050
2051
2052
2053
2054
2055
2056
2057
2058
2059
2060
2061
2062
2063
2064
2065
2066
2067
2068
2069
2070
2071
2072
2073
2074
2075
2076
2077
2078
2079
2080
2081
2082
2083
2084
2085
2086
2087
2088
2089
2090
2091
2092
2093
2094
2095
2096
2097
2098
2099
2100
2101
2102
2103
2104
2105
2106
2107
2108
2109
2110
2111
2112
2113
2114
2115
2116
2117
2118
2119
2120
2121
2122
2123
2124
2125
2126
2127
2128
2129
2130
2131
2132
2133
2134
2135
2136
2137
2138
2139
2140
2141
2142
2143
2144
2145
2146
2147
2148
2149
2150
2151
2152
2153
2154
2155
2156
2157
2158
2159
2160
2161
2162
2163
2164
2165
2166
2167
2168
2169
2170
2171
2172
2173
2174
2175
2176
2177
2178
2179
2180
2181
2182
2183
2184
2185
2186
2187
2188
2189
2190
2191
2192
2193
2194
2195
2196
2197
2198
2199
2200
2201
2202
2203
2204
2205
2206
2207
2208
2209
2210
2211
2212
2213
2214
2215
2216
2217
2218
2219
2220
2221
2222
2223
2224
2225
2226
2227
2228
2229
2230
2231
2232
2233
2234
2235
2236
2237
2238
2239
2240
2241
2242
2243
2244
2245
2246
2247
2248
2249
2250
2251
2252
2253
2254
2255
2256
2257
2258
2259
2260
2261
2262
2263
2264
2265
2266
2267
2268
2269
2270
2271
2272
2273
2274
2275
2276
2277
2278
2279
2280
2281
2282
2283
2284
2285
2286
2287
2288
2289
2290
2291
2292
2293
2294
2295
2296
2297
2298
2299
2300
2301
2302
2303
2304
2305
2306
2307
2308
2309
2310
2311
2312
2313
2314
2315
2316
2317
2318
2319
2320
2321
2322
2323
2324
2325
2326
2327
2328
2329
2330
2331
2332
2333
2334
2335
2336
2337
2338
2339
2340
2341
2342
2343
2344
2345
2346
2347
2348
2349
2350
2351
2352
2353
2354
2355
2356
2357
2358
2359
2360
2361
2362
2363
2364
2365
2366
2367
2368
2369
2370
2371
2372
2373
2374
2375
2376
2377
2378
2379
2380
2381
2382
2383
2384
2385
2386
2387
2388
2389
2390
2391
2392
2393
2394
2395
2396
2397
2398
2399
2400
2401
2402
2403
2404
2405
2406
2407
2408
2409
2410
2411
2412
2413
2414
2415
2416
2417
2418
2419
2420
2421
2422
2423
2424
2425
2426
2427
2428
2429
2430
2431
2432
2433
2434
2435
2436
2437
2438
2439
2440
2441
2442
2443
2444
2445
2446
2447
2448
2449
2450
2451
2452
2453
2454
2455
2456
2457
2458
2459
2460
2461
2462
2463
2464
2465
2466
2467
2468
2469
2470
2471
2472
2473
2474
2475
2476
2477
2478
2479
2480
2481
2482
2483
2484
2485
2486
2487
2488
2489
2490
2491
2492
2493
2494
2495
2496
2497
2498
2499
2500
2501
2502
2503
2504
2505
2506
2507
2508
2509
2510
2511
2512
2513
2514
2515
2516
2517
2518
2519
2520
2521
2522
2523
2524
2525
2526
2527
2528
2529
2530
2531
2532
2533
2534
2535
2536
2537
2538
2539
2540
2541
2542
2543
2544
2545
2546
2547
2548
2549
2550
2551
2552
2553
2554
2555
2556
2557
2558
2559
2560
2561
2562
2563
2564
2565
2566
2567
2568
2569
2570
2571
2572
2573
2574
2575
2576
2577
2578
2579
2580
2581
2582
2583
2584
2585
2586
2587
2588
2589
2590
2591
2592
2593
2594
2595
2596
2597
2598
2599
2600
2601
2602
2603
2604
2605
2606
2607
2608
2609
2610
2611
2612
2613
2614
2615
2616
2617
2618
2619
2620
2621
2622
2623
2624
2625
2626
2627
2628
2629
2630
2631
2632
2633
2634
2635
2636
2637
2638
2639
2640
2641
2642
2643
2644
2645
2646
2647
2648
2649
2650
2651
2652
2653
2654
2655
2656
2657
2658
2659
2660
2661
2662
2663
2664
2665
2666
2667
2668
2669
2670
2671
2672
2673
2674
2675
2676
2677
2678
2679
2680
2681
2682
2683
2684
2685
2686
2687
2688
2689
2690
2691
2692
2693
2694
2695
2696
2697
2698
2699
2700
2701
2702
2703
2704
2705
2706
2707
2708
2709
2710
2711
2712
2713
2714
2715
2716
2717
2718
2719
2720
2721
2722
2723
2724
2725
2726
2727
2728
2729
2730
2731
2732
2733
2734
2735
2736
2737
2738
2739
2740
2741
2742
2743
2744
2745
2746
2747
2748
2749
2750
2751
2752
2753
2754
2755
2756
2757
2758
2759
2760
2761
2762
2763
2764
2765
2766
2767
2768
2769
2770
2771
2772
2773
2774
2775
2776
2777
2778
2779
2780
2781
2782
2783
2784
2785
2786
2787
2788
2789
2790
2791
2792
2793
2794
2795
2796
2797
2798
2799
2800
2801
2802
2803
2804
2805
2806
2807
2808
2809
2810
2811
2812
2813
2814
2815
2816
2817
2818
2819
2820
2821
2822
2823
2824
2825
2826
2827
2828
2829
2830
2831
2832
2833
2834
2835
2836
2837
2838
2839
2840
2841
2842
2843
2844
2845
2846
2847
2848
2849
2850
2851
2852
2853
2854
2855
2856
2857
2858
2859
2860
2861
2862
2863
2864
2865
2866
2867
2868
2869
2870
2871
2872
2873
2874
2875
2876
2877
2878
2879
2880
2881
2882
2883
2884
2885
2886
2887
2888
2889
2890
2891
2892
2893
2894
2895
2896
2897
2898
2899
2900
2901
2902
2903
2904
2905
2906
2907
2908
2909
2910
2911
2912
2913
2914
2915
2916
2917
2918
2919
2920
2921
2922
2923
2924
2925
2926
2927
2928
2929
2930
2931
2932
2933
2934
2935
2936
2937
2938
2939
2940
2941
2942
2943
2944
2945
2946
2947
2948
2949
2950
2951
2952
2953
2954
2955
2956
2957
2958
2959
2960
2961
2962
2963
2964
2965
2966
2967
2968
2969
2970
2971
2972
2973
2974
2975
2976
2977
2978
2979
2980
2981
2982
2983
2984
2985
2986
2987
2988
2989
2990
2991
2992
2993
2994
2995
2996
2997
2998
2999
3000
3001
3002
3003
3004
3005
3006
3007
3008
3009
3010
3011
3012
3013
3014
3015
3016
3017
3018
3019
3020
3021
3022
3023
3024
3025
3026
3027
3028
3029
3030
3031
3032
3033
3034
3035
3036
3037
3038
3039
3040
3041
3042
3043
3044
3045
3046
3047
3048
3049
3050
3051
3052
3053
3054
3055
3056
3057
3058
3059
3060
3061
3062
3063
3064
3065
3066
3067
3068
3069
3070
3071
3072
3073
3074
3075
3076
3077
3078
3079
3080
3081
3082
3083
3084
3085
3086
3087
3088
3089
3090
3091
3092
3093
3094
3095
3096
3097
3098
3099
3100
3101
3102
3103
3104
3105
3106
3107
3108
3109
3110
3111
3112
3113
3114
3115
3116
3117
3118
3119
3120
3121
3122
3123
3124
3125
3126
3127
3128
3129
3130
3131
3132
3133
3134
3135
3136
3137
3138
3139
3140
3141
3142
3143
3144
3145
3146
3147
3148
3149
3150
3151
3152
3153
3154
3155
3156
3157
3158
3159
3160
3161
3162
3163
3164
3165
3166
3167
3168
3169
3170
3171
3172
3173
3174
3175
3176
3177
3178
3179
3180
3181
3182
3183
3184
3185
3186
3187
3188
3189
3190
3191
3192
3193
3194
3195
3196
3197
3198
3199
3200
3201
3202
3203
3204
3205
3206
3207
3208
3209
3210
3211
3212
3213
3214
3215
3216
3217
3218
3219
3220
3221
3222
3223
3224
3225
3226
3227
3228
3229
3230
3231
3232
3233
3234
3235
3236
3237
3238
3239
3240
3241
3242
3243
3244
3245
3246
3247
3248
3249
3250
3251
3252
3253
3254
3255
3256
3257
3258
3259
3260
3261
3262
3263
3264
3265
3266
3267
3268
3269
3270
3271
3272
3273
3274
3275
3276
3277
3278
3279
3280
3281
3282
3283
3284
3285
3286
3287
3288
3289
3290
3291
3292
3293
3294
3295
3296
3297
3298
3299
3300
3301
3302
3303
3304
3305
3306
3307
3308
3309
3310
3311
3312
3313
3314
3315
3316
3317
3318
3319
3320
3321
3322
3323
3324
3325
3326
3327
3328
3329
3330
3331
3332
3333
3334
3335
3336
3337
3338
3339
3340
3341
3342
3343
3344
3345
3346
3347
3348
3349
3350
3351
3352
3353
3354
3355
3356
3357
3358
3359
3360
3361
3362
3363
3364
3365
3366
3367
3368
3369
3370
3371
3372
3373
3374
3375
3376
3377
3378
3379
3380
3381
3382
3383
3384
3385
3386
3387
3388
3389
3390
3391
3392
3393
3394
3395
3396
3397
3398
3399
3400
3401
3402
3403
3404
3405
3406
3407
3408
3409
3410
3411
3412
3413
3414
3415
3416
3417
3418
3419
3420
3421
3422
3423
3424
3425
3426
3427
3428
3429
3430
3431
3432
3433
3434
3435
3436
3437
3438
3439
3440
3441
3442
3443
3444
3445
3446
3447
3448
3449
3450
3451
3452
3453
3454
3455
3456
3457
3458
3459
3460
3461
3462
3463
3464
3465
3466
3467
3468
3469
3470
3471
3472
3473
3474
3475
3476
3477
3478
3479
3480
3481
3482
3483
3484
3485
3486
3487
3488
3489
3490
3491
3492
3493
3494
3495
3496
3497
3498
3499
3500
3501
3502
3503
3504
3505
3506
3507
3508
3509
3510
3511
3512
3513
3514
3515
3516
3517
3518
3519
3520
3521
3522
3523
3524
3525
3526
3527
3528
3529
3530
3531
3532
3533
3534
3535
3536
3537
3538
3539
3540
3541
3542
3543
3544
3545
3546
3547
3548
3549
3550
3551
3552
3553
3554
3555
3556
3557
3558
3559
3560
3561
3562
3563
3564
3565
3566
3567
3568
3569
3570
3571
3572
3573
3574
3575
3576
3577
3578
3579
3580
3581
3582
3583
3584
3585
3586
3587
3588
3589
3590
3591
3592
3593
3594
3595
3596
3597
3598
3599
3600
3601
3602
3603
3604
3605
3606
3607
3608
3609
3610
3611
3612
3613
3614
3615
3616
3617
3618
3619
3620
3621
3622
3623
3624
3625
3626
3627
3628
3629
3630
3631
3632
3633
3634
3635
3636
3637
3638
3639
3640
3641
3642
3643
3644
3645
3646
3647
3648
3649
3650
3651
3652
3653
3654
3655
3656
3657
3658
3659
3660
3661
3662
3663
3664
3665
3666
3667
3668
3669
3670
3671
3672
3673
3674
3675
3676
3677
3678
3679
3680
3681
3682
3683
3684
3685
3686
3687
3688
3689
3690
3691
3692
3693
3694
3695
3696
3697
3698
3699
3700
3701
3702
3703
3704
3705
3706
3707
3708
3709
3710
3711
3712
3713
3714
3715
3716
3717
3718
3719
3720
3721
3722
3723
3724
3725
3726
3727
3728
3729
3730
3731
3732
3733
3734
3735
3736
3737
3738
3739
3740
3741
3742
3743
3744
3745
3746
3747
3748
3749
3750
3751
3752
3753
3754
3755
3756
3757
3758
3759
3760
3761
3762
3763
3764
3765
3766
3767
3768
3769
3770
3771
3772
3773
3774
3775
3776
3777
3778
3779
3780
3781
3782
3783
3784
3785
3786
3787
3788
3789
3790
3791
3792
3793
3794
3795
3796
3797
3798
3799
3800
3801
3802
3803
3804
3805
3806
3807
3808
3809
3810
3811
3812
3813
3814
3815
3816
3817
3818</td><td class="line-coverage"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1568</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">5969</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">224</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">266</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">32604</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">32068</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">9111</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">16161</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">88</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">84</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">632</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">108</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">524</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2798</span>
<span class="cline-any cline-yes">44</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2754</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">177</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">572</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">381</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">609</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">131</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">99</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">579</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">194</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">24482</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24482</span>
<span class="cline-any cline-yes">24482</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24482</span>
<span class="cline-any cline-yes">29971</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29971</span>
<span class="cline-any cline-yes">318</span>
<span class="cline-any cline-yes">318</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29653</span>
<span class="cline-any cline-yes">1020</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">956</span>
<span class="cline-any cline-yes">956</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">948</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28633</span>
<span class="cline-any cline-yes">371</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">371</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">332</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">264</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28262</span>
<span class="cline-any cline-yes">5934</span>
<span class="cline-any cline-yes">22328</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22211</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">59</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">59</span>
<span class="cline-any cline-yes">59</span>
<span class="cline-any cline-yes">174</span>
<span class="cline-any cline-yes">152</span>
<span class="cline-any cline-yes">152</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">4174</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4174</span>
<span class="cline-any cline-yes">4174</span>
<span class="cline-any cline-yes">16103</span>
<span class="cline-any cline-yes">16103</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16084</span>
<span class="cline-any cline-yes">12235</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3849</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4155</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">4203</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4203</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4203</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4167</span>
<span class="cline-any cline-yes">1369</span>
<span class="cline-any cline-yes">2798</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-yes">1358</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">1334</span>
<span class="cline-any cline-yes">153</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1181</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4167</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">6341</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6341</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5418</span>
<span class="cline-any cline-yes">5418</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">923</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">923</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33</span>
<span class="cline-any cline-yes">33</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">875</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">875</span>
<span class="cline-any cline-yes">875</span>
<span class="cline-any cline-yes">875</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">875</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">872</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">866</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">863</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">860</span>
<span class="cline-any cline-yes">167</span>
<span class="cline-any cline-yes">167</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">693</span>
<span class="cline-any cline-yes">677</span>
<span class="cline-any cline-yes">677</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-yes">611</span>
<span class="cline-any cline-yes">608</span>
<span class="cline-any cline-yes">608</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">608</span>
<span class="cline-any cline-yes">102</span>
<span class="cline-any cline-yes">32</span>
<span class="cline-any cline-yes">32</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">55</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">32</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">70</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">543</span>
<span class="cline-any cline-yes">733</span>
<span class="cline-any cline-yes">419</span>
<span class="cline-any cline-yes">419</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">314</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">546</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">78</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">72</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">546</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">530</span>
<span class="cline-any cline-yes">391</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">514</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-yes">4864</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4864</span>
<span class="cline-any cline-yes">455</span>
<span class="cline-any cline-yes">455</span>
<span class="cline-any cline-yes">4409</span>
<span class="cline-any cline-yes">121</span>
<span class="cline-any cline-yes">121</span>
<span class="cline-any cline-yes">115</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">78</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">47</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">78</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4288</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4280</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">455</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">247</span>
<span class="cline-any cline-yes">247</span>
<span class="cline-any cline-yes">247</span>
<span class="cline-any cline-yes">112</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">135</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-yes">93</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">43</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-yes">42</span>
<span class="cline-any cline-yes">42</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">67</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">13035</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13035</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13011</span>
<span class="cline-any cline-yes">1385</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11626</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11626</span>
<span class="cline-any cline-yes">2496</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9130</span>
<span class="cline-any cline-yes">471</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8659</span>
<span class="cline-any cline-yes">4203</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4456</span>
<span class="cline-any cline-yes">78</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">75</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4378</span>
<span class="cline-any cline-yes">608</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3770</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">11067</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11067</span>
<span class="cline-any cline-yes">11067</span>
<span class="cline-any cline-yes">11067</span>
<span class="cline-any cline-yes">11067</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11067</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11059</span>
<span class="cline-any cline-yes">11059</span>
<span class="cline-any cline-yes">11059</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11059</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1968</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1968</span>
<span class="cline-any cline-yes">1968</span>
<span class="cline-any cline-yes">1968</span>
<span class="cline-any cline-yes">1968</span>
<span class="cline-any cline-yes">1832</span>
<span class="cline-any cline-yes">1832</span>
<span class="cline-any cline-yes">1832</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">42</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">221</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">308</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">308</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">486</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">138</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1289</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">47</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">158</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2578</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1114</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">75</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">106</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">214</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">32</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">127</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">180</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">211</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">916</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">336</span>
<span class="cline-any cline-yes">336</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">916</span>
<span class="cline-any cline-yes">506</span>
<span class="cline-any cline-yes">506</span>
<span class="cline-any cline-yes">506</span>
<span class="cline-any cline-yes">506</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">410</span>
<span class="cline-any cline-yes">410</span>
<span class="cline-any cline-yes">410</span>
<span class="cline-any cline-yes">410</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">916</span>
<span class="cline-any cline-yes">916</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">300</span>
<span class="cline-any cline-yes">300</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">300</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">232</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">312</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">232</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">216</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">212</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">208</span>
<span class="cline-any cline-yes">88</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">84</span>
<span class="cline-any cline-yes">44</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">120</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">4378</span>
<span class="cline-any cline-yes">4378</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1225</span>
<span class="cline-any cline-yes">1225</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">28095</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">6064</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2367</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2367</span>
<span class="cline-any cline-yes">824</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1543</span>
<span class="cline-any cline-yes">1543</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1504</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1504</span>
<span class="cline-any cline-yes">552</span>
<span class="cline-any cline-yes">552</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">952</span>
<span class="cline-any cline-yes">952</span>
<span class="cline-any cline-yes">952</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">925</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">922</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">356</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">50</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">50</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">50</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">42</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">42</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">105</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">105</span>
<span class="cline-any cline-yes">105</span>
<span class="cline-any cline-yes">105</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">97</span>
<span class="cline-any cline-yes">97</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">355</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">355</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">327</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">254</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">254</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">254</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">218</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">218</span>
<span class="cline-any cline-yes">53</span>
<span class="cline-any cline-yes">53</span>
<span class="cline-any cline-yes">53</span>
<span class="cline-any cline-yes">53</span>
<span class="cline-any cline-yes">53</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">165</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">44</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">109</span>
<span class="cline-any cline-yes">109</span>
<span class="cline-any cline-yes">97</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">202</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">202</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">202</span>
<span class="cline-any cline-yes">254</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">214</span>
<span class="cline-any cline-yes">194</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">214</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">214</span>
<span class="cline-any cline-yes">214</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">160</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">174</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">174</span>
<span class="cline-any cline-yes">78</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">106</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">106</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">3183</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3183</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3183</span>
<span class="cline-any cline-yes">1373</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1810</span>
<span class="cline-any cline-yes">962</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">950</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">848</span>
<span class="cline-any cline-yes">144</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">141</span>
<span class="cline-any cline-yes">129</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">716</span>
<span class="cline-any cline-yes">113</span>
<span class="cline-any cline-yes">113</span>
<span class="cline-any cline-yes">113</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">603</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">599</span>
<span class="cline-any cline-yes">50</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">549</span>
<span class="cline-any cline-yes">202</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">347</span>
<span class="cline-any cline-yes">216</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">131</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">158</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">158</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">158</span>
<span class="cline-any cline-yes">60</span>
<span class="cline-any cline-yes">73</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">154</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">67</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">67</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">67</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">67</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1743</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1743</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1311</span>
<span class="cline-any cline-yes">89</span>
<span class="cline-any cline-yes">62</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1303</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">3183</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2743</span>
<span class="cline-any cline-yes">821</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1922</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1898</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">3270</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3270</span>
<span class="cline-any cline-yes">2440</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">830</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">778</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">758</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">743</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2691</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2691</span>
<span class="cline-any cline-yes">789</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1902</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">92</span>
<span class="cline-any cline-yes">92</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1557</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1902</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2383</span>
<span class="cline-any cline-yes">2383</span>
<span class="cline-any cline-yes">2383</span>
<span class="cline-any cline-yes">2161</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">222</span>
<span class="cline-any cline-yes">222</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">222</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">206</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">102</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">102</span>
<span class="cline-any cline-yes">102</span>
<span class="cline-any cline-yes">102</span>
<span class="cline-any cline-yes">102</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">206</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">206</span>
<span class="cline-any cline-yes">206</span>
<span class="cline-any cline-yes">206</span>
<span class="cline-any cline-yes">254</span>
<span class="cline-any cline-yes">254</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">206</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2367</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2367</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-yes">2859</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2367</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">257</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">237</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-yes">221</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2110</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2240</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1756</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1756</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-yes">401</span>
<span class="cline-any cline-yes">181</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">220</span>
<span class="cline-any cline-yes">184</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">180</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">193</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">229</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">193</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">181</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">915</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">915</span>
<span class="cline-any cline-yes">100</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">819</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">276</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">216</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">208</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-yes">177</span>
<span class="cline-any cline-yes">120</span>
<span class="cline-any cline-yes">116</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">180</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">223</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">223</span>
<span class="cline-any cline-yes">276</span>
<span class="cline-any cline-yes">180</span>
<span class="cline-any cline-yes">127</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">53</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">127</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">157</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">157</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">157</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">81</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">81</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">198</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">38</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">56</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">44</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">49</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">49</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">49</span>
<span class="cline-any cline-yes">49</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">49</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">60</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">50</span>
<span class="cline-any cline-yes">13</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37</span>
<span class="cline-any cline-yes">23</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">23</span>
<span class="cline-any cline-yes">23</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">32</span>
<span class="cline-any cline-yes">21</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-yes">15</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">42</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">17</span>
<span class="cline-any cline-yes">33</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">26</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2576</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2576</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2552</span>
<span class="cline-any cline-yes">548</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">160</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">198</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">136</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2140</span>
<span class="cline-any cline-yes">685</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">157</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">86</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1506</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1310</span>
<span class="cline-any cline-yes">59</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">59</span>
<span class="cline-any cline-yes">59</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">51</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-yes">27</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1251</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1231</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">513</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">513</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">513</span>
<span class="cline-any cline-yes">780</span>
<span class="cline-any cline-yes">494</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">286</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">286</span>
<span class="cline-any cline-yes">278</span>
<span class="cline-any cline-yes">278</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">275</span>
<span class="cline-any cline-yes">275</span>
<span class="cline-any cline-yes">261</span>
<span class="cline-any cline-yes">261</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">497</span>
<span class="cline-any cline-yes">590</span>
<span class="cline-any cline-yes">305</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">285</span>
<span class="cline-any cline-yes">97</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">93</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">309</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">305</span>
<span class="cline-any cline-yes">305</span>
<span class="cline-any cline-yes">305</span>
<span class="cline-any cline-yes">305</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">305</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">424</span>
<span class="cline-any cline-yes">424</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">424</span>
<span class="cline-any cline-yes">111</span>
<span class="cline-any cline-yes">111</span>
<span class="cline-any cline-yes">144</span>
<span class="cline-any cline-yes">144</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-yes">52</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-yes">95</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">408</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">408</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">335</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">335</span>
<span class="cline-any cline-yes">335</span>
<span class="cline-any cline-yes">335</span>
<span class="cline-any cline-yes">315</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">12</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">275</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">25</span>
<span class="cline-any cline-yes">250</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">307</span>
<span class="cline-any cline-yes">291</span>
<span class="cline-any cline-yes">291</span>
<span class="cline-any cline-yes">291</span>
<span class="cline-any cline-yes">291</span>
<span class="cline-any cline-yes">49</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">291</span>
<span class="cline-any cline-yes">291</span>
<span class="cline-any cline-yes">107</span>
<span class="cline-any cline-yes">44</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">47</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">47</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">129</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">129</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">129</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-yes">32</span>
<span class="cline-any cline-yes">16</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">117</span>
<span class="cline-any cline-yes">93</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">69</span>
<span class="cline-any cline-yes">14</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">61</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">61</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">2677</span>
<span class="cline-any cline-yes">981</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">332</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">608</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1696</span>
<span class="cline-any cline-yes">1684</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1805</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1805</span>
<span class="cline-any cline-yes">1874</span>
<span class="cline-any cline-yes">1874</span>
<span class="cline-any cline-yes">1728</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">146</span>
<span class="cline-any cline-yes">146</span>
<span class="cline-any cline-yes">146</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">143</span>
<span class="cline-any cline-yes">143</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-yes">30</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1801</span>
<span class="cline-any cline-yes">1740</span>
<span class="cline-any cline-yes">1032</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1028</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1805</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">99</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">99</span>
<span class="cline-any cline-yes">31</span>
<span class="cline-any cline-yes">22</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">77</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">3357</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3357</span>
<span class="cline-any cline-yes">3357</span>
<span class="cline-any cline-yes">3357</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3357</span>
<span class="cline-any cline-yes">5299</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5299</span>
<span class="cline-any cline-yes">391</span>
<span class="cline-any cline-yes">391</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-yes">362</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">353</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4908</span>
<span class="cline-any cline-yes">431</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">5</span>
<span class="cline-any cline-yes">5</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">34</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">397</span>
<span class="cline-any cline-yes">397</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">389</span>
<span class="cline-any cline-yes">389</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">64</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-yes">58</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4477</span>
<span class="cline-any cline-yes">135</span>
<span class="cline-any cline-yes">135</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">41</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-yes">3</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">94</span>
<span class="cline-any cline-yes">70</span>
<span class="cline-any cline-yes">70</span>
<span class="cline-any cline-yes">70</span>
<span class="cline-any cline-yes">70</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">70</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4342</span>
<span class="cline-any cline-yes">1242</span>
<span class="cline-any cline-yes">3100</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-yes">39</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3061</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-yes">38</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-yes">36</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">63</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">168</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-yes">72</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-yes">72</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">123</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-yes">12068</span>
<span class="cline-any cline-yes">12068</span>
<span class="cline-any cline-yes">12068</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-yes">4598</span>
<span class="cline-any cline-yes">4574</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4598</span>
<span class="cline-any cline-yes">4574</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13398</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">48</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-yes">126</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-yes">80</span>
<span class="cline-any cline-yes">46</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-yes">6</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1440</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">20890</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20890</span>
<span class="cline-any cline-yes">20890</span>
<span class="cline-any cline-yes">50620</span>
<span class="cline-any cline-yes">50524</span>
<span class="cline-any cline-yes">50524</span>
<span class="cline-any cline-yes">30380</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20144</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20890</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19396</span>
<span class="cline-any cline-yes">13442</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19396</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-yes">152</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-yes">128</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">752</span>
<span class="cline-any cline-yes">66</span>
<span class="cline-any cline-yes">66</span>
<span class="cline-any cline-yes">66</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-yes">776</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">752</span>
<span class="cline-any cline-yes">66</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19396</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-yes">4444</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-yes">24</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-yes">496</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11890</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">478</span>
<span class="cline-any cline-yes">478</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">478</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1933</span>
<span class="cline-any cline-yes">1933</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1933</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1933</span>
<span class="cline-any cline-yes">478</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1933</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1938</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-yes">54</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-yes">45</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-yes">68</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-yes">746</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">848</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-yes">1941</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1093</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-yes">40</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/*
  Copyright (C) 2012 Ariya Hidayat &lt;<EMAIL>&gt;
  Copyright (C) 2012 Mathias Bynens &lt;<EMAIL>&gt;
  Copyright (C) 2012 Joost-Wim Boekesteijn &lt;<EMAIL>&gt;
  Copyright (C) 2012 Kris Kowal &lt;<EMAIL>&gt;
  Copyright (C) 2012 Yusuke Suzuki &lt;<EMAIL>&gt;
  Copyright (C) 2012 Arpad Borsos &lt;<EMAIL>&gt;
  Copyright (C) 2011 Ariya Hidayat &lt;<EMAIL>&gt;
&nbsp;
  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
&nbsp;
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
&nbsp;
  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL &lt;COPYRIGHT HOLDER&gt; BE LIABLE FOR ANY
  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/
&nbsp;
/*jslint bitwise:true plusplus:true */
/*global esprima:true, define:true, exports:true, window: true,
throwError: true, generateStatement: true, peek: true,
parseAssignmentExpression: true, parseBlock: true, parseExpression: true,
parseFunctionDeclaration: true, parseFunctionExpression: true,
parseFunctionSourceElements: true, parseVariableIdentifier: true,
parseLeftHandSideExpression: true,
parseStatement: true, parseSourceElement: true */
&nbsp;
(function (root, factory) {
    'use strict';
&nbsp;
    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js,
    // Rhino, and plain browser loading.
    <span class="missing-if-branch" title="if path not taken"" >I</span>if (typeof define === 'function' &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >define.amd)</span> {
<span class="cstat-no" title="statement not covered" >        define(['exports'], factory);</span>
    } else <span class="missing-if-branch" title="else path not taken"" >E</span>if (typeof exports !== 'undefined') {
        factory(exports);
    } else {
<span class="cstat-no" title="statement not covered" >        factory((root.esprima = {}));</span>
    }
}(this, function (exports) {
    'use strict';
&nbsp;
    var Token,
        TokenName,
        Syntax,
        PropertyKind,
        Messages,
        Regex,
        SyntaxTreeDelegate,
        source,
        strict,
        index,
        lineNumber,
        lineStart,
        length,
        delegate,
        lookahead,
        state,
        extra;
&nbsp;
    Token = {
        BooleanLiteral: 1,
        EOF: 2,
        Identifier: 3,
        Keyword: 4,
        NullLiteral: 5,
        NumericLiteral: 6,
        Punctuator: 7,
        StringLiteral: 8
    };
&nbsp;
    TokenName = {};
    TokenName[Token.BooleanLiteral] = 'Boolean';
    TokenName[Token.EOF] = '&lt;end&gt;';
    TokenName[Token.Identifier] = 'Identifier';
    TokenName[Token.Keyword] = 'Keyword';
    TokenName[Token.NullLiteral] = 'Null';
    TokenName[Token.NumericLiteral] = 'Numeric';
    TokenName[Token.Punctuator] = 'Punctuator';
    TokenName[Token.StringLiteral] = 'String';
&nbsp;
    Syntax = {
        AssignmentExpression: 'AssignmentExpression',
        ArrayExpression: 'ArrayExpression',
        BlockStatement: 'BlockStatement',
        BinaryExpression: 'BinaryExpression',
        BreakStatement: 'BreakStatement',
        CallExpression: 'CallExpression',
        CatchClause: 'CatchClause',
        ConditionalExpression: 'ConditionalExpression',
        ContinueStatement: 'ContinueStatement',
        DoWhileStatement: 'DoWhileStatement',
        DebuggerStatement: 'DebuggerStatement',
        EmptyStatement: 'EmptyStatement',
        ExpressionStatement: 'ExpressionStatement',
        ForStatement: 'ForStatement',
        ForInStatement: 'ForInStatement',
        FunctionDeclaration: 'FunctionDeclaration',
        FunctionExpression: 'FunctionExpression',
        Identifier: 'Identifier',
        IfStatement: 'IfStatement',
        Literal: 'Literal',
        LabeledStatement: 'LabeledStatement',
        LogicalExpression: 'LogicalExpression',
        MemberExpression: 'MemberExpression',
        NewExpression: 'NewExpression',
        ObjectExpression: 'ObjectExpression',
        Program: 'Program',
        Property: 'Property',
        ReturnStatement: 'ReturnStatement',
        SequenceExpression: 'SequenceExpression',
        SwitchStatement: 'SwitchStatement',
        SwitchCase: 'SwitchCase',
        ThisExpression: 'ThisExpression',
        ThrowStatement: 'ThrowStatement',
        TryStatement: 'TryStatement',
        UnaryExpression: 'UnaryExpression',
        UpdateExpression: 'UpdateExpression',
        VariableDeclaration: 'VariableDeclaration',
        VariableDeclarator: 'VariableDeclarator',
        WhileStatement: 'WhileStatement',
        WithStatement: 'WithStatement'
    };
&nbsp;
    PropertyKind = {
        Data: 1,
        Get: 2,
        Set: 4
    };
&nbsp;
    // Error messages should be identical to V8.
    Messages = {
        UnexpectedToken:  'Unexpected token %0',
        UnexpectedNumber:  'Unexpected number',
        UnexpectedString:  'Unexpected string',
        UnexpectedIdentifier:  'Unexpected identifier',
        UnexpectedReserved:  'Unexpected reserved word',
        UnexpectedEOS:  'Unexpected end of input',
        NewlineAfterThrow:  'Illegal newline after throw',
        InvalidRegExp: 'Invalid regular expression',
        UnterminatedRegExp:  'Invalid regular expression: missing /',
        InvalidLHSInAssignment:  'Invalid left-hand side in assignment',
        InvalidLHSInForIn:  'Invalid left-hand side in for-in',
        MultipleDefaultsInSwitch: 'More than one default clause in switch statement',
        NoCatchOrFinally:  'Missing catch or finally after try',
        UnknownLabel: 'Undefined label \'%0\'',
        Redeclaration: '%0 \'%1\' has already been declared',
        IllegalContinue: 'Illegal continue statement',
        IllegalBreak: 'Illegal break statement',
        IllegalReturn: 'Illegal return statement',
        StrictModeWith:  'Strict mode code may not include a with statement',
        StrictCatchVariable:  'Catch variable may not be eval or arguments in strict mode',
        StrictVarName:  'Variable name may not be eval or arguments in strict mode',
        StrictParamName:  'Parameter name eval or arguments is not allowed in strict mode',
        StrictParamDupe: 'Strict mode function may not have duplicate parameter names',
        StrictFunctionName:  'Function name may not be eval or arguments in strict mode',
        StrictOctalLiteral:  'Octal literals are not allowed in strict mode.',
        StrictDelete:  'Delete of an unqualified identifier in strict mode.',
        StrictDuplicateProperty:  'Duplicate data property in object literal not allowed in strict mode',
        AccessorDataProperty:  'Object literal may not have data and accessor property with the same name',
        AccessorGetSet:  'Object literal may not have multiple get/set accessors with the same name',
        StrictLHSAssignment:  'Assignment to eval or arguments is not allowed in strict mode',
        StrictLHSPostfix:  'Postfix increment/decrement may not have eval or arguments operand in strict mode',
        StrictLHSPrefix:  'Prefix increment/decrement may not have eval or arguments operand in strict mode',
        StrictReservedWord:  'Use of future reserved word in strict mode'
    };
&nbsp;
    // See also tools/generate-unicode-regex.py.
    Regex = {
        NonAsciiIdentifierStart: new RegExp('[\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376\u0377\u037a-\u037d\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05d0-\u05ea\u05f0-\u05f2\u0620-\u064a\u066e\u066f\u0671-\u06d3\u06d5\u06e5\u06e6\u06ee\u06ef\u06fa-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07ca-\u07ea\u07f4\u07f5\u07fa\u0800-\u0815\u081a\u0824\u0828\u0840-\u0858\u08a0\u08a2-\u08ac\u0904-\u0939\u093d\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097f\u0985-\u098c\u098f\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc\u09dd\u09df-\u09e1\u09f0\u09f1\u0a05-\u0a0a\u0a0f\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32\u0a33\u0a35\u0a36\u0a38\u0a39\u0a59-\u0a5c\u0a5e\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0\u0ae1\u0b05-\u0b0c\u0b0f\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32\u0b33\u0b35-\u0b39\u0b3d\u0b5c\u0b5d\u0b5f-\u0b61\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99\u0b9a\u0b9c\u0b9e\u0b9f\u0ba3\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c33\u0c35-\u0c39\u0c3d\u0c58\u0c59\u0c60\u0c61\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0\u0ce1\u0cf1\u0cf2\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d\u0d4e\u0d60\u0d61\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0e01-\u0e30\u0e32\u0e33\u0e40-\u0e46\u0e81\u0e82\u0e84\u0e87\u0e88\u0e8a\u0e8d\u0e94-\u0e97\u0e99-\u0e9f\u0ea1-\u0ea3\u0ea5\u0ea7\u0eaa\u0eab\u0ead-\u0eb0\u0eb2\u0eb3\u0ebd\u0ec0-\u0ec4\u0ec6\u0edc-\u0edf\u0f00\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8c\u1000-\u102a\u103f\u1050-\u1055\u105a-\u105d\u1061\u1065\u1066\u106e-\u1070\u1075-\u1081\u108e\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f4\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f0\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1780-\u17b3\u17d7\u17dc\u1820-\u1877\u1880-\u18a8\u18aa\u18b0-\u18f5\u1900-\u191c\u1950-\u196d\u1970-\u1974\u1980-\u19ab\u19c1-\u19c7\u1a00-\u1a16\u1a20-\u1a54\u1aa7\u1b05-\u1b33\u1b45-\u1b4b\u1b83-\u1ba0\u1bae\u1baf\u1bba-\u1be5\u1c00-\u1c23\u1c4d-\u1c4f\u1c5a-\u1c7d\u1ce9-\u1cec\u1cee-\u1cf1\u1cf5\u1cf6\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u2071\u207f\u2090-\u209c\u2102\u2107\u210a-\u2113\u2115\u2119-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cee\u2cf2\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2e2f\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303c\u3041-\u3096\u309d-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312d\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fcc\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua61f\ua62a\ua62b\ua640-\ua66e\ua67f-\ua697\ua6a0-\ua6ef\ua717-\ua71f\ua722-\ua788\ua78b-\ua78e\ua790-\ua793\ua7a0-\ua7aa\ua7f8-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8f2-\ua8f7\ua8fb\ua90a-\ua925\ua930-\ua946\ua960-\ua97c\ua984-\ua9b2\ua9cf\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uaa60-\uaa76\uaa7a\uaa80-\uaaaf\uaab1\uaab5\uaab6\uaab9-\uaabd\uaac0\uaac2\uaadb-\uaadd\uaae0-\uaaea\uaaf2-\uaaf4\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uabc0-\uabe2\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40\ufb41\ufb43\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe70-\ufe74\ufe76-\ufefc\uff21-\uff3a\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]'),
        NonAsciiIdentifierPart: new RegExp('[\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0300-\u0374\u0376\u0377\u037a-\u037d\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u0483-\u0487\u048a-\u0527\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u05d0-\u05ea\u05f0-\u05f2\u0610-\u061a\u0620-\u0669\u066e-\u06d3\u06d5-\u06dc\u06df-\u06e8\u06ea-\u06fc\u06ff\u0710-\u074a\u074d-\u07b1\u07c0-\u07f5\u07fa\u0800-\u082d\u0840-\u085b\u08a0\u08a2-\u08ac\u08e4-\u08fe\u0900-\u0963\u0966-\u096f\u0971-\u0977\u0979-\u097f\u0981-\u0983\u0985-\u098c\u098f\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bc-\u09c4\u09c7\u09c8\u09cb-\u09ce\u09d7\u09dc\u09dd\u09df-\u09e3\u09e6-\u09f1\u0a01-\u0a03\u0a05-\u0a0a\u0a0f\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32\u0a33\u0a35\u0a36\u0a38\u0a39\u0a3c\u0a3e-\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a59-\u0a5c\u0a5e\u0a66-\u0a75\u0a81-\u0a83\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2\u0ab3\u0ab5-\u0ab9\u0abc-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ad0\u0ae0-\u0ae3\u0ae6-\u0aef\u0b01-\u0b03\u0b05-\u0b0c\u0b0f\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32\u0b33\u0b35-\u0b39\u0b3c-\u0b44\u0b47\u0b48\u0b4b-\u0b4d\u0b56\u0b57\u0b5c\u0b5d\u0b5f-\u0b63\u0b66-\u0b6f\u0b71\u0b82\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99\u0b9a\u0b9c\u0b9e\u0b9f\u0ba3\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd0\u0bd7\u0be6-\u0bef\u0c01-\u0c03\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c33\u0c35-\u0c39\u0c3d-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c58\u0c59\u0c60-\u0c63\u0c66-\u0c6f\u0c82\u0c83\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbc-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5\u0cd6\u0cde\u0ce0-\u0ce3\u0ce6-\u0cef\u0cf1\u0cf2\u0d02\u0d03\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d-\u0d44\u0d46-\u0d48\u0d4a-\u0d4e\u0d57\u0d60-\u0d63\u0d66-\u0d6f\u0d7a-\u0d7f\u0d82\u0d83\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0df2\u0df3\u0e01-\u0e3a\u0e40-\u0e4e\u0e50-\u0e59\u0e81\u0e82\u0e84\u0e87\u0e88\u0e8a\u0e8d\u0e94-\u0e97\u0e99-\u0e9f\u0ea1-\u0ea3\u0ea5\u0ea7\u0eaa\u0eab\u0ead-\u0eb9\u0ebb-\u0ebd\u0ec0-\u0ec4\u0ec6\u0ec8-\u0ecd\u0ed0-\u0ed9\u0edc-\u0edf\u0f00\u0f18\u0f19\u0f20-\u0f29\u0f35\u0f37\u0f39\u0f3e-\u0f47\u0f49-\u0f6c\u0f71-\u0f84\u0f86-\u0f97\u0f99-\u0fbc\u0fc6\u1000-\u1049\u1050-\u109d\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u135d-\u135f\u1380-\u138f\u13a0-\u13f4\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f0\u1700-\u170c\u170e-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176c\u176e-\u1770\u1772\u1773\u1780-\u17d3\u17d7\u17dc\u17dd\u17e0-\u17e9\u180b-\u180d\u1810-\u1819\u1820-\u1877\u1880-\u18aa\u18b0-\u18f5\u1900-\u191c\u1920-\u192b\u1930-\u193b\u1946-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u19d0-\u19d9\u1a00-\u1a1b\u1a20-\u1a5e\u1a60-\u1a7c\u1a7f-\u1a89\u1a90-\u1a99\u1aa7\u1b00-\u1b4b\u1b50-\u1b59\u1b6b-\u1b73\u1b80-\u1bf3\u1c00-\u1c37\u1c40-\u1c49\u1c4d-\u1c7d\u1cd0-\u1cd2\u1cd4-\u1cf6\u1d00-\u1de6\u1dfc-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u200c\u200d\u203f\u2040\u2054\u2071\u207f\u2090-\u209c\u20d0-\u20dc\u20e1\u20e5-\u20f0\u2102\u2107\u210a-\u2113\u2115\u2119-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d7f-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2de0-\u2dff\u2e2f\u3005-\u3007\u3021-\u302f\u3031-\u3035\u3038-\u303c\u3041-\u3096\u3099\u309a\u309d-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312d\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fcc\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua62b\ua640-\ua66f\ua674-\ua67d\ua67f-\ua697\ua69f-\ua6f1\ua717-\ua71f\ua722-\ua788\ua78b-\ua78e\ua790-\ua793\ua7a0-\ua7aa\ua7f8-\ua827\ua840-\ua873\ua880-\ua8c4\ua8d0-\ua8d9\ua8e0-\ua8f7\ua8fb\ua900-\ua92d\ua930-\ua953\ua960-\ua97c\ua980-\ua9c0\ua9cf-\ua9d9\uaa00-\uaa36\uaa40-\uaa4d\uaa50-\uaa59\uaa60-\uaa76\uaa7a\uaa7b\uaa80-\uaac2\uaadb-\uaadd\uaae0-\uaaef\uaaf2-\uaaf6\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uabc0-\uabea\uabec\uabed\uabf0-\uabf9\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40\ufb41\ufb43\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe00-\ufe0f\ufe20-\ufe26\ufe33\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]')
    };
&nbsp;
    // Ensure the condition is true, otherwise throw an error.
    // This is only to have a better contract semantic, i.e. another safety net
    // to catch a logic error. The condition shall be fulfilled in normal case.
    // Do NOT use this to enforce a certain condition on any user input.
&nbsp;
    function assert(condition, message) {
        <span class="missing-if-branch" title="if path not taken"" >I</span>if (!condition) {
<span class="cstat-no" title="statement not covered" >            throw new Error('ASSERT: ' + message);</span>
        }
    }
&nbsp;
    function isDecimalDigit(ch) {
        return (ch &gt;= 48 &amp;&amp; ch &lt;= 57);   // 0..9
    }
&nbsp;
    function isHexDigit(ch) {
        return '0123456789abcdefABCDEF'.indexOf(ch) &gt;= 0;
    }
&nbsp;
    function isOctalDigit(ch) {
        return '01234567'.indexOf(ch) &gt;= 0;
    }
&nbsp;
&nbsp;
    // 7.2 White Space
&nbsp;
    function isWhiteSpace(ch) {
        return (ch === 32) ||  // space
            (ch === 9) ||      // tab
            (ch === 0xB) ||
            (ch === 0xC) ||
            (ch === 0xA0) ||
            (ch &gt;= 0x1680 &amp;&amp; '\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\uFEFF'.indexOf(String.fromCharCode(ch)) &gt; 0);
    }
&nbsp;
    // 7.3 Line Terminators
&nbsp;
    function isLineTerminator(ch) {
        return (ch === 10) || (ch === 13) || (ch === 0x2028) || (ch === 0x2029);
    }
&nbsp;
    // 7.6 Identifier Names and Identifiers
&nbsp;
    function isIdentifierStart(ch) {
        return (ch === 36) || (ch === 95) ||  // $ (dollar) and _ (underscore)
            (ch &gt;= 65 &amp;&amp; ch &lt;= 90) ||         // A..Z
            (ch &gt;= 97 &amp;&amp; ch &lt;= 122) ||        // a..z
            (ch === 92) ||                    // \ (backslash)
            ((ch &gt;= 0x80) &amp;&amp; Regex.NonAsciiIdentifierStart.test(String.fromCharCode(ch)));
    }
&nbsp;
    function isIdentifierPart(ch) {
        return (ch === 36) || (ch === 95) ||  // $ (dollar) and _ (underscore)
            (ch &gt;= 65 &amp;&amp; ch &lt;= 90) ||         // A..Z
            (ch &gt;= 97 &amp;&amp; ch &lt;= 122) ||        // a..z
            (ch &gt;= 48 &amp;&amp; ch &lt;= 57) ||         // 0..9
            (ch === 92) ||                    // \ (backslash)
            ((ch &gt;= 0x80) &amp;&amp; Regex.NonAsciiIdentifierPart.test(String.fromCharCode(ch)));
    }
&nbsp;
    // 7.6.1.2 Future Reserved Words
&nbsp;
    function isFutureReservedWord(id) {
        switch (id) {
        case 'class':
        case 'enum':
        case 'export':
        case 'extends':
        case 'import':
        case 'super':
            return true;
        default:
            return false;
        }
    }
&nbsp;
    function isStrictModeReservedWord(id) {
        switch (id) {
        case 'implements':
        case 'interface':
        case 'package':
        case 'private':
        case 'protected':
        case 'public':
        case 'static':
        case 'yield':
        case 'let':
            return true;
        default:
            return false;
        }
    }
&nbsp;
    function isRestrictedWord(id) {
        return id === 'eval' || id === 'arguments';
    }
&nbsp;
    // 7.6.1.1 Keywords
&nbsp;
    function isKeyword(id) {
        if (strict &amp;&amp; isStrictModeReservedWord(id)) {
            return true;
        }
&nbsp;
        // 'const' is specialized as Keyword in V8.
        // 'yield' and 'let' are for compatiblity with SpiderMonkey and ES.next.
        // Some others are from future reserved words.
&nbsp;
        switch (id.length) {
        case 2:
            return (id === 'if') || (id === 'in') || (id === 'do');
        case 3:
            return (id === 'var') || (id === 'for') || (id === 'new') ||
                (id === 'try') || (id === 'let');
        case 4:
            return (id === 'this') || (id === 'else') || (id === 'case') ||
                (id === 'void') || (id === 'with') || (id === 'enum');
        case 5:
            return (id === 'while') || (id === 'break') || (id === 'catch') ||
                (id === 'throw') || (id === 'const') || (id === 'yield') ||
                (id === 'class') || (id === 'super');
        case 6:
            return (id === 'return') || (id === 'typeof') || (id === 'delete') ||
                (id === 'switch') || (id === 'export') || (id === 'import');
        case 7:
            return (id === 'default') || (id === 'finally') || (id === 'extends');
        case 8:
            return (id === 'function') || (id === 'continue') || (id === 'debugger');
        case 10:
            return (id === 'instanceof');
        default:
            return false;
        }
    }
&nbsp;
    // 7.4 Comments
&nbsp;
    function skipComment() {
        var ch, blockComment, lineComment;
&nbsp;
        blockComment = false;
        lineComment = false;
&nbsp;
        while (index &lt; length) {
            ch = source.charCodeAt(index);
&nbsp;
            if (lineComment) {
                ++index;
                if (isLineTerminator(ch)) {
                    lineComment = false;
                    if (ch === 13 &amp;&amp; source.charCodeAt(index) === 10) {
                        ++index;
                    }
                    ++lineNumber;
                    lineStart = index;
                }
            } else if (blockComment) {
                if (isLineTerminator(ch)) {
                    if (ch === 13 &amp;&amp; source.charCodeAt(index + 1) === 10) {
                        ++index;
                    }
                    ++lineNumber;
                    ++index;
                    lineStart = index;
                    if (index &gt;= length) {
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
                } else {
                    ch = source.charCodeAt(index++);
                    if (index &gt;= length) {
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
                    // Block comment ends with '*/' (char #42, char #47).
                    if (ch === 42) {
                        ch = source.charCodeAt(index);
                        if (ch === 47) {
                            ++index;
                            blockComment = false;
                        }
                    }
                }
            } else if (ch === 47) {
                ch = source.charCodeAt(index + 1);
                // Line comment starts with '//' (char #47, char #47).
                if (ch === 47) {
                    index += 2;
                    lineComment = true;
                } else if (ch === 42) {
                    // Block comment starts with '/*' (char #47, char #42).
                    index += 2;
                    blockComment = true;
                    if (index &gt;= length) {
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
                } else {
                    break;
                }
            } else if (isWhiteSpace(ch)) {
                ++index;
            } else if (isLineTerminator(ch)) {
                ++index;
                if (ch === 13 &amp;&amp; source.charCodeAt(index) === 10) {
                    ++index;
                }
                ++lineNumber;
                lineStart = index;
            } else {
                break;
            }
        }
    }
&nbsp;
    function scanHexEscape(prefix) {
        var i, len, ch, code = 0;
&nbsp;
        len = (prefix === 'u') ? 4 : 2;
        for (i = 0; i &lt; len; ++i) {
            if (index &lt; length &amp;&amp; isHexDigit(source[index])) {
                ch = source[index++];
                code = code * 16 + '0123456789abcdef'.indexOf(ch.toLowerCase());
            } else {
                return '';
            }
        }
        return String.fromCharCode(code);
    }
&nbsp;
    function getEscapedIdentifier() {
        var ch, id;
&nbsp;
        ch = source.charCodeAt(index++);
        id = String.fromCharCode(ch);
&nbsp;
        // '\u' (char #92, char #117) denotes an escaped character.
        if (ch === 92) {
            if (source.charCodeAt(index) !== 117) {
                throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
            }
            ++index;
            ch = scanHexEscape('u');
            if (!ch || ch === '\\' || !isIdentifierStart(ch.charCodeAt(0))) {
                throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
            }
            id = ch;
        }
&nbsp;
        while (index &lt; length) {
            ch = source.charCodeAt(index);
            if (!isIdentifierPart(ch)) {
                break;
            }
            ++index;
            id += String.fromCharCode(ch);
&nbsp;
            // '\u' (char #92, char #117) denotes an escaped character.
            if (ch === 92) {
                id = id.substr(0, id.length - 1);
                if (source.charCodeAt(index) !== 117) {
                    throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                }
                ++index;
                ch = scanHexEscape('u');
                if (!ch || ch === '\\' || !isIdentifierPart(ch.charCodeAt(0))) {
                    throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                }
                id += ch;
            }
        }
&nbsp;
        return id;
    }
&nbsp;
    function getIdentifier() {
        var start, ch;
&nbsp;
        start = index++;
        while (index &lt; length) {
            ch = source.charCodeAt(index);
            if (ch === 92) {
                // Blackslash (char #92) marks Unicode escape sequence.
                index = start;
                return getEscapedIdentifier();
            }
            if (isIdentifierPart(ch)) {
                ++index;
            } else {
                break;
            }
        }
&nbsp;
        return source.slice(start, index);
    }
&nbsp;
    function scanIdentifier() {
        var start, id, type;
&nbsp;
        start = index;
&nbsp;
        // Backslash (char #92) starts an escaped character.
        id = (source.charCodeAt(index) === 92) ? getEscapedIdentifier() : getIdentifier();
&nbsp;
        // There is no keyword or literal with only one character.
        // Thus, it must be an identifier.
        if (id.length === 1) {
            type = Token.Identifier;
        } else if (isKeyword(id)) {
            type = Token.Keyword;
        } else if (id === 'null') {
            type = Token.NullLiteral;
        } else if (id === 'true' || id === 'false') {
            type = Token.BooleanLiteral;
        } else {
            type = Token.Identifier;
        }
&nbsp;
        return {
            type: type,
            value: id,
            lineNumber: lineNumber,
            lineStart: lineStart,
            range: [start, index]
        };
    }
&nbsp;
&nbsp;
    // 7.7 Punctuators
&nbsp;
    function scanPunctuator() {
        var start = index,
            code = source.charCodeAt(index),
            code2,
            ch1 = source[index],
            ch2,
            ch3,
            ch4;
&nbsp;
        switch (code) {
&nbsp;
        // Check for most common single-character punctuators.
        case 46:   // . dot
        case 40:   // ( open bracket
        case 41:   // ) close bracket
        case 59:   // ; semicolon
        case 44:   // , comma
        case 123:  // { open curly brace
        case 125:  // } close curly brace
        case 91:   // [
        case 93:   // ]
        case 58:   // :
        case 63:   // ?
        case 126:  // ~
            ++index;
            return {
                type: Token.Punctuator,
                value: String.fromCharCode(code),
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [start, index]
            };
&nbsp;
        default:
            code2 = source.charCodeAt(index + 1);
&nbsp;
            // '=' (char #61) marks an assignment or comparison operator.
            if (code2 === 61) {
                switch (code) {
                case 37:  // %
                case 38:  // &amp;
                case 42:  // *:
                case 43:  // +
                case 45:  // -
                case 47:  // /
                case 60:  // &lt;
                case 62:  // &gt;
                case 94:  // ^
                case 124: // |
                    index += 2;
                    return {
                        type: Token.Punctuator,
                        value: String.fromCharCode(code) + String.fromCharCode(code2),
                        lineNumber: lineNumber,
                        lineStart: lineStart,
                        range: [start, index]
                    };
&nbsp;
                case 33: // !
                case 61: // =
                    index += 2;
&nbsp;
                    // !== and ===
                    if (source.charCodeAt(index) === 61) {
                        ++index;
                    }
                    return {
                        type: Token.Punctuator,
                        value: source.slice(start, index),
                        lineNumber: lineNumber,
                        lineStart: lineStart,
                        range: [start, index]
                    };
                default:
                    break;
                }
            }
            break;
        }
&nbsp;
        // Peek more characters.
&nbsp;
        ch2 = source[index + 1];
        ch3 = source[index + 2];
        ch4 = source[index + 3];
&nbsp;
        // 4-character punctuator: &gt;&gt;&gt;=
&nbsp;
        if (ch1 === '&gt;' &amp;&amp; ch2 === '&gt;' &amp;&amp; ch3 === '&gt;') {
            if (ch4 === '=') {
                index += 4;
                return {
                    type: Token.Punctuator,
                    value: '&gt;&gt;&gt;=',
                    lineNumber: lineNumber,
                    lineStart: lineStart,
                    range: [start, index]
                };
            }
        }
&nbsp;
        // 3-character punctuators: === !== &gt;&gt;&gt; &lt;&lt;= &gt;&gt;=
&nbsp;
        if (ch1 === '&gt;' &amp;&amp; ch2 === '&gt;' &amp;&amp; ch3 === '&gt;') {
            index += 3;
            return {
                type: Token.Punctuator,
                value: '&gt;&gt;&gt;',
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [start, index]
            };
        }
&nbsp;
        if (ch1 === '&lt;' &amp;&amp; ch2 === '&lt;' &amp;&amp; ch3 === '=') {
            index += 3;
            return {
                type: Token.Punctuator,
                value: '&lt;&lt;=',
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [start, index]
            };
        }
&nbsp;
        if (ch1 === '&gt;' &amp;&amp; ch2 === '&gt;' &amp;&amp; ch3 === '=') {
            index += 3;
            return {
                type: Token.Punctuator,
                value: '&gt;&gt;=',
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [start, index]
            };
        }
&nbsp;
        // Other 2-character punctuators: ++ -- &lt;&lt; &gt;&gt; &amp;&amp; ||
&nbsp;
        if (ch1 === ch2 &amp;&amp; ('+-&lt;&gt;&amp;|'.indexOf(ch1) &gt;= 0)) {
            index += 2;
            return {
                type: Token.Punctuator,
                value: ch1 + ch2,
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [start, index]
            };
        }
&nbsp;
        if ('&lt;&gt;=!+-*%&amp;|^/'.indexOf(ch1) &gt;= 0) {
            ++index;
            return {
                type: Token.Punctuator,
                value: ch1,
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [start, index]
            };
        }
&nbsp;
        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
    }
&nbsp;
    // 7.8.3 Numeric Literals
&nbsp;
    function scanNumericLiteral() {
        var number, start, ch;
&nbsp;
        ch = source[index];
        assert(isDecimalDigit(ch.charCodeAt(0)) || (ch === '.'),
            'Numeric literal must start with a decimal digit or a decimal point');
&nbsp;
        start = index;
        number = '';
        if (ch !== '.') {
            number = source[index++];
            ch = source[index];
&nbsp;
            // Hex number starts with '0x'.
            // Octal number starts with '0'.
            if (number === '0') {
                if (ch === 'x' || ch === 'X') {
                    number += source[index++];
                    while (index &lt; length) {
                        ch = source[index];
                        if (!isHexDigit(ch)) {
                            break;
                        }
                        number += source[index++];
                    }
&nbsp;
                    if (number.length &lt;= 2) {
                        // only 0x
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
&nbsp;
                    if (index &lt; length) {
                        ch = source[index];
                        if (isIdentifierStart(ch.charCodeAt(0))) {
                            throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                        }
                    }
                    return {
                        type: Token.NumericLiteral,
                        value: parseInt(number, 16),
                        lineNumber: lineNumber,
                        lineStart: lineStart,
                        range: [start, index]
                    };
                }
                if (isOctalDigit(ch)) {
                    number += source[index++];
                    while (index &lt; length) {
                        ch = source[index];
                        if (!isOctalDigit(ch)) {
                            break;
                        }
                        number += source[index++];
                    }
&nbsp;
                    if (index &lt; length) {
                        ch = source.charCodeAt(index);
                        if (isIdentifierStart(ch) || isDecimalDigit(ch)) {
                            throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                        }
                    }
                    return {
                        type: Token.NumericLiteral,
                        value: parseInt(number, 8),
                        octal: true,
                        lineNumber: lineNumber,
                        lineStart: lineStart,
                        range: [start, index]
                    };
                }
&nbsp;
                // decimal number starts with '0' such as '09' is illegal.
                if (ch &amp;&amp; isDecimalDigit(ch.charCodeAt(0))) {
                    throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                }
            }
&nbsp;
            while (index &lt; length) {
                if (!isDecimalDigit(source.charCodeAt(index))) {
                    ch = source[index];
                    break;
                }
                number += source[index++];
            }
        }
&nbsp;
        if (ch === '.') {
            number += source[index++];
            while (index &lt; length) {
                if (!isDecimalDigit(source.charCodeAt(index))) {
                    ch = source[index];
                    break;
                }
                number += source[index++];
            }
        }
&nbsp;
        if (ch === 'e' || ch === 'E') {
            number += source[index++];
&nbsp;
            ch = source[index];
            if (ch === '+' || ch === '-') {
                number += source[index++];
            }
&nbsp;
            ch = source[index];
            if (ch &amp;&amp; isDecimalDigit(ch.charCodeAt(0))) {
                number += source[index++];
                while (index &lt; length) {
                    if (!isDecimalDigit(source.charCodeAt(index))) {
                        ch = source[index];
                        break;
                    }
                    number += source[index++];
                }
            } else {
                ch = 'character ' + ch;
                if (index &gt;= length) {
                    ch = '&lt;end&gt;';
                }
                throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
            }
        }
&nbsp;
        if (index &lt; length) {
            if (isIdentifierStart(source.charCodeAt(index))) {
                throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
            }
        }
&nbsp;
        return {
            type: Token.NumericLiteral,
            value: parseFloat(number),
            lineNumber: lineNumber,
            lineStart: lineStart,
            range: [start, index]
        };
    }
&nbsp;
    // 7.8.4 String Literals
&nbsp;
    function scanStringLiteral() {
        var str = '', quote, start, ch, code, unescaped, restore, octal = false;
&nbsp;
        quote = source[index];
        assert((quote === '\'' || quote === '"'),
            'String literal must starts with a quote');
&nbsp;
        start = index;
        ++index;
&nbsp;
        while (index &lt; length) {
            ch = source[index++];
&nbsp;
            if (ch === quote) {
                quote = '';
                break;
            } else if (ch === '\\') {
                ch = source[index++];
                if (!ch || !isLineTerminator(ch.charCodeAt(0))) {
                    switch (ch) {
                    case 'n':
                        str += '\n';
                        break;
                    case 'r':
                        str += '\r';
                        break;
                    case 't':
                        str += '\t';
                        break;
                    case 'u':
                    case 'x':
                        restore = index;
                        unescaped = scanHexEscape(ch);
                        if (unescaped) {
                            str += unescaped;
                        } else {
                            index = restore;
                            str += ch;
                        }
                        break;
                    case 'b':
                        str += '\b';
                        break;
                    case 'f':
                        str += '\f';
                        break;
                    case 'v':
                        str += '\v';
                        break;
&nbsp;
                    default:
                        if (isOctalDigit(ch)) {
                            code = '01234567'.indexOf(ch);
&nbsp;
                            // \0 is not octal escape sequence
                            if (code !== 0) {
                                octal = true;
                            }
&nbsp;
                            if (index &lt; length &amp;&amp; isOctalDigit(source[index])) {
                                octal = true;
                                code = code * 8 + '01234567'.indexOf(source[index++]);
&nbsp;
                                // 3 digits are only allowed when string starts
                                // with 0, 1, 2, 3
                                if ('0123'.indexOf(ch) &gt;= 0 &amp;&amp;
                                        index &lt; length &amp;&amp;
                                        isOctalDigit(source[index])) {
                                    code = code * 8 + '01234567'.indexOf(source[index++]);
                                }
                            }
                            str += String.fromCharCode(code);
                        } else {
                            str += ch;
                        }
                        break;
                    }
                } else {
                    ++lineNumber;
                    if (ch ===  '\r' &amp;&amp; source[index] === '\n') {
                        ++index;
                    }
                }
            } else if (isLineTerminator(ch.charCodeAt(0))) {
                break;
            } else {
                str += ch;
            }
        }
&nbsp;
        if (quote !== '') {
            throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
        }
&nbsp;
        return {
            type: Token.StringLiteral,
            value: str,
            octal: octal,
            lineNumber: lineNumber,
            lineStart: lineStart,
            range: [start, index]
        };
    }
&nbsp;
    function scanRegExp() {
        var str, ch, start, pattern, flags, value, classMarker = false, restore, terminated = false;
&nbsp;
        lookahead = null;
        skipComment();
&nbsp;
        start = index;
        ch = source[index];
        assert(ch === '/', 'Regular expression literal must start with a slash');
        str = source[index++];
&nbsp;
        while (index &lt; length) {
            ch = source[index++];
            str += ch;
            if (classMarker) {
                if (ch === ']') {
                    classMarker = false;
                }
            } else {
                if (ch === '\\') {
                    ch = source[index++];
                    // ECMA-262 7.8.5
                    if (isLineTerminator(ch.charCodeAt(0))) {
                        throwError({}, Messages.UnterminatedRegExp);
                    }
                    str += ch;
                } else if (ch === '/') {
                    terminated = true;
                    break;
                } else if (ch === '[') {
                    classMarker = true;
                } else if (isLineTerminator(ch.charCodeAt(0))) {
                    throwError({}, Messages.UnterminatedRegExp);
                }
            }
        }
&nbsp;
        if (!terminated) {
            throwError({}, Messages.UnterminatedRegExp);
        }
&nbsp;
        // Exclude leading and trailing slash.
        pattern = str.substr(1, str.length - 2);
&nbsp;
        flags = '';
        while (index &lt; length) {
            ch = source[index];
            if (!isIdentifierPart(ch.charCodeAt(0))) {
                break;
            }
&nbsp;
            ++index;
            if (ch === '\\' &amp;&amp; index &lt; length) {
                ch = source[index];
                if (ch === 'u') {
                    ++index;
                    restore = index;
                    ch = scanHexEscape('u');
                    if (ch) {
                        flags += ch;
                        for (str += '\\u'; restore &lt; index; ++restore) {
                            str += source[restore];
                        }
                    } else {
                        index = restore;
                        flags += 'u';
                        str += '\\u';
                    }
                } else {
                    str += '\\';
                }
            } else {
                flags += ch;
                str += ch;
            }
        }
&nbsp;
        try {
            value = new RegExp(pattern, flags);
        } catch (e) {
            throwError({}, Messages.InvalidRegExp);
        }
&nbsp;
        peek();
&nbsp;
        return {
            literal: str,
            value: value,
            range: [start, index]
        };
    }
&nbsp;
    function isIdentifierName(token) {
        return token.type === Token.Identifier ||
            token.type === Token.Keyword ||
            token.type === Token.BooleanLiteral ||
            token.type === Token.NullLiteral;
    }
&nbsp;
    function advance() {
        var ch;
&nbsp;
        skipComment();
&nbsp;
        if (index &gt;= length) {
            return {
                type: Token.EOF,
                lineNumber: lineNumber,
                lineStart: lineStart,
                range: [index, index]
            };
        }
&nbsp;
        ch = source.charCodeAt(index);
&nbsp;
        // Very common: ( and ) and ;
        if (ch === 40 || ch === 41 || ch === 58) {
            return scanPunctuator();
        }
&nbsp;
        // String literal starts with single quote (#39) or double quote (#34).
        if (ch === 39 || ch === 34) {
            return scanStringLiteral();
        }
&nbsp;
        if (isIdentifierStart(ch)) {
            return scanIdentifier();
        }
&nbsp;
        // Dot (.) char #46 can also start a floating-point number, hence the need
        // to check the next character.
        if (ch === 46) {
            if (isDecimalDigit(source.charCodeAt(index + 1))) {
                return scanNumericLiteral();
            }
            return scanPunctuator();
        }
&nbsp;
        if (isDecimalDigit(ch)) {
            return scanNumericLiteral();
        }
&nbsp;
        return scanPunctuator();
    }
&nbsp;
    function lex() {
        var token;
&nbsp;
        token = lookahead;
        index = token.range[1];
        lineNumber = token.lineNumber;
        lineStart = token.lineStart;
&nbsp;
        lookahead = advance();
&nbsp;
        index = token.range[1];
        lineNumber = token.lineNumber;
        lineStart = token.lineStart;
&nbsp;
        return token;
    }
&nbsp;
    function peek() {
        var pos, line, start;
&nbsp;
        pos = index;
        line = lineNumber;
        start = lineStart;
        lookahead = advance();
        index = pos;
        lineNumber = line;
        lineStart = start;
    }
&nbsp;
    SyntaxTreeDelegate = {
&nbsp;
        name: 'SyntaxTree',
&nbsp;
        createArrayExpression: function (elements) {
            return {
                type: Syntax.ArrayExpression,
                elements: elements
            };
        },
&nbsp;
        createAssignmentExpression: function (operator, left, right) {
            return {
                type: Syntax.AssignmentExpression,
                operator: operator,
                left: left,
                right: right
            };
        },
&nbsp;
        createBinaryExpression: function (operator, left, right) {
            var type = (operator === '||' || operator === '&amp;&amp;') ? Syntax.LogicalExpression :
                        Syntax.BinaryExpression;
            return {
                type: type,
                operator: operator,
                left: left,
                right: right
            };
        },
&nbsp;
        createBlockStatement: function (body) {
            return {
                type: Syntax.BlockStatement,
                body: body
            };
        },
&nbsp;
        createBreakStatement: function (label) {
            return {
                type: Syntax.BreakStatement,
                label: label
            };
        },
&nbsp;
        createCallExpression: function (callee, args) {
            return {
                type: Syntax.CallExpression,
                callee: callee,
                'arguments': args
            };
        },
&nbsp;
        createCatchClause: function (param, body) {
            return {
                type: Syntax.CatchClause,
                param: param,
                body: body
            };
        },
&nbsp;
        createConditionalExpression: function (test, consequent, alternate) {
            return {
                type: Syntax.ConditionalExpression,
                test: test,
                consequent: consequent,
                alternate: alternate
            };
        },
&nbsp;
        createContinueStatement: function (label) {
            return {
                type: Syntax.ContinueStatement,
                label: label
            };
        },
&nbsp;
        createDebuggerStatement: function () {
            return {
                type: Syntax.DebuggerStatement
            };
        },
&nbsp;
        createDoWhileStatement: function (body, test) {
            return {
                type: Syntax.DoWhileStatement,
                body: body,
                test: test
            };
        },
&nbsp;
        createEmptyStatement: function () {
            return {
                type: Syntax.EmptyStatement
            };
        },
&nbsp;
        createExpressionStatement: function (expression) {
            return {
                type: Syntax.ExpressionStatement,
                expression: expression
            };
        },
&nbsp;
        createForStatement: function (init, test, update, body) {
            return {
                type: Syntax.ForStatement,
                init: init,
                test: test,
                update: update,
                body: body
            };
        },
&nbsp;
        createForInStatement: function (left, right, body) {
            return {
                type: Syntax.ForInStatement,
                left: left,
                right: right,
                body: body,
                each: false
            };
        },
&nbsp;
        createFunctionDeclaration: function (id, params, defaults, body) {
            return {
                type: Syntax.FunctionDeclaration,
                id: id,
                params: params,
                defaults: defaults,
                body: body,
                rest: null,
                generator: false,
                expression: false
            };
        },
&nbsp;
        createFunctionExpression: function (id, params, defaults, body) {
            return {
                type: Syntax.FunctionExpression,
                id: id,
                params: params,
                defaults: defaults,
                body: body,
                rest: null,
                generator: false,
                expression: false
            };
        },
&nbsp;
        createIdentifier: function (name) {
            return {
                type: Syntax.Identifier,
                name: name
            };
        },
&nbsp;
        createIfStatement: function (test, consequent, alternate) {
            return {
                type: Syntax.IfStatement,
                test: test,
                consequent: consequent,
                alternate: alternate
            };
        },
&nbsp;
        createLabeledStatement: function (label, body) {
            return {
                type: Syntax.LabeledStatement,
                label: label,
                body: body
            };
        },
&nbsp;
        createLiteral: function (token) {
            return {
                type: Syntax.Literal,
                value: token.value,
                raw: source.slice(token.range[0], token.range[1])
            };
        },
&nbsp;
        createMemberExpression: function (accessor, object, property) {
            return {
                type: Syntax.MemberExpression,
                computed: accessor === '[',
                object: object,
                property: property
            };
        },
&nbsp;
        createNewExpression: function (callee, args) {
            return {
                type: Syntax.NewExpression,
                callee: callee,
                'arguments': args
            };
        },
&nbsp;
        createObjectExpression: function (properties) {
            return {
                type: Syntax.ObjectExpression,
                properties: properties
            };
        },
&nbsp;
        createPostfixExpression: function (operator, argument) {
            return {
                type: Syntax.UpdateExpression,
                operator: operator,
                argument: argument,
                prefix: false
            };
        },
&nbsp;
        createProgram: function (body) {
            return {
                type: Syntax.Program,
                body: body
            };
        },
&nbsp;
        createProperty: function (kind, key, value) {
            return {
                type: Syntax.Property,
                key: key,
                value: value,
                kind: kind
            };
        },
&nbsp;
        createReturnStatement: function (argument) {
            return {
                type: Syntax.ReturnStatement,
                argument: argument
            };
        },
&nbsp;
        createSequenceExpression: function (expressions) {
            return {
                type: Syntax.SequenceExpression,
                expressions: expressions
            };
        },
&nbsp;
        createSwitchCase: function (test, consequent) {
            return {
                type: Syntax.SwitchCase,
                test: test,
                consequent: consequent
            };
        },
&nbsp;
        createSwitchStatement: function (discriminant, cases) {
            return {
                type: Syntax.SwitchStatement,
                discriminant: discriminant,
                cases: cases
            };
        },
&nbsp;
        createThisExpression: function () {
            return {
                type: Syntax.ThisExpression
            };
        },
&nbsp;
        createThrowStatement: function (argument) {
            return {
                type: Syntax.ThrowStatement,
                argument: argument
            };
        },
&nbsp;
        createTryStatement: function (block, guardedHandlers, handlers, finalizer) {
            return {
                type: Syntax.TryStatement,
                block: block,
                guardedHandlers: guardedHandlers,
                handlers: handlers,
                finalizer: finalizer
            };
        },
&nbsp;
        createUnaryExpression: function (operator, argument) {
            if (operator === '++' || operator === '--') {
                return {
                    type: Syntax.UpdateExpression,
                    operator: operator,
                    argument: argument,
                    prefix: true
                };
            }
            return {
                type: Syntax.UnaryExpression,
                operator: operator,
                argument: argument
            };
        },
&nbsp;
        createVariableDeclaration: function (declarations, kind) {
            return {
                type: Syntax.VariableDeclaration,
                declarations: declarations,
                kind: kind
            };
        },
&nbsp;
        createVariableDeclarator: function (id, init) {
            return {
                type: Syntax.VariableDeclarator,
                id: id,
                init: init
            };
        },
&nbsp;
        createWhileStatement: function (test, body) {
            return {
                type: Syntax.WhileStatement,
                test: test,
                body: body
            };
        },
&nbsp;
        createWithStatement: function (object, body) {
            return {
                type: Syntax.WithStatement,
                object: object,
                body: body
            };
        }
    };
&nbsp;
    // Return true if there is a line terminator before the next token.
&nbsp;
    function peekLineTerminator() {
        var pos, line, start, found;
&nbsp;
        pos = index;
        line = lineNumber;
        start = lineStart;
        skipComment();
        found = lineNumber !== line;
        index = pos;
        lineNumber = line;
        lineStart = start;
&nbsp;
        return found;
    }
&nbsp;
    // Throw an exception
&nbsp;
    function throwError(token, messageFormat) {
        var error,
            args = Array.prototype.slice.call(arguments, 2),
            msg = messageFormat.replace(
                /%(\d)/g,
                function (whole, index) {
                    assert(index &lt; args.length, 'Message reference must be in range');
                    return args[index];
                }
            );
&nbsp;
        if (typeof token.lineNumber === 'number') {
            error = new Error('Line ' + token.lineNumber + ': ' + msg);
            error.index = token.range[0];
            error.lineNumber = token.lineNumber;
            error.column = token.range[0] - lineStart + 1;
        } else {
            error = new Error('Line ' + lineNumber + ': ' + msg);
            error.index = index;
            error.lineNumber = lineNumber;
            error.column = index - lineStart + 1;
        }
&nbsp;
        error.description = msg;
        throw error;
    }
&nbsp;
    function throwErrorTolerant() {
        try {
            throwError.apply(null, arguments);
        } catch (e) {
            if (extra.errors) {
                extra.errors.push(e);
            } else {
                throw e;
            }
        }
    }
&nbsp;
&nbsp;
    // Throw an exception because of the token.
&nbsp;
    function throwUnexpected(token) {
        if (token.type === Token.EOF) {
            throwError(token, Messages.UnexpectedEOS);
        }
&nbsp;
        if (token.type === Token.NumericLiteral) {
            throwError(token, Messages.UnexpectedNumber);
        }
&nbsp;
        if (token.type === Token.StringLiteral) {
            throwError(token, Messages.UnexpectedString);
        }
&nbsp;
        if (token.type === Token.Identifier) {
            throwError(token, Messages.UnexpectedIdentifier);
        }
&nbsp;
        if (token.type === Token.Keyword) {
            if (isFutureReservedWord(token.value)) {
                throwError(token, Messages.UnexpectedReserved);
            } else if (strict &amp;&amp; isStrictModeReservedWord(token.value)) {
                throwErrorTolerant(token, Messages.StrictReservedWord);
                return;
            }
            throwError(token, Messages.UnexpectedToken, token.value);
        }
&nbsp;
        // BooleanLiteral, NullLiteral, or Punctuator.
        throwError(token, Messages.UnexpectedToken, token.value);
    }
&nbsp;
    // Expect the next token to match the specified punctuator.
    // If not, an exception will be thrown.
&nbsp;
    function expect(value) {
        var token = lex();
        if (token.type !== Token.Punctuator || token.value !== value) {
            throwUnexpected(token);
        }
    }
&nbsp;
    // Expect the next token to match the specified keyword.
    // If not, an exception will be thrown.
&nbsp;
    function expectKeyword(keyword) {
        var token = lex();
        if (token.type !== Token.Keyword || token.value !== keyword) {
            throwUnexpected(token);
        }
    }
&nbsp;
    // Return true if the next token matches the specified punctuator.
&nbsp;
    function match(value) {
        return lookahead.type === Token.Punctuator &amp;&amp; lookahead.value === value;
    }
&nbsp;
    // Return true if the next token matches the specified keyword
&nbsp;
    function matchKeyword(keyword) {
        return lookahead.type === Token.Keyword &amp;&amp; lookahead.value === keyword;
    }
&nbsp;
    // Return true if the next token is an assignment operator
&nbsp;
    function matchAssign() {
        var op;
&nbsp;
        if (lookahead.type !== Token.Punctuator) {
            return false;
        }
        op = lookahead.value;
        return op === '=' ||
            op === '*=' ||
            op === '/=' ||
            op === '%=' ||
            op === '+=' ||
            op === '-=' ||
            op === '&lt;&lt;=' ||
            op === '&gt;&gt;=' ||
            op === '&gt;&gt;&gt;=' ||
            op === '&amp;=' ||
            op === '^=' ||
            op === '|=';
    }
&nbsp;
    function consumeSemicolon() {
        var line;
&nbsp;
        // Catch the very common case first: immediately a semicolon (char #59).
        if (source.charCodeAt(index) === 59) {
            lex();
            return;
        }
&nbsp;
        line = lineNumber;
        skipComment();
        if (lineNumber !== line) {
            return;
        }
&nbsp;
        if (match(';')) {
            lex();
            return;
        }
&nbsp;
        if (lookahead.type !== Token.EOF &amp;&amp; !match('}')) {
            throwUnexpected(lookahead);
        }
    }
&nbsp;
    // Return true if provided expression is LeftHandSideExpression
&nbsp;
    function isLeftHandSide(expr) {
        return expr.type === Syntax.Identifier || expr.type === Syntax.MemberExpression;
    }
&nbsp;
    // 11.1.4 Array Initialiser
&nbsp;
    function parseArrayInitialiser() {
        var elements = [];
&nbsp;
        expect('[');
&nbsp;
        while (!match(']')) {
            if (match(',')) {
                lex();
                elements.push(null);
            } else {
                elements.push(parseAssignmentExpression());
&nbsp;
                if (!match(']')) {
                    expect(',');
                }
            }
        }
&nbsp;
        expect(']');
&nbsp;
        return delegate.createArrayExpression(elements);
    }
&nbsp;
    // 11.1.5 Object Initialiser
&nbsp;
    function parsePropertyFunction(param, first) {
        var previousStrict, body;
&nbsp;
        previousStrict = strict;
        body = parseFunctionSourceElements();
        if (first &amp;&amp; strict &amp;&amp; isRestrictedWord(param[0].name)) {
            throwErrorTolerant(first, Messages.StrictParamName);
        }
        strict = previousStrict;
        return delegate.createFunctionExpression(null, param, [], body);
    }
&nbsp;
    function parseObjectPropertyKey() {
        var token = lex();
&nbsp;
        // Note: This function is called only from parseObjectProperty(), where
        // EOF and Punctuator tokens are already filtered out.
&nbsp;
        if (token.type === Token.StringLiteral || token.type === Token.NumericLiteral) {
            if (strict &amp;&amp; token.octal) {
                throwErrorTolerant(token, Messages.StrictOctalLiteral);
            }
            return delegate.createLiteral(token);
        }
&nbsp;
        return delegate.createIdentifier(token.value);
    }
&nbsp;
    function parseObjectProperty() {
        var token, key, id, value, param;
&nbsp;
        token = lookahead;
&nbsp;
        if (token.type === Token.Identifier) {
&nbsp;
            id = parseObjectPropertyKey();
&nbsp;
            // Property Assignment: Getter and Setter.
&nbsp;
            if (token.value === 'get' &amp;&amp; !match(':')) {
                key = parseObjectPropertyKey();
                expect('(');
                expect(')');
                value = parsePropertyFunction([]);
                return delegate.createProperty('get', key, value);
            }
            if (token.value === 'set' &amp;&amp; !match(':')) {
                key = parseObjectPropertyKey();
                expect('(');
                token = lookahead;
                if (token.type !== Token.Identifier) {
                    throwUnexpected(lex());
                }
                param = [ parseVariableIdentifier() ];
                expect(')');
                value = parsePropertyFunction(param, token);
                return delegate.createProperty('set', key, value);
            }
            expect(':');
            value = parseAssignmentExpression();
            return delegate.createProperty('init', id, value);
        }
        if (token.type === Token.EOF || token.type === Token.Punctuator) {
            throwUnexpected(token);
        } else {
            key = parseObjectPropertyKey();
            expect(':');
            value = parseAssignmentExpression();
            return delegate.createProperty('init', key, value);
        }
    }
&nbsp;
    function parseObjectInitialiser() {
        var properties = [], property, name, key, kind, map = {}, toString = String;
&nbsp;
        expect('{');
&nbsp;
        while (!match('}')) {
            property = parseObjectProperty();
&nbsp;
            if (property.key.type === Syntax.Identifier) {
                name = property.key.name;
            } else {
                name = toString(property.key.value);
            }
            kind = (property.kind === 'init') ? PropertyKind.Data : (property.kind === 'get') ? PropertyKind.Get : PropertyKind.Set;
&nbsp;
            key = '$' + name;
            if (Object.prototype.hasOwnProperty.call(map, key)) {
                if (map[key] === PropertyKind.Data) {
                    if (strict &amp;&amp; kind === PropertyKind.Data) {
                        throwErrorTolerant({}, Messages.StrictDuplicateProperty);
                    } else if (kind !== PropertyKind.Data) {
                        throwErrorTolerant({}, Messages.AccessorDataProperty);
                    }
                } else {
                    if (kind === PropertyKind.Data) {
                        throwErrorTolerant({}, Messages.AccessorDataProperty);
                    } else if (map[key] &amp; kind) {
                        throwErrorTolerant({}, Messages.AccessorGetSet);
                    }
                }
                map[key] |= kind;
            } else {
                map[key] = kind;
            }
&nbsp;
            properties.push(property);
&nbsp;
            if (!match('}')) {
                expect(',');
            }
        }
&nbsp;
        expect('}');
&nbsp;
        return delegate.createObjectExpression(properties);
    }
&nbsp;
    // 11.1.6 The Grouping Operator
&nbsp;
    function parseGroupExpression() {
        var expr;
&nbsp;
        expect('(');
&nbsp;
        expr = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        return expr;
    }
&nbsp;
&nbsp;
    // 11.1 Primary Expressions
&nbsp;
    function parsePrimaryExpression() {
        var type, token;
&nbsp;
        type = lookahead.type;
&nbsp;
        if (type === Token.Identifier) {
            return delegate.createIdentifier(lex().value);
        }
&nbsp;
        if (type === Token.StringLiteral || type === Token.NumericLiteral) {
            if (strict &amp;&amp; lookahead.octal) {
                throwErrorTolerant(lookahead, Messages.StrictOctalLiteral);
            }
            return delegate.createLiteral(lex());
        }
&nbsp;
        if (type === Token.Keyword) {
            if (matchKeyword('this')) {
                lex();
                return delegate.createThisExpression();
            }
&nbsp;
            if (matchKeyword('function')) {
                return parseFunctionExpression();
            }
        }
&nbsp;
        if (type === Token.BooleanLiteral) {
            token = lex();
            token.value = (token.value === 'true');
            return delegate.createLiteral(token);
        }
&nbsp;
        if (type === Token.NullLiteral) {
            token = lex();
            token.value = null;
            return delegate.createLiteral(token);
        }
&nbsp;
        if (match('[')) {
            return parseArrayInitialiser();
        }
&nbsp;
        if (match('{')) {
            return parseObjectInitialiser();
        }
&nbsp;
        if (match('(')) {
            return parseGroupExpression();
        }
&nbsp;
        if (match('/') || match('/=')) {
            return delegate.createLiteral(scanRegExp());
        }
&nbsp;
        return throwUnexpected(lex());
    }
&nbsp;
    // 11.2 Left-Hand-Side Expressions
&nbsp;
    function parseArguments() {
        var args = [];
&nbsp;
        expect('(');
&nbsp;
        if (!match(')')) {
            while (index &lt; length) {
                args.push(parseAssignmentExpression());
                if (match(')')) {
                    break;
                }
                expect(',');
            }
        }
&nbsp;
        expect(')');
&nbsp;
        return args;
    }
&nbsp;
    function parseNonComputedProperty() {
        var token = lex();
&nbsp;
        if (!isIdentifierName(token)) {
            throwUnexpected(token);
        }
&nbsp;
        return delegate.createIdentifier(token.value);
    }
&nbsp;
    function parseNonComputedMember() {
        expect('.');
&nbsp;
        return parseNonComputedProperty();
    }
&nbsp;
    function parseComputedMember() {
        var expr;
&nbsp;
        expect('[');
&nbsp;
        expr = parseExpression();
&nbsp;
        expect(']');
&nbsp;
        return expr;
    }
&nbsp;
    function parseNewExpression() {
        var callee, args;
&nbsp;
        expectKeyword('new');
        callee = parseLeftHandSideExpression();
        args = match('(') ? parseArguments() : [];
&nbsp;
        return delegate.createNewExpression(callee, args);
    }
&nbsp;
    function parseLeftHandSideExpressionAllowCall() {
        var expr, args, property;
&nbsp;
        expr = matchKeyword('new') ? parseNewExpression() : parsePrimaryExpression();
&nbsp;
        while (match('.') || match('[') || match('(')) {
            if (match('(')) {
                args = parseArguments();
                expr = delegate.createCallExpression(expr, args);
            } else if (match('[')) {
                property = parseComputedMember();
                expr = delegate.createMemberExpression('[', expr, property);
            } else {
                property = parseNonComputedMember();
                expr = delegate.createMemberExpression('.', expr, property);
            }
        }
&nbsp;
        return expr;
    }
&nbsp;
&nbsp;
    function parseLeftHandSideExpression() {
        var expr, property;
&nbsp;
        expr = matchKeyword('new') ? parseNewExpression() : parsePrimaryExpression();
&nbsp;
        while (match('.') || match('[')) {
            if (match('[')) {
                property = parseComputedMember();
                expr = delegate.createMemberExpression('[', expr, property);
            } else {
                property = parseNonComputedMember();
                expr = delegate.createMemberExpression('.', expr, property);
            }
        }
&nbsp;
        return expr;
    }
&nbsp;
    // 11.3 Postfix Expressions
&nbsp;
    function parsePostfixExpression() {
        var expr = parseLeftHandSideExpressionAllowCall(), token;
&nbsp;
        if (lookahead.type !== Token.Punctuator) {
            return expr;
        }
&nbsp;
        if ((match('++') || match('--')) &amp;&amp; !peekLineTerminator()) {
            // 11.3.1, 11.3.2
            if (strict &amp;&amp; expr.type === Syntax.Identifier &amp;&amp; isRestrictedWord(expr.name)) {
                throwErrorTolerant({}, Messages.StrictLHSPostfix);
            }
&nbsp;
            if (!isLeftHandSide(expr)) {
                throwError({}, Messages.InvalidLHSInAssignment);
            }
&nbsp;
            token = lex();
            expr = delegate.createPostfixExpression(token.value, expr);
        }
&nbsp;
        return expr;
    }
&nbsp;
    // 11.4 Unary Operators
&nbsp;
    function parseUnaryExpression() {
        var token, expr;
&nbsp;
        if (lookahead.type !== Token.Punctuator &amp;&amp; lookahead.type !== Token.Keyword) {
            return parsePostfixExpression();
        }
&nbsp;
        if (match('++') || match('--')) {
            token = lex();
            expr = parseUnaryExpression();
            // 11.4.4, 11.4.5
            if (strict &amp;&amp; expr.type === Syntax.Identifier &amp;&amp; isRestrictedWord(expr.name)) {
                throwErrorTolerant({}, Messages.StrictLHSPrefix);
            }
&nbsp;
            if (!isLeftHandSide(expr)) {
                throwError({}, Messages.InvalidLHSInAssignment);
            }
&nbsp;
            return delegate.createUnaryExpression(token.value, expr);
        }
&nbsp;
        if (match('+') || match('-') || match('~') || match('!')) {
            token = lex();
            expr = parseUnaryExpression();
            return delegate.createUnaryExpression(token.value, expr);
        }
&nbsp;
        if (matchKeyword('delete') || matchKeyword('void') || matchKeyword('typeof')) {
            token = lex();
            expr = parseUnaryExpression();
            expr = delegate.createUnaryExpression(token.value, expr);
            if (strict &amp;&amp; expr.operator === 'delete' &amp;&amp; expr.argument.type === Syntax.Identifier) {
                throwErrorTolerant({}, Messages.StrictDelete);
            }
            return expr;
        }
&nbsp;
        return parsePostfixExpression();
    }
&nbsp;
    function binaryPrecedence(token, allowIn) {
        var prec = 0;
&nbsp;
        if (token.type !== Token.Punctuator &amp;&amp; token.type !== Token.Keyword) {
            return 0;
        }
&nbsp;
        switch (token.value) {
        case '||':
            prec = 1;
            break;
&nbsp;
        case '&amp;&amp;':
            prec = 2;
            break;
&nbsp;
        case '|':
            prec = 3;
            break;
&nbsp;
        case '^':
            prec = 4;
            break;
&nbsp;
        case '&amp;':
            prec = 5;
            break;
&nbsp;
        case '==':
        case '!=':
        case '===':
        case '!==':
            prec = 6;
            break;
&nbsp;
        case '&lt;':
        case '&gt;':
        case '&lt;=':
        case '&gt;=':
        case 'instanceof':
            prec = 7;
            break;
&nbsp;
        case 'in':
            prec = allowIn ? 7 : 0;
            break;
&nbsp;
        case '&lt;&lt;':
        case '&gt;&gt;':
        case '&gt;&gt;&gt;':
            prec = 8;
            break;
&nbsp;
        case '+':
        case '-':
            prec = 9;
            break;
&nbsp;
        case '*':
        case '/':
        case '%':
            prec = 11;
            break;
&nbsp;
        default:
            break;
        }
&nbsp;
        return prec;
    }
&nbsp;
    // 11.5 Multiplicative Operators
    // 11.6 Additive Operators
    // 11.7 Bitwise Shift Operators
    // 11.8 Relational Operators
    // 11.9 Equality Operators
    // 11.10 Binary Bitwise Operators
    // 11.11 Binary Logical Operators
&nbsp;
    function parseBinaryExpression() {
        var expr, token, prec, previousAllowIn, stack, right, operator, left, i;
&nbsp;
        previousAllowIn = state.allowIn;
        state.allowIn = true;
&nbsp;
        expr = parseUnaryExpression();
&nbsp;
        token = lookahead;
        prec = binaryPrecedence(token, previousAllowIn);
        if (prec === 0) {
            return expr;
        }
        token.prec = prec;
        lex();
&nbsp;
        stack = [expr, token, parseUnaryExpression()];
&nbsp;
        while ((prec = binaryPrecedence(lookahead, previousAllowIn)) &gt; 0) {
&nbsp;
            // Reduce: make a binary expression from the three topmost entries.
            while ((stack.length &gt; 2) &amp;&amp; (prec &lt;= stack[stack.length - 2].prec)) {
                right = stack.pop();
                operator = stack.pop().value;
                left = stack.pop();
                stack.push(delegate.createBinaryExpression(operator, left, right));
            }
&nbsp;
            // Shift.
            token = lex();
            token.prec = prec;
            stack.push(token);
            stack.push(parseUnaryExpression());
        }
&nbsp;
        state.allowIn = previousAllowIn;
&nbsp;
        // Final reduce to clean-up the stack.
        i = stack.length - 1;
        expr = stack[i];
        while (i &gt; 1) {
            expr = delegate.createBinaryExpression(stack[i - 1].value, stack[i - 2], expr);
            i -= 2;
        }
        return expr;
    }
&nbsp;
&nbsp;
    // 11.12 Conditional Operator
&nbsp;
    function parseConditionalExpression() {
        var expr, previousAllowIn, consequent, alternate;
&nbsp;
        expr = parseBinaryExpression();
&nbsp;
        if (match('?')) {
            lex();
            previousAllowIn = state.allowIn;
            state.allowIn = true;
            consequent = parseAssignmentExpression();
            state.allowIn = previousAllowIn;
            expect(':');
            alternate = parseAssignmentExpression();
&nbsp;
            expr = delegate.createConditionalExpression(expr, consequent, alternate);
        }
&nbsp;
        return expr;
    }
&nbsp;
    // 11.13 Assignment Operators
&nbsp;
    function parseAssignmentExpression() {
        var token, left, right;
&nbsp;
        token = lookahead;
        left = parseConditionalExpression();
&nbsp;
        if (matchAssign()) {
            // LeftHandSideExpression
            if (!isLeftHandSide(left)) {
                throwError({}, Messages.InvalidLHSInAssignment);
            }
&nbsp;
            // 11.13.1
            if (strict &amp;&amp; left.type === Syntax.Identifier &amp;&amp; isRestrictedWord(left.name)) {
                throwErrorTolerant(token, Messages.StrictLHSAssignment);
            }
&nbsp;
            token = lex();
            right = parseAssignmentExpression();
            return delegate.createAssignmentExpression(token.value, left, right);
        }
&nbsp;
        return left;
    }
&nbsp;
    // 11.14 Comma Operator
&nbsp;
    function parseExpression() {
        var expr = parseAssignmentExpression();
&nbsp;
        if (match(',')) {
            expr = delegate.createSequenceExpression([ expr ]);
&nbsp;
            while (index &lt; length) {
                if (!match(',')) {
                    break;
                }
                lex();
                expr.expressions.push(parseAssignmentExpression());
            }
&nbsp;
        }
        return expr;
    }
&nbsp;
    // 12.1 Block
&nbsp;
    function parseStatementList() {
        var list = [],
            statement;
&nbsp;
        while (index &lt; length) {
            if (match('}')) {
                break;
            }
            statement = parseSourceElement();
            if (typeof statement === 'undefined') {
                break;
            }
            list.push(statement);
        }
&nbsp;
        return list;
    }
&nbsp;
    function parseBlock() {
        var block;
&nbsp;
        expect('{');
&nbsp;
        block = parseStatementList();
&nbsp;
        expect('}');
&nbsp;
        return delegate.createBlockStatement(block);
    }
&nbsp;
    // 12.2 Variable Statement
&nbsp;
    function parseVariableIdentifier() {
        var token = lex();
&nbsp;
        if (token.type !== Token.Identifier) {
            throwUnexpected(token);
        }
&nbsp;
        return delegate.createIdentifier(token.value);
    }
&nbsp;
    function parseVariableDeclaration(kind) {
        var id = parseVariableIdentifier(),
            init = null;
&nbsp;
        // 12.2.1
        if (strict &amp;&amp; isRestrictedWord(id.name)) {
            throwErrorTolerant({}, Messages.StrictVarName);
        }
&nbsp;
        if (kind === 'const') {
            expect('=');
            init = parseAssignmentExpression();
        } else if (match('=')) {
            lex();
            init = parseAssignmentExpression();
        }
&nbsp;
        return delegate.createVariableDeclarator(id, init);
    }
&nbsp;
    function parseVariableDeclarationList(kind) {
        var list = [];
&nbsp;
        do {
            list.push(parseVariableDeclaration(kind));
            if (!match(',')) {
                break;
            }
            lex();
        } while (index &lt; length);
&nbsp;
        return list;
    }
&nbsp;
    function parseVariableStatement() {
        var declarations;
&nbsp;
        expectKeyword('var');
&nbsp;
        declarations = parseVariableDeclarationList();
&nbsp;
        consumeSemicolon();
&nbsp;
        return delegate.createVariableDeclaration(declarations, 'var');
    }
&nbsp;
    // kind may be `const` or `let`
    // Both are experimental and not in the specification yet.
    // see http://wiki.ecmascript.org/doku.php?id=harmony:const
    // and http://wiki.ecmascript.org/doku.php?id=harmony:let
    function parseConstLetDeclaration(kind) {
        var declarations;
&nbsp;
        expectKeyword(kind);
&nbsp;
        declarations = parseVariableDeclarationList(kind);
&nbsp;
        consumeSemicolon();
&nbsp;
        return delegate.createVariableDeclaration(declarations, kind);
    }
&nbsp;
    // 12.3 Empty Statement
&nbsp;
    function parseEmptyStatement() {
        expect(';');
        return delegate.createEmptyStatement();
    }
&nbsp;
    // 12.4 Expression Statement
&nbsp;
    function parseExpressionStatement() {
        var expr = parseExpression();
        consumeSemicolon();
        return delegate.createExpressionStatement(expr);
    }
&nbsp;
    // 12.5 If statement
&nbsp;
    function parseIfStatement() {
        var test, consequent, alternate;
&nbsp;
        expectKeyword('if');
&nbsp;
        expect('(');
&nbsp;
        test = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        consequent = parseStatement();
&nbsp;
        if (matchKeyword('else')) {
            lex();
            alternate = parseStatement();
        } else {
            alternate = null;
        }
&nbsp;
        return delegate.createIfStatement(test, consequent, alternate);
    }
&nbsp;
    // 12.6 Iteration Statements
&nbsp;
    function parseDoWhileStatement() {
        var body, test, oldInIteration;
&nbsp;
        expectKeyword('do');
&nbsp;
        oldInIteration = state.inIteration;
        state.inIteration = true;
&nbsp;
        body = parseStatement();
&nbsp;
        state.inIteration = oldInIteration;
&nbsp;
        expectKeyword('while');
&nbsp;
        expect('(');
&nbsp;
        test = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        if (match(';')) {
            lex();
        }
&nbsp;
        return delegate.createDoWhileStatement(body, test);
    }
&nbsp;
    function parseWhileStatement() {
        var test, body, oldInIteration;
&nbsp;
        expectKeyword('while');
&nbsp;
        expect('(');
&nbsp;
        test = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        oldInIteration = state.inIteration;
        state.inIteration = true;
&nbsp;
        body = parseStatement();
&nbsp;
        state.inIteration = oldInIteration;
&nbsp;
        return delegate.createWhileStatement(test, body);
    }
&nbsp;
    function parseForVariableDeclaration() {
        var token = lex(),
            declarations = parseVariableDeclarationList();
&nbsp;
        return delegate.createVariableDeclaration(declarations, token.value);
    }
&nbsp;
    function parseForStatement() {
        var init, test, update, left, right, body, oldInIteration;
&nbsp;
        init = test = update = null;
&nbsp;
        expectKeyword('for');
&nbsp;
        expect('(');
&nbsp;
        if (match(';')) {
            lex();
        } else {
            if (matchKeyword('var') || matchKeyword('let')) {
                state.allowIn = false;
                init = parseForVariableDeclaration();
                state.allowIn = true;
&nbsp;
                if (init.declarations.length === 1 &amp;&amp; matchKeyword('in')) {
                    lex();
                    left = init;
                    right = parseExpression();
                    init = null;
                }
            } else {
                state.allowIn = false;
                init = parseExpression();
                state.allowIn = true;
&nbsp;
                if (matchKeyword('in')) {
                    // LeftHandSideExpression
                    if (!isLeftHandSide(init)) {
                        throwError({}, Messages.InvalidLHSInForIn);
                    }
&nbsp;
                    lex();
                    left = init;
                    right = parseExpression();
                    init = null;
                }
            }
&nbsp;
            if (typeof left === 'undefined') {
                expect(';');
            }
        }
&nbsp;
        if (typeof left === 'undefined') {
&nbsp;
            if (!match(';')) {
                test = parseExpression();
            }
            expect(';');
&nbsp;
            if (!match(')')) {
                update = parseExpression();
            }
        }
&nbsp;
        expect(')');
&nbsp;
        oldInIteration = state.inIteration;
        state.inIteration = true;
&nbsp;
        body = parseStatement();
&nbsp;
        state.inIteration = oldInIteration;
&nbsp;
        return (typeof left === 'undefined') ?
                delegate.createForStatement(init, test, update, body) :
                delegate.createForInStatement(left, right, body);
    }
&nbsp;
    // 12.7 The continue statement
&nbsp;
    function parseContinueStatement() {
        var label = null, key;
&nbsp;
        expectKeyword('continue');
&nbsp;
        // Optimize the most common form: 'continue;'.
        if (source.charCodeAt(index) === 59) {
            lex();
&nbsp;
            if (!state.inIteration) {
                throwError({}, Messages.IllegalContinue);
            }
&nbsp;
            return delegate.createContinueStatement(null);
        }
&nbsp;
        if (peekLineTerminator()) {
            if (!state.inIteration) {
                throwError({}, Messages.IllegalContinue);
            }
&nbsp;
            return delegate.createContinueStatement(null);
        }
&nbsp;
        if (lookahead.type === Token.Identifier) {
            label = parseVariableIdentifier();
&nbsp;
            key = '$' + label.name;
            if (!Object.prototype.hasOwnProperty.call(state.labelSet, key)) {
                throwError({}, Messages.UnknownLabel, label.name);
            }
        }
&nbsp;
        consumeSemicolon();
&nbsp;
        if (label === null &amp;&amp; !state.inIteration) {
            throwError({}, Messages.IllegalContinue);
        }
&nbsp;
        return delegate.createContinueStatement(label);
    }
&nbsp;
    // 12.8 The break statement
&nbsp;
    function parseBreakStatement() {
        var label = null, key;
&nbsp;
        expectKeyword('break');
&nbsp;
        // Catch the very common case first: immediately a semicolon (char #59).
        if (source.charCodeAt(index) === 59) {
            lex();
&nbsp;
            if (!(state.inIteration || state.inSwitch)) {
                throwError({}, Messages.IllegalBreak);
            }
&nbsp;
            return delegate.createBreakStatement(null);
        }
&nbsp;
        if (peekLineTerminator()) {
            if (!(state.inIteration || state.inSwitch)) {
                throwError({}, Messages.IllegalBreak);
            }
&nbsp;
            return delegate.createBreakStatement(null);
        }
&nbsp;
        if (lookahead.type === Token.Identifier) {
            label = parseVariableIdentifier();
&nbsp;
            key = '$' + label.name;
            if (!Object.prototype.hasOwnProperty.call(state.labelSet, key)) {
                throwError({}, Messages.UnknownLabel, label.name);
            }
        }
&nbsp;
        consumeSemicolon();
&nbsp;
        if (label === null &amp;&amp; !(state.inIteration || state.inSwitch)) {
            throwError({}, Messages.IllegalBreak);
        }
&nbsp;
        return delegate.createBreakStatement(label);
    }
&nbsp;
    // 12.9 The return statement
&nbsp;
    function parseReturnStatement() {
        var argument = null;
&nbsp;
        expectKeyword('return');
&nbsp;
        if (!state.inFunctionBody) {
            throwErrorTolerant({}, Messages.IllegalReturn);
        }
&nbsp;
        // 'return' followed by a space and an identifier is very common.
        if (source.charCodeAt(index) === 32) {
            if (isIdentifierStart(source.charCodeAt(index + 1))) {
                argument = parseExpression();
                consumeSemicolon();
                return delegate.createReturnStatement(argument);
            }
        }
&nbsp;
        if (peekLineTerminator()) {
            return delegate.createReturnStatement(null);
        }
&nbsp;
        if (!match(';')) {
            if (!match('}') &amp;&amp; lookahead.type !== Token.EOF) {
                argument = parseExpression();
            }
        }
&nbsp;
        consumeSemicolon();
&nbsp;
        return delegate.createReturnStatement(argument);
    }
&nbsp;
    // 12.10 The with statement
&nbsp;
    function parseWithStatement() {
        var object, body;
&nbsp;
        if (strict) {
            throwErrorTolerant({}, Messages.StrictModeWith);
        }
&nbsp;
        expectKeyword('with');
&nbsp;
        expect('(');
&nbsp;
        object = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        body = parseStatement();
&nbsp;
        return delegate.createWithStatement(object, body);
    }
&nbsp;
    // 12.10 The swith statement
&nbsp;
    function parseSwitchCase() {
        var test,
            consequent = [],
            statement;
&nbsp;
        if (matchKeyword('default')) {
            lex();
            test = null;
        } else {
            expectKeyword('case');
            test = parseExpression();
        }
        expect(':');
&nbsp;
        while (index &lt; length) {
            if (match('}') || matchKeyword('default') || matchKeyword('case')) {
                break;
            }
            statement = parseStatement();
            consequent.push(statement);
        }
&nbsp;
        return delegate.createSwitchCase(test, consequent);
    }
&nbsp;
    function parseSwitchStatement() {
        var discriminant, cases, clause, oldInSwitch, defaultFound;
&nbsp;
        expectKeyword('switch');
&nbsp;
        expect('(');
&nbsp;
        discriminant = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        expect('{');
&nbsp;
        if (match('}')) {
            lex();
            return delegate.createSwitchStatement(discriminant);
        }
&nbsp;
        cases = [];
&nbsp;
        oldInSwitch = state.inSwitch;
        state.inSwitch = true;
        defaultFound = false;
&nbsp;
        while (index &lt; length) {
            if (match('}')) {
                break;
            }
            clause = parseSwitchCase();
            if (clause.test === null) {
                if (defaultFound) {
                    throwError({}, Messages.MultipleDefaultsInSwitch);
                }
                defaultFound = true;
            }
            cases.push(clause);
        }
&nbsp;
        state.inSwitch = oldInSwitch;
&nbsp;
        expect('}');
&nbsp;
        return delegate.createSwitchStatement(discriminant, cases);
    }
&nbsp;
    // 12.13 The throw statement
&nbsp;
    function parseThrowStatement() {
        var argument;
&nbsp;
        expectKeyword('throw');
&nbsp;
        if (peekLineTerminator()) {
            throwError({}, Messages.NewlineAfterThrow);
        }
&nbsp;
        argument = parseExpression();
&nbsp;
        consumeSemicolon();
&nbsp;
        return delegate.createThrowStatement(argument);
    }
&nbsp;
    // 12.14 The try statement
&nbsp;
    function parseCatchClause() {
        var param, body;
&nbsp;
        expectKeyword('catch');
&nbsp;
        expect('(');
        if (match(')')) {
            throwUnexpected(lookahead);
        }
&nbsp;
        param = parseExpression();
        // 12.14.1
        if (strict &amp;&amp; param.type === Syntax.Identifier &amp;&amp; isRestrictedWord(param.name)) {
            throwErrorTolerant({}, Messages.StrictCatchVariable);
        }
&nbsp;
        expect(')');
        body = parseBlock();
        return delegate.createCatchClause(param, body);
    }
&nbsp;
    function parseTryStatement() {
        var block, handlers = [], finalizer = null;
&nbsp;
        expectKeyword('try');
&nbsp;
        block = parseBlock();
&nbsp;
        if (matchKeyword('catch')) {
            handlers.push(parseCatchClause());
        }
&nbsp;
        if (matchKeyword('finally')) {
            lex();
            finalizer = parseBlock();
        }
&nbsp;
        if (handlers.length === 0 &amp;&amp; !finalizer) {
            throwError({}, Messages.NoCatchOrFinally);
        }
&nbsp;
        return delegate.createTryStatement(block, [], handlers, finalizer);
    }
&nbsp;
    // 12.15 The debugger statement
&nbsp;
    function parseDebuggerStatement() {
        expectKeyword('debugger');
&nbsp;
        consumeSemicolon();
&nbsp;
        return delegate.createDebuggerStatement();
    }
&nbsp;
    // 12 Statements
&nbsp;
    function parseStatement() {
        var type = lookahead.type,
            expr,
            labeledBody,
            key;
&nbsp;
        if (type === Token.EOF) {
            throwUnexpected(lookahead);
        }
&nbsp;
        if (type === Token.Punctuator) {
            switch (lookahead.value) {
            case ';':
                return parseEmptyStatement();
            case '{':
                return parseBlock();
            case '(':
                return parseExpressionStatement();
            default:
                break;
            }
        }
&nbsp;
        if (type === Token.Keyword) {
            switch (lookahead.value) {
            case 'break':
                return parseBreakStatement();
            case 'continue':
                return parseContinueStatement();
            case 'debugger':
                return parseDebuggerStatement();
            case 'do':
                return parseDoWhileStatement();
            case 'for':
                return parseForStatement();
            case 'function':
                return parseFunctionDeclaration();
            case 'if':
                return parseIfStatement();
            case 'return':
                return parseReturnStatement();
            case 'switch':
                return parseSwitchStatement();
            case 'throw':
                return parseThrowStatement();
            case 'try':
                return parseTryStatement();
            case 'var':
                return parseVariableStatement();
            case 'while':
                return parseWhileStatement();
            case 'with':
                return parseWithStatement();
            default:
                break;
            }
        }
&nbsp;
        expr = parseExpression();
&nbsp;
        // 12.12 Labelled Statements
        if ((expr.type === Syntax.Identifier) &amp;&amp; match(':')) {
            lex();
&nbsp;
            key = '$' + expr.name;
            if (Object.prototype.hasOwnProperty.call(state.labelSet, key)) {
                throwError({}, Messages.Redeclaration, 'Label', expr.name);
            }
&nbsp;
            state.labelSet[key] = true;
            labeledBody = parseStatement();
            delete state.labelSet[key];
            return delegate.createLabeledStatement(expr, labeledBody);
        }
&nbsp;
        consumeSemicolon();
&nbsp;
        return delegate.createExpressionStatement(expr);
    }
&nbsp;
    // 13 Function Definition
&nbsp;
    function parseFunctionSourceElements() {
        var sourceElement, sourceElements = [], token, directive, firstRestricted,
            oldLabelSet, oldInIteration, oldInSwitch, oldInFunctionBody;
&nbsp;
        expect('{');
&nbsp;
        while (index &lt; length) {
            if (lookahead.type !== Token.StringLiteral) {
                break;
            }
            token = lookahead;
&nbsp;
            sourceElement = parseSourceElement();
            sourceElements.push(sourceElement);
            if (sourceElement.expression.type !== Syntax.Literal) {
                // this is not directive
                break;
            }
            directive = source.slice(token.range[0] + 1, token.range[1] - 1);
            if (directive === 'use strict') {
                strict = true;
                if (firstRestricted) {
                    throwErrorTolerant(firstRestricted, Messages.StrictOctalLiteral);
                }
            } else {
                if (!firstRestricted &amp;&amp; token.octal) {
                    firstRestricted = token;
                }
            }
        }
&nbsp;
        oldLabelSet = state.labelSet;
        oldInIteration = state.inIteration;
        oldInSwitch = state.inSwitch;
        oldInFunctionBody = state.inFunctionBody;
&nbsp;
        state.labelSet = {};
        state.inIteration = false;
        state.inSwitch = false;
        state.inFunctionBody = true;
&nbsp;
        while (index &lt; length) {
            if (match('}')) {
                break;
            }
            sourceElement = parseSourceElement();
            if (typeof sourceElement === 'undefined') {
                break;
            }
            sourceElements.push(sourceElement);
        }
&nbsp;
        expect('}');
&nbsp;
        state.labelSet = oldLabelSet;
        state.inIteration = oldInIteration;
        state.inSwitch = oldInSwitch;
        state.inFunctionBody = oldInFunctionBody;
&nbsp;
        return delegate.createBlockStatement(sourceElements);
    }
&nbsp;
    function parseParams(firstRestricted) {
        var param, params = [], token, stricted, paramSet, key, message;
        expect('(');
&nbsp;
        if (!match(')')) {
            paramSet = {};
            while (index &lt; length) {
                token = lookahead;
                param = parseVariableIdentifier();
                key = '$' + token.value;
                if (strict) {
                    if (isRestrictedWord(token.value)) {
                        stricted = token;
                        message = Messages.StrictParamName;
                    }
                    if (Object.prototype.hasOwnProperty.call(paramSet, key)) {
                        stricted = token;
                        message = Messages.StrictParamDupe;
                    }
                } else if (!firstRestricted) {
                    if (isRestrictedWord(token.value)) {
                        firstRestricted = token;
                        message = Messages.StrictParamName;
                    } else if (isStrictModeReservedWord(token.value)) {
                        firstRestricted = token;
                        message = Messages.StrictReservedWord;
                    } else if (Object.prototype.hasOwnProperty.call(paramSet, key)) {
                        firstRestricted = token;
                        message = Messages.StrictParamDupe;
                    }
                }
                params.push(param);
                paramSet[key] = true;
                if (match(')')) {
                    break;
                }
                expect(',');
            }
        }
&nbsp;
        expect(')');
&nbsp;
        return {
            params: params,
            stricted: stricted,
            firstRestricted: firstRestricted,
            message: message
        };
    }
&nbsp;
    function parseFunctionDeclaration() {
        var id, params = [], body, token, stricted, tmp, firstRestricted, message, previousStrict;
&nbsp;
        expectKeyword('function');
        token = lookahead;
        id = parseVariableIdentifier();
        if (strict) {
            if (isRestrictedWord(token.value)) {
                throwErrorTolerant(token, Messages.StrictFunctionName);
            }
        } else {
            if (isRestrictedWord(token.value)) {
                firstRestricted = token;
                message = Messages.StrictFunctionName;
            } else if (isStrictModeReservedWord(token.value)) {
                firstRestricted = token;
                message = Messages.StrictReservedWord;
            }
        }
&nbsp;
        tmp = parseParams(firstRestricted);
        params = tmp.params;
        stricted = tmp.stricted;
        firstRestricted = tmp.firstRestricted;
        if (tmp.message) {
            message = tmp.message;
        }
&nbsp;
        previousStrict = strict;
        body = parseFunctionSourceElements();
        if (strict &amp;&amp; firstRestricted) {
            throwError(firstRestricted, message);
        }
        if (strict &amp;&amp; stricted) {
            throwErrorTolerant(stricted, message);
        }
        strict = previousStrict;
&nbsp;
        return delegate.createFunctionDeclaration(id, params, [], body);
    }
&nbsp;
    function parseFunctionExpression() {
        var token, id = null, stricted, firstRestricted, message, tmp, params = [], body, previousStrict;
&nbsp;
        expectKeyword('function');
&nbsp;
        if (!match('(')) {
            token = lookahead;
            id = parseVariableIdentifier();
            if (strict) {
                if (isRestrictedWord(token.value)) {
                    throwErrorTolerant(token, Messages.StrictFunctionName);
                }
            } else {
                if (isRestrictedWord(token.value)) {
                    firstRestricted = token;
                    message = Messages.StrictFunctionName;
                } else if (isStrictModeReservedWord(token.value)) {
                    firstRestricted = token;
                    message = Messages.StrictReservedWord;
                }
            }
        }
&nbsp;
        tmp = parseParams(firstRestricted);
        params = tmp.params;
        stricted = tmp.stricted;
        firstRestricted = tmp.firstRestricted;
        if (tmp.message) {
            message = tmp.message;
        }
&nbsp;
        previousStrict = strict;
        body = parseFunctionSourceElements();
        if (strict &amp;&amp; firstRestricted) {
            throwError(firstRestricted, message);
        }
        if (strict &amp;&amp; stricted) {
            throwErrorTolerant(stricted, message);
        }
        strict = previousStrict;
&nbsp;
        return delegate.createFunctionExpression(id, params, [], body);
    }
&nbsp;
    // 14 Program
&nbsp;
    function parseSourceElement() {
        if (lookahead.type === Token.Keyword) {
            switch (lookahead.value) {
            case 'const':
            case 'let':
                return parseConstLetDeclaration(lookahead.value);
            case 'function':
                return parseFunctionDeclaration();
            default:
                return parseStatement();
            }
        }
&nbsp;
        if (lookahead.type !== Token.EOF) {
            return parseStatement();
        }
    }
&nbsp;
    function parseSourceElements() {
        var sourceElement, sourceElements = [], token, directive, firstRestricted;
&nbsp;
        while (index &lt; length) {
            token = lookahead;
            if (token.type !== Token.StringLiteral) {
                break;
            }
&nbsp;
            sourceElement = parseSourceElement();
            sourceElements.push(sourceElement);
            if (sourceElement.expression.type !== Syntax.Literal) {
                // this is not directive
                break;
            }
            directive = source.slice(token.range[0] + 1, token.range[1] - 1);
            if (directive === 'use strict') {
                strict = true;
                if (firstRestricted) {
                    throwErrorTolerant(firstRestricted, Messages.StrictOctalLiteral);
                }
            } else {
                if (!firstRestricted &amp;&amp; token.octal) {
                    firstRestricted = token;
                }
            }
        }
&nbsp;
        while (index &lt; length) {
            sourceElement = parseSourceElement();
            if (typeof sourceElement === 'undefined') {
                break;
            }
            sourceElements.push(sourceElement);
        }
        return sourceElements;
    }
&nbsp;
    function parseProgram() {
        var body;
        strict = false;
        peek();
        body = parseSourceElements();
        return delegate.createProgram(body);
    }
&nbsp;
    // The following functions are needed only when the option to preserve
    // the comments is active.
&nbsp;
    function addComment(type, value, start, end, loc) {
        assert(typeof start === 'number', 'Comment must have valid position');
&nbsp;
        // Because the way the actual token is scanned, often the comments
        // (if any) are skipped twice during the lexical analysis.
        // Thus, we need to skip adding a comment if the comment array already
        // handled it.
        if (extra.comments.length &gt; 0) {
            if (extra.comments[extra.comments.length - 1].range[1] &gt; start) {
                return;
            }
        }
&nbsp;
        extra.comments.push({
            type: type,
            value: value,
            range: [start, end],
            loc: loc
        });
    }
&nbsp;
    function scanComment() {
        var comment, ch, loc, start, blockComment, lineComment;
&nbsp;
        comment = '';
        blockComment = false;
        lineComment = false;
&nbsp;
        while (index &lt; length) {
            ch = source[index];
&nbsp;
            if (lineComment) {
                ch = source[index++];
                if (isLineTerminator(ch.charCodeAt(0))) {
                    loc.end = {
                        line: lineNumber,
                        column: index - lineStart - 1
                    };
                    lineComment = false;
                    addComment('Line', comment, start, index - 1, loc);
                    if (ch === '\r' &amp;&amp; source[index] === '\n') {
                        ++index;
                    }
                    ++lineNumber;
                    lineStart = index;
                    comment = '';
                } else if (index &gt;= length) {
                    lineComment = false;
                    comment += ch;
                    loc.end = {
                        line: lineNumber,
                        column: length - lineStart
                    };
                    addComment('Line', comment, start, length, loc);
                } else {
                    comment += ch;
                }
            } else if (blockComment) {
                if (isLineTerminator(ch.charCodeAt(0))) {
                    if (ch === '\r' &amp;&amp; source[index + 1] === '\n') {
                        ++index;
                        comment += '\r\n';
                    } else {
                        comment += ch;
                    }
                    ++lineNumber;
                    ++index;
                    lineStart = index;
                    if (index &gt;= length) {
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
                } else {
                    ch = source[index++];
                    if (index &gt;= length) {
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
                    comment += ch;
                    if (ch === '*') {
                        ch = source[index];
                        if (ch === '/') {
                            comment = comment.substr(0, comment.length - 1);
                            blockComment = false;
                            ++index;
                            loc.end = {
                                line: lineNumber,
                                column: index - lineStart
                            };
                            addComment('Block', comment, start, index, loc);
                            comment = '';
                        }
                    }
                }
            } else if (ch === '/') {
                ch = source[index + 1];
                if (ch === '/') {
                    loc = {
                        start: {
                            line: lineNumber,
                            column: index - lineStart
                        }
                    };
                    start = index;
                    index += 2;
                    lineComment = true;
                    if (index &gt;= length) {
                        loc.end = {
                            line: lineNumber,
                            column: index - lineStart
                        };
                        lineComment = false;
                        addComment('Line', comment, start, index, loc);
                    }
                } else if (ch === '*') {
                    start = index;
                    index += 2;
                    blockComment = true;
                    loc = {
                        start: {
                            line: lineNumber,
                            column: index - lineStart - 2
                        }
                    };
                    if (index &gt;= length) {
                        throwError({}, Messages.UnexpectedToken, 'ILLEGAL');
                    }
                } else {
                    break;
                }
            } else if (isWhiteSpace(ch.charCodeAt(0))) {
                ++index;
            } else if (isLineTerminator(ch.charCodeAt(0))) {
                ++index;
                if (ch ===  '\r' &amp;&amp; source[index] === '\n') {
                    ++index;
                }
                ++lineNumber;
                lineStart = index;
            } else {
                break;
            }
        }
    }
&nbsp;
    function filterCommentLocation() {
        var i, entry, comment, comments = [];
&nbsp;
        for (i = 0; i &lt; extra.comments.length; ++i) {
            entry = extra.comments[i];
            comment = {
                type: entry.type,
                value: entry.value
            };
            if (extra.range) {
                comment.range = entry.range;
            }
            if (extra.loc) {
                comment.loc = entry.loc;
            }
            comments.push(comment);
        }
&nbsp;
        extra.comments = comments;
    }
&nbsp;
    function collectToken() {
        var start, loc, token, range, value;
&nbsp;
        skipComment();
        start = index;
        loc = {
            start: {
                line: lineNumber,
                column: index - lineStart
            }
        };
&nbsp;
        token = extra.advance();
        loc.end = {
            line: lineNumber,
            column: index - lineStart
        };
&nbsp;
        if (token.type !== Token.EOF) {
            range = [token.range[0], token.range[1]];
            value = source.slice(token.range[0], token.range[1]);
            extra.tokens.push({
                type: TokenName[token.type],
                value: value,
                range: range,
                loc: loc
            });
        }
&nbsp;
        return token;
    }
&nbsp;
    function collectRegex() {
        var pos, loc, regex, token;
&nbsp;
        skipComment();
&nbsp;
        pos = index;
        loc = {
            start: {
                line: lineNumber,
                column: index - lineStart
            }
        };
&nbsp;
        regex = extra.scanRegExp();
        loc.end = {
            line: lineNumber,
            column: index - lineStart
        };
&nbsp;
        // Pop the previous token, which is likely '/' or '/='
        <span class="missing-if-branch" title="else path not taken"" >E</span>if (extra.tokens.length &gt; 0) {
            token = extra.tokens[extra.tokens.length - 1];
            <span class="missing-if-branch" title="else path not taken"" >E</span>if (token.range[0] === pos &amp;&amp; token.type === 'Punctuator') {
                <span class="missing-if-branch" title="else path not taken"" >E</span>if (token.value === '/' || token.value === '/=') {
                    extra.tokens.pop();
                }
            }
        }
&nbsp;
        extra.tokens.push({
            type: 'RegularExpression',
            value: regex.literal,
            range: [pos, index],
            loc: loc
        });
&nbsp;
        return regex;
    }
&nbsp;
    function filterTokenLocation() {
        var i, entry, token, tokens = [];
&nbsp;
        for (i = 0; i &lt; extra.tokens.length; ++i) {
            entry = extra.tokens[i];
            token = {
                type: entry.type,
                value: entry.value
            };
            if (extra.range) {
                token.range = entry.range;
            }
            if (extra.loc) {
                token.loc = entry.loc;
            }
            tokens.push(token);
        }
&nbsp;
        extra.tokens = tokens;
    }
&nbsp;
    function createLocationMarker() {
        var marker = {};
&nbsp;
        marker.range = [index, index];
        marker.loc = {
            start: {
                line: lineNumber,
                column: index - lineStart
            },
            end: {
                line: lineNumber,
                column: index - lineStart
            }
        };
&nbsp;
        marker.end = function () {
            this.range[1] = index;
            this.loc.end.line = lineNumber;
            this.loc.end.column = index - lineStart;
        };
&nbsp;
        marker.applyGroup = function (node) {
            <span class="missing-if-branch" title="else path not taken"" >E</span>if (extra.range) {
                node.groupRange = [this.range[0], this.range[1]];
            }
            <span class="missing-if-branch" title="else path not taken"" >E</span>if (extra.loc) {
                node.groupLoc = {
                    start: {
                        line: this.loc.start.line,
                        column: this.loc.start.column
                    },
                    end: {
                        line: this.loc.end.line,
                        column: this.loc.end.column
                    }
                };
            }
        };
&nbsp;
        marker.apply = function (node) {
            if (extra.range) {
                node.range = [this.range[0], this.range[1]];
            }
            if (extra.loc) {
                node.loc = {
                    start: {
                        line: this.loc.start.line,
                        column: this.loc.start.column
                    },
                    end: {
                        line: this.loc.end.line,
                        column: this.loc.end.column
                    }
                };
            }
        };
&nbsp;
        return marker;
    }
&nbsp;
    function trackGroupExpression() {
        var marker, expr;
&nbsp;
        skipComment();
        marker = createLocationMarker();
        expect('(');
&nbsp;
        expr = parseExpression();
&nbsp;
        expect(')');
&nbsp;
        marker.end();
        marker.applyGroup(expr);
&nbsp;
        return expr;
    }
&nbsp;
    function trackLeftHandSideExpression() {
        var marker, expr, property;
&nbsp;
        skipComment();
        marker = createLocationMarker();
&nbsp;
        expr = matchKeyword('new') ? parseNewExpression() : parsePrimaryExpression();
&nbsp;
        while (match('.') || match('[')) {
            if (match('[')) {
                property = parseComputedMember();
                expr = delegate.createMemberExpression('[', expr, property);
                marker.end();
                marker.apply(expr);
            } else {
                property = parseNonComputedMember();
                expr = delegate.createMemberExpression('.', expr, property);
                marker.end();
                marker.apply(expr);
            }
        }
&nbsp;
        return expr;
    }
&nbsp;
    function trackLeftHandSideExpressionAllowCall() {
        var marker, expr, args, property;
&nbsp;
        skipComment();
        marker = createLocationMarker();
&nbsp;
        expr = matchKeyword('new') ? parseNewExpression() : parsePrimaryExpression();
&nbsp;
        while (match('.') || match('[') || match('(')) {
            if (match('(')) {
                args = parseArguments();
                expr = delegate.createCallExpression(expr, args);
                marker.end();
                marker.apply(expr);
            } else if (match('[')) {
                property = parseComputedMember();
                expr = delegate.createMemberExpression('[', expr, property);
                marker.end();
                marker.apply(expr);
            } else {
                property = parseNonComputedMember();
                expr = delegate.createMemberExpression('.', expr, property);
                marker.end();
                marker.apply(expr);
            }
        }
&nbsp;
        return expr;
    }
&nbsp;
    function filterGroup(node) {
        var n, i, entry;
&nbsp;
        n = (Object.prototype.toString.apply(node) === '[object Array]') ? [] : {};
        for (i in node) {
            if (node.hasOwnProperty(i) &amp;&amp; i !== 'groupRange' &amp;&amp; i !== 'groupLoc') {
                entry = node[i];
                if (entry === null || typeof entry !== 'object' || entry instanceof RegExp) {
                    n[i] = entry;
                } else {
                    n[i] = filterGroup(entry);
                }
            }
        }
        return n;
    }
&nbsp;
    function wrapTrackingFunction(range, loc) {
&nbsp;
        return function (parseFunction) {
&nbsp;
            function isBinary(node) {
                return node.type === Syntax.LogicalExpression ||
                    node.type === Syntax.BinaryExpression;
            }
&nbsp;
            function visit(node) {
                var start, end;
&nbsp;
                if (isBinary(node.left)) {
                    visit(node.left);
                }
                if (isBinary(node.right)) {
                    visit(node.right);
                }
&nbsp;
                <span class="missing-if-branch" title="else path not taken"" >E</span>if (range) {
                    if (node.left.groupRange || node.right.groupRange) {
                        start = node.left.groupRange ? node.left.groupRange[0] : node.left.range[0];
                        end = node.right.groupRange ? node.right.groupRange[1] : node.right.range[1];
                        node.range = [start, end];
                    } else if (typeof node.range === 'undefined') {
                        start = node.left.range[0];
                        end = node.right.range[1];
                        node.range = [start, end];
                    }
                }
                <span class="missing-if-branch" title="else path not taken"" >E</span>if (loc) {
                    if (node.left.groupLoc || node.right.groupLoc) {
                        start = node.left.groupLoc ? node.left.groupLoc.start : node.left.loc.start;
                        end = node.right.groupLoc ? node.right.groupLoc.end : node.right.loc.end;
                        node.loc = {
                            start: start,
                            end: end
                        };
                    } else if (typeof node.loc === 'undefined') {
                        node.loc = {
                            start: node.left.loc.start,
                            end: node.right.loc.end
                        };
                    }
                }
            }
&nbsp;
            return function () {
                var marker, node;
&nbsp;
                skipComment();
&nbsp;
                marker = createLocationMarker();
                node = parseFunction.apply(null, arguments);
                marker.end();
&nbsp;
                if (range &amp;&amp; typeof node.range === 'undefined') {
                    marker.apply(node);
                }
&nbsp;
                if (loc &amp;&amp; typeof node.loc === 'undefined') {
                    marker.apply(node);
                }
&nbsp;
                if (isBinary(node)) {
                    visit(node);
                }
&nbsp;
                return node;
            };
        };
    }
&nbsp;
    function patch() {
&nbsp;
        var wrapTracking;
&nbsp;
        if (extra.comments) {
            extra.skipComment = skipComment;
            skipComment = scanComment;
        }
&nbsp;
        if (extra.range || extra.loc) {
&nbsp;
            extra.parseGroupExpression = parseGroupExpression;
            extra.parseLeftHandSideExpression = parseLeftHandSideExpression;
            extra.parseLeftHandSideExpressionAllowCall = parseLeftHandSideExpressionAllowCall;
            parseGroupExpression = trackGroupExpression;
            parseLeftHandSideExpression = trackLeftHandSideExpression;
            parseLeftHandSideExpressionAllowCall = trackLeftHandSideExpressionAllowCall;
&nbsp;
            wrapTracking = wrapTrackingFunction(extra.range, extra.loc);
&nbsp;
            extra.parseAssignmentExpression = parseAssignmentExpression;
            extra.parseBinaryExpression = parseBinaryExpression;
            extra.parseBlock = parseBlock;
            extra.parseFunctionSourceElements = parseFunctionSourceElements;
            extra.parseCatchClause = parseCatchClause;
            extra.parseComputedMember = parseComputedMember;
            extra.parseConditionalExpression = parseConditionalExpression;
            extra.parseConstLetDeclaration = parseConstLetDeclaration;
            extra.parseExpression = parseExpression;
            extra.parseForVariableDeclaration = parseForVariableDeclaration;
            extra.parseFunctionDeclaration = parseFunctionDeclaration;
            extra.parseFunctionExpression = parseFunctionExpression;
            extra.parseNewExpression = parseNewExpression;
            extra.parseNonComputedProperty = parseNonComputedProperty;
            extra.parseObjectProperty = parseObjectProperty;
            extra.parseObjectPropertyKey = parseObjectPropertyKey;
            extra.parsePostfixExpression = parsePostfixExpression;
            extra.parsePrimaryExpression = parsePrimaryExpression;
            extra.parseProgram = parseProgram;
            extra.parsePropertyFunction = parsePropertyFunction;
            extra.parseStatement = parseStatement;
            extra.parseSwitchCase = parseSwitchCase;
            extra.parseUnaryExpression = parseUnaryExpression;
            extra.parseVariableDeclaration = parseVariableDeclaration;
            extra.parseVariableIdentifier = parseVariableIdentifier;
&nbsp;
            parseAssignmentExpression = wrapTracking(extra.parseAssignmentExpression);
            parseBinaryExpression = wrapTracking(extra.parseBinaryExpression);
            parseBlock = wrapTracking(extra.parseBlock);
            parseFunctionSourceElements = wrapTracking(extra.parseFunctionSourceElements);
            parseCatchClause = wrapTracking(extra.parseCatchClause);
            parseComputedMember = wrapTracking(extra.parseComputedMember);
            parseConditionalExpression = wrapTracking(extra.parseConditionalExpression);
            parseConstLetDeclaration = wrapTracking(extra.parseConstLetDeclaration);
            parseExpression = wrapTracking(extra.parseExpression);
            parseForVariableDeclaration = wrapTracking(extra.parseForVariableDeclaration);
            parseFunctionDeclaration = wrapTracking(extra.parseFunctionDeclaration);
            parseFunctionExpression = wrapTracking(extra.parseFunctionExpression);
            parseLeftHandSideExpression = wrapTracking(parseLeftHandSideExpression);
            parseNewExpression = wrapTracking(extra.parseNewExpression);
            parseNonComputedProperty = wrapTracking(extra.parseNonComputedProperty);
            parseObjectProperty = wrapTracking(extra.parseObjectProperty);
            parseObjectPropertyKey = wrapTracking(extra.parseObjectPropertyKey);
            parsePostfixExpression = wrapTracking(extra.parsePostfixExpression);
            parsePrimaryExpression = wrapTracking(extra.parsePrimaryExpression);
            parseProgram = wrapTracking(extra.parseProgram);
            parsePropertyFunction = wrapTracking(extra.parsePropertyFunction);
            parseStatement = wrapTracking(extra.parseStatement);
            parseSwitchCase = wrapTracking(extra.parseSwitchCase);
            parseUnaryExpression = wrapTracking(extra.parseUnaryExpression);
            parseVariableDeclaration = wrapTracking(extra.parseVariableDeclaration);
            parseVariableIdentifier = wrapTracking(extra.parseVariableIdentifier);
        }
&nbsp;
        if (typeof extra.tokens !== 'undefined') {
            extra.advance = advance;
            extra.scanRegExp = scanRegExp;
&nbsp;
            advance = collectToken;
            scanRegExp = collectRegex;
        }
    }
&nbsp;
    function unpatch() {
        if (typeof extra.skipComment === 'function') {
            skipComment = extra.skipComment;
        }
&nbsp;
        if (extra.range || extra.loc) {
            parseAssignmentExpression = extra.parseAssignmentExpression;
            parseBinaryExpression = extra.parseBinaryExpression;
            parseBlock = extra.parseBlock;
            parseFunctionSourceElements = extra.parseFunctionSourceElements;
            parseCatchClause = extra.parseCatchClause;
            parseComputedMember = extra.parseComputedMember;
            parseConditionalExpression = extra.parseConditionalExpression;
            parseConstLetDeclaration = extra.parseConstLetDeclaration;
            parseExpression = extra.parseExpression;
            parseForVariableDeclaration = extra.parseForVariableDeclaration;
            parseFunctionDeclaration = extra.parseFunctionDeclaration;
            parseFunctionExpression = extra.parseFunctionExpression;
            parseGroupExpression = extra.parseGroupExpression;
            parseLeftHandSideExpression = extra.parseLeftHandSideExpression;
            parseLeftHandSideExpressionAllowCall = extra.parseLeftHandSideExpressionAllowCall;
            parseNewExpression = extra.parseNewExpression;
            parseNonComputedProperty = extra.parseNonComputedProperty;
            parseObjectProperty = extra.parseObjectProperty;
            parseObjectPropertyKey = extra.parseObjectPropertyKey;
            parsePrimaryExpression = extra.parsePrimaryExpression;
            parsePostfixExpression = extra.parsePostfixExpression;
            parseProgram = extra.parseProgram;
            parsePropertyFunction = extra.parsePropertyFunction;
            parseStatement = extra.parseStatement;
            parseSwitchCase = extra.parseSwitchCase;
            parseUnaryExpression = extra.parseUnaryExpression;
            parseVariableDeclaration = extra.parseVariableDeclaration;
            parseVariableIdentifier = extra.parseVariableIdentifier;
        }
&nbsp;
        if (typeof extra.scanRegExp === 'function') {
            advance = extra.advance;
            scanRegExp = extra.scanRegExp;
        }
    }
&nbsp;
    function parse(code, options) {
        var program, toString;
&nbsp;
        toString = String;
        if (typeof code !== 'string' &amp;&amp; !(code instanceof String)) {
            code = toString(code);
        }
&nbsp;
        delegate = SyntaxTreeDelegate;
        source = code;
        index = 0;
        lineNumber = (source.length &gt; 0) ? 1 : 0;
        lineStart = 0;
        length = source.length;
        lookahead = null;
        state = {
            allowIn: true,
            labelSet: {},
            inFunctionBody: false,
            inIteration: false,
            inSwitch: false
        };
&nbsp;
        extra = {};
        if (typeof options !== 'undefined') {
            extra.range = (typeof options.range === 'boolean') &amp;&amp; options.range;
            extra.loc = (typeof options.loc === 'boolean') &amp;&amp; options.loc;
&nbsp;
            if (typeof options.tokens === 'boolean' &amp;&amp; options.tokens) {
                extra.tokens = [];
            }
            if (typeof options.comment === 'boolean' &amp;&amp; options.comment) {
                extra.comments = [];
            }
            if (typeof options.tolerant === 'boolean' &amp;&amp; options.tolerant) {
                extra.errors = [];
            }
        }
&nbsp;
        if (length &gt; 0) {
            <span class="missing-if-branch" title="if path not taken"" >I</span>if (typeof source[0] === 'undefined') {
                // Try first to convert to a string. This is good as fast path
                // for old IE which understands string indexing for string
                // literals only and not for string object.
<span class="cstat-no" title="statement not covered" >                if (code instanceof String) {</span>
<span class="cstat-no" title="statement not covered" >                    source = code.valueOf();</span>
                }
            }
        }
&nbsp;
        patch();
        try {
            program = parseProgram();
            if (typeof extra.comments !== 'undefined') {
                filterCommentLocation();
                program.comments = extra.comments;
            }
            if (typeof extra.tokens !== 'undefined') {
                filterTokenLocation();
                program.tokens = extra.tokens;
            }
            if (typeof extra.errors !== 'undefined') {
                program.errors = extra.errors;
            }
            if (extra.range || extra.loc) {
                program.body = filterGroup(program.body);
            }
        } catch (e) {
            throw e;
        } finally {
            unpatch();
            extra = {};
        }
&nbsp;
        return program;
    }
&nbsp;
    // Sync with package.json and component.json.
    exports.version = '1.1.0-dev';
&nbsp;
    exports.parse = parse;
&nbsp;
    // Deep copy.
    exports.Syntax = (function () {
        var name, types = {};
&nbsp;
        <span class="missing-if-branch" title="else path not taken"" >E</span>if (typeof Object.create === 'function') {
            types = Object.create(null);
        }
&nbsp;
        for (name in Syntax) {
            <span class="missing-if-branch" title="else path not taken"" >E</span>if (Syntax.hasOwnProperty(name)) {
                types[name] = Syntax[name];
            }
        }
&nbsp;
        <span class="missing-if-branch" title="else path not taken"" >E</span>if (typeof Object.freeze === 'function') {
            Object.freeze(types);
        }
&nbsp;
        return types;
    }());
&nbsp;
}));
/* vim: set sw=4 ts=4 et tw=80 : */
&nbsp;</pre></td></tr>
</table></pre>

</div>
<div class='footer'>
</div>
</body>

<script src="../prettify.js"></script>

<script src="http://yui.yahooapis.com/3.6.0/build/yui/yui-min.js"></script>
<script>

    YUI().use('datatable', function (Y) {

        var formatters = {
          pct: function (o) {
              o.className += o.record.get('classes')[o.column.key];
              try {
                  return o.value.toFixed(2) + '%';
              } catch (ex) { return o.value + '%'; }
          },
          html: function (o) {
              o.className += o.record.get('classes')[o.column.key];
              return o.record.get(o.column.key + '_html');
          }
        },
          defaultFormatter = function (o) {
              o.className += o.record.get('classes')[o.column.key];
              return o.value;
          };

        function getColumns(theadNode) {
            var colNodes = theadNode.all('tr th'),
                cols = [],
                col;
            colNodes.each(function (colNode) {
                col = {
                    key: colNode.getAttribute('data-col'),
                    label: colNode.get('innerHTML') || ' ',
                    sortable: !colNode.getAttribute('data-nosort'),
                    className: colNode.getAttribute('class'),
                    type: colNode.getAttribute('data-type'),
                    allowHTML: colNode.getAttribute('data-html') === 'true' || colNode.getAttribute('data-fmt') === 'html'
                };
                col.formatter = formatters[colNode.getAttribute('data-fmt')] || defaultFormatter;
                cols.push(col);
            });
            return cols;
        }

        function getRowData(trNode, cols) {
            var tdNodes = trNode.all('td'),
                    i,
                    row = { classes: {} },
                    node,
                    name;
            for (i = 0; i < cols.length; i += 1) {
                name = cols[i].key;
                node = tdNodes.item(i);
                row[name] = node.getAttribute('data-value') || node.get('innerHTML');
                row[name + '_html'] = node.get('innerHTML');
                row.classes[name] = node.getAttribute('class');
                //Y.log('Name: ' + name + '; Value: ' + row[name]);
                if (cols[i].type === 'number') { row[name] = row[name] * 1; }
            }
            //Y.log(row);
            return row;
        }

        function getData(tbodyNode, cols) {
            var data = [];
            tbodyNode.all('tr').each(function (trNode) {
                data.push(getRowData(trNode, cols));
            });
            return data;
        }

        function replaceTable(node) {
            if (!node) { return; }
            var cols = getColumns(node.one('thead')),
                data = getData(node.one('tbody'), cols),
                table,
                parent = node.get('parentNode');

            table = new Y.DataTable({
                columns: cols,
                data: data,
                sortBy: 'file'
            });
            parent.set('innerHTML', '');
            table.render(parent);
        }

        Y.on('domready', function () {
            replaceTable(Y.one('div.coverage-summary table'));
            if (typeof prettyPrint === 'function') {
                prettyPrint();
            }
        });
    });
</script>
</html>
