<!DOCTYPE html>
<!--[if IE 8]>    <html class="no-js lt-ie9" lang="en"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en"> <!--<![endif]-->

<head>
  <meta charset="utf-8" />
  <title>Esprima: Compatibility Tests</title>
  <meta name="viewport" content="width=device-width" />
  <link rel="stylesheet" href="../assets/style.css">
  <link rel="stylesheet" href="../assets/foundation/foundation.min.css">
</head>

<body>

  <!-- Navigation bar -->
  <div class="row">
    <div class="twelve columns">
      <nav class="top-bar">
        <ul>
          <li class="name">
             <h1><a href="../index.html">Esprima</a></h1>
          </li>
        </ul>
        <section>
          <ul class="right">
            <li class="divider show-for-medium-and-up"></li>
            <li class="has-dropdown">
              <a href="../demo/index.html">Demo</a>
              <ul class="dropdown">
                <li><label>Static Analysis</label></li>
                <li><a href="../demo/parse.html">Online Parsing</a></li>
                <li><a href="../demo/validate.html">Syntax Validator</a></li>
                <li><a href="../demo/precedence.html">Operator Precedence</a></li>
                <li><a href="../demo/collector.html">Regex Collector</a></li>
                <li><label>Dynamic Tracing</label></li>
                <li><a href="../demo/functiontrace.html">Function Instrumentation</a></li>
                <li><label>Code Transformation</label></li>
                <li><a href="../demo/rewrite.html">Source Rewrite</a></li>
                <li><a href="../demo/minify.html">Minifiy &amp; Obfuscate</a></li>
                <li><label>Editing Tools</label></li>
                <li><a href="../demo/highlight.html">Identifier Highlight</a></li>
                <li><a href="../demo/autocomplete.html">Autocomplete</a></li>
              </ul>
            </li>
            <li class="has-dropdown">
              <a href="#">Project</a>
              <ul class="dropdown">
                <li><a href="http://github.com/ariya/esprima">Git Repository</a></li>
                <li><a href="https://travis-ci.org/ariya/esprima">Continuous Integration</a></li>
                <li><a href="http://groups.google.com/group/esprima">Mailing List</a></li>
                <li><a href="http://issues.esprima.org/">Issue Tracker</a></li>
                <li class="divider"></li>
                <li><label>QA</label></li>
                <li><a href="../test/index.html">Unit Tests</a></li>
                <li><a href="../test/benchmarks.html">Benchmarks Suite</a></li>
                <li><a href="../test/compat.html">Compatibility Tests</a></li>
                <li><a href="../test/compare.html">Speed Comparison</a></li>
                <li><a href="../test/module.html">Module Loading</a></li>
                <li><a href="../test/coverage.html">Coverage Analysis</a></li>
              </ul>
            </li>
            <li><a href="../doc/index.html">Documentation</a></li>
          </ul>
        </section>
      </nav>
    </div>
  </div>

  <!-- Title and subtitle -->
  <div class="row">
    <div class="twelve columns">
      <h3 class="subheader"><strong>Compatibility</strong> keeps everyone happy</h3>
    </div>
  </div>

  <!-- Main content -->
  <div class="row">
    <div class="eight columns">
      <div id="report"></div>
    </div>

    <div class="four columns">
      <div id="status" class="alert-box secondary">Please wait...</div>
      <div class="panel">
        <p>The following tests are to ensure that the syntax tree is compatible to
that produced by Mozilla SpiderMonkey
        <a href="https://developer.mozilla.org/en/SpiderMonkey/Parser_API">Parser reflection</a>.</p>
        <p>Version being tested: <strong><span id="version"></span></strong>.</p>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="row copyright">
    <div class="six columns">
      <p>Esprima is created and mantained by <a href="http://ariya.ofilabs.com/about">Ariya Hidayat</a>.</p>
    </div>
    <div class="six columns">
      <ul class="link-list right">
        <li><a href="http://twitter.com/esprima">@Esprima</a></li>
        <li><a href="https://github.com/ariya/esprima">GitHub</a></li>
      </ul>
    </div>
  </div>

<script src="../esprima.js"></script>
<script src="../assets/json2.js"></script>
<script id="reflect" src="reflect.js"></script>
<script src="compat.js"></script>
<script>
/*jslint browser: true */
window.onload = window.runTests;
</script>
</body>
</html>
