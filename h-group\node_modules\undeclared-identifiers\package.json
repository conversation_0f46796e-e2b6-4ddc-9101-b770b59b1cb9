{"name": "undeclared-identifiers", "description": "find undeclared identifiers and property accesses in a javascript file.", "version": "1.1.3", "author": "<PERSON><PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/goto-bus-stop/undeclared-identifiers/issues"}, "devDependencies": {"jquery": "^3.3.1", "lodash": "^4.17.5", "nanobench": "^2.1.0", "standard": "^11.0.0", "tap-spec": "^5.0.0", "tape": "^4.0.0", "three": "^0.92.0"}, "homepage": "https://github.com/goto-bus-stop/undeclared-identifiers", "keywords": ["ast", "check", "detect", "identifiers", "javascript", "undeclared"], "license": "Apache-2.0", "main": "index.js", "bin": "bin.js", "repository": {"type": "git", "url": "https://github.com/goto-bus-stop/undeclared-identifiers.git"}, "scripts": {"test": "standard && tape test/*.js | tap-spec"}, "dependencies": {"acorn-node": "^1.3.0", "dash-ast": "^1.0.0", "get-assigned-identifiers": "^1.2.0", "simple-concat": "^1.0.0", "xtend": "^4.0.1"}}