{"name": "exposify", "version": "0.5.0", "description": "browserify transform that exposes globals added via a script tag as modules so they can be required.", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/exposify.git"}, "homepage": "https://github.com/thlorenz/exposify", "dependencies": {"globo": "~1.1.0", "map-obj": "~1.0.1", "replace-requires": "~1.0.3", "through2": "~0.4.0", "transformify": "~0.1.1"}, "devDependencies": {"tap": "~0.4.3", "browserify": "~8.1.1"}, "keywords": ["browserify", "browserify-transform", "transform", "expose", "window", "global", "require", "shim"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "license": {"type": "MIT", "url": "https://github.com/thlorenz/exposify/blob/master/LICENSE"}, "engine": {"node": ">=0.8"}}