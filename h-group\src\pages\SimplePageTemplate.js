const React = require('react');

// قالب بسيط للصفحات
const createSimplePage = (title, description) => {
  return () => {
    return React.createElement('div', { className: 'simple-page' },
      React.createElement('div', { className: 'page-header' },
        React.createElement('h2', null, title)
      ),
      React.createElement('div', { className: 'card' },
        React.createElement('div', { className: 'card-body' },
          React.createElement('div', { className: 'alert alert-info' },
            description || `صفحة ${title} قيد التطوير. يرجى المحاولة لاحقاً.`
          )
        )
      )
    );
  };
};

// تصدير الصفحات البسيطة
const CustomerDetails = createSimplePage('تفاصيل العميل', 'صفحة تفاصيل العميل قيد التطوير.');
const CustomerForm = createSimplePage('إضافة/تعديل عميل', 'صفحة إضافة وتعديل العملاء قيد التطوير.');
const ProductsList = createSimplePage('المنتجات', 'صفحة إدارة المنتجات قيد التطوير.');
const ProductDetails = createSimplePage('تفاصيل المنتج', 'صفحة تفاصيل المنتج قيد التطوير.');
const ProductForm = createSimplePage('إضافة/تعديل منتج', 'صفحة إضافة وتعديل المنتجات قيد التطوير.');
const MaterialsList = createSimplePage('المواد الخام', 'صفحة إدارة المواد الخام قيد التطوير.');
const MaterialDetails = createSimplePage('تفاصيل المادة', 'صفحة تفاصيل المادة الخام قيد التطوير.');
const MaterialForm = createSimplePage('إضافة/تعديل مادة', 'صفحة إضافة وتعديل المواد الخام قيد التطوير.');
const InventoryList = createSimplePage('المخزون', 'صفحة إدارة المخزون قيد التطوير.');
const InventoryForm = createSimplePage('إدارة المخزون', 'صفحة إدارة المخزون قيد التطوير.');
const WorkersList = createSimplePage('العمال', 'صفحة إدارة العمال قيد التطوير.');
const WorkerDetails = createSimplePage('تفاصيل العامل', 'صفحة تفاصيل العامل قيد التطوير.');
const WorkerForm = createSimplePage('إضافة/تعديل عامل', 'صفحة إضافة وتعديل العمال قيد التطوير.');
const ExpensesList = createSimplePage('المصروفات', 'صفحة إدارة المصروفات قيد التطوير.');
const ExpenseForm = createSimplePage('إضافة مصروف', 'صفحة إضافة المصروفات قيد التطوير.');
const InvoicesList = createSimplePage('الفواتير', 'صفحة إدارة الفواتير قيد التطوير.');
const InvoiceDetails = createSimplePage('تفاصيل الفاتورة', 'صفحة تفاصيل الفاتورة قيد التطوير.');
const InvoiceForm = createSimplePage('إنشاء فاتورة', 'صفحة إنشاء الفواتير قيد التطوير.');
const SalesReport = createSimplePage('تقرير المبيعات', 'صفحة تقرير المبيعات قيد التطوير.');
const ExpensesReport = createSimplePage('تقرير المصروفات', 'صفحة تقرير المصروفات قيد التطوير.');
const ProfitReport = createSimplePage('تقرير الأرباح', 'صفحة تقرير الأرباح قيد التطوير.');
const WorkersReport = createSimplePage('تقرير العمال', 'صفحة تقرير العمال قيد التطوير.');
const MaterialsReport = createSimplePage('تقرير المواد', 'صفحة تقرير المواد الخام قيد التطوير.');
const ProductsReport = createSimplePage('تقرير المنتجات', 'صفحة تقرير المنتجات قيد التطوير.');
const Settings = createSimplePage('الإعدادات', 'صفحة الإعدادات قيد التطوير.');
const UsersList = createSimplePage('المستخدمون', 'صفحة إدارة المستخدمين قيد التطوير.');
const UserForm = createSimplePage('إضافة/تعديل مستخدم', 'صفحة إضافة وتعديل المستخدمين قيد التطوير.');
const BackupRestore = createSimplePage('النسخ الاحتياطي', 'صفحة النسخ الاحتياطي والاستعادة قيد التطوير.');

module.exports = {
  CustomerDetails,
  CustomerForm,
  ProductsList,
  ProductDetails,
  ProductForm,
  MaterialsList,
  MaterialDetails,
  MaterialForm,
  InventoryList,
  InventoryForm,
  WorkersList,
  WorkerDetails,
  WorkerForm,
  ExpensesList,
  ExpenseForm,
  InvoicesList,
  InvoiceDetails,
  InvoiceForm,
  SalesReport,
  ExpensesReport,
  ProfitReport,
  WorkersReport,
  MaterialsReport,
  ProductsReport,
  Settings,
  UsersList,
  UserForm,
  BackupRestore
};
