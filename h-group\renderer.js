// هذا الملف هو نقطة الدخول لتطبيق React
console.log('تم تحميل ملف renderer.js');

// استيراد React وReactDOM
const React = require('react');
const ReactDOM = require('react-dom');

// تحميل تطبيق React عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('بدء تحميل التطبيق...');

  try {
    const appDiv = document.getElementById('app');

    // تنظيف محتوى العنصر
    appDiv.innerHTML = '';

    // استيراد مكون App الحقيقي
    const App = require('./src/App');

    // تحميل تطبيق React الحقيقي
    ReactDOM.render(
      React.createElement(App),
      appDiv
    );

    console.log('تم تحميل تطبيق React بنجاح');

  } catch (error) {
    console.error('خطأ في تحميل التطبيق:', error);
    document.getElementById('app').innerHTML = `
      <div class="alert alert-danger">
        <h2>خطأ في تحميل التطبيق</h2>
        <p>حدث خطأ أثناء تحميل التطبيق: ${error.message}</p>
      </div>
    `;
  }
});
