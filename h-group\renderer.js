// هذا الملف هو نقطة الدخول لتطبيق React
// سيتم تحميل تطبيق React هنا

console.log('تم تحميل ملف renderer.js');

// استيراد React وReactDOM
const React = require('react');
const ReactDOM = require('react-dom');
const { HashRouter } = require('react-router-dom');

// تحميل تطبيق React عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  const appDiv = document.getElementById('app');

  // تنظيف محتوى العنصر
  appDiv.innerHTML = '';

  // استيراد مكون App
  const App = require('./src/App');

  // تحميل تطبيق React
  ReactDOM.render(
    React.createElement(
      Hash<PERSON>outer,
      null,
      React.createElement(App)
    ),
    appDiv
  );

  console.log('تم تحميل تطبيق React بنجاح');
});
