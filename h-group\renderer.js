// هذا الملف هو نقطة الدخول لتطبيق React
console.log('تم تحميل ملف renderer.js');

// تحميل التطبيق عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('بدء تحميل التطبيق...');

  try {
    const appDiv = document.getElementById('app');

    // تنظيف محتوى العنصر
    appDiv.innerHTML = '';

    // إنشاء محتوى بسيط للاختبار
    const dashboardHTML = `
      <div class="app">
        <div class="app-container">
          <div class="main-content">
            <div class="dashboard">
              <h1>مرحباً بك في نظام اتش قروب</h1>
              <div class="stats-grid">
                <div class="stat-card">
                  <h3>إجمالي الطلبات</h3>
                  <p class="stat-value">25</p>
                </div>
                <div class="stat-card warning">
                  <h3>طلبات قيد التنفيذ</h3>
                  <p class="stat-value">8</p>
                </div>
                <div class="stat-card success">
                  <h3>طلبات مكتملة</h3>
                  <p class="stat-value">17</p>
                </div>
                <div class="stat-card">
                  <h3>العملاء</h3>
                  <p class="stat-value">45</p>
                </div>
              </div>
              <div class="card">
                <div class="card-header">
                  <h3>نظام إدارة ورشة النجارة</h3>
                </div>
                <div class="card-body">
                  <p>يمكنك الآن إدارة جميع عمليات ورشة النجارة من خلال هذا النظام المتكامل.</p>
                  <div class="dashboard-actions">
                    <button class="btn btn-primary">إضافة طلب جديد</button>
                    <button class="btn btn-secondary">إضافة عميل جديد</button>
                    <button class="btn btn-info">عرض التقارير</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    appDiv.innerHTML = dashboardHTML;
    console.log('تم تحميل التطبيق بنجاح');

  } catch (error) {
    console.error('خطأ في تحميل التطبيق:', error);
    document.getElementById('app').innerHTML = `
      <div class="alert alert-danger">
        <h2>خطأ في تحميل التطبيق</h2>
        <p>حدث خطأ أثناء تحميل التطبيق: ${error.message}</p>
      </div>
    `;
  }
});
