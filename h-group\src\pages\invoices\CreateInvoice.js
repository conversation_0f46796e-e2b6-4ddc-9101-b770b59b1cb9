const React = require('react');
const { useState, useEffect } = React;
const { useNavigate, useParams } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const InstallmentsForm = require('../../components/invoices/InstallmentsForm');

const CreateInvoice = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [order, setOrder] = useState(null);
  const [invoice, setInvoice] = useState({
    order_id: orderId,
    issue_date: new Date().toISOString().split('T')[0],
    due_date: '',
    total_amount: 0,
    paid_amount: 0,
    payment_method: 'نقدي',
    payment_type: 'كامل',
    status: 'غير مدفوعة',
    notes: ''
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [step, setStep] = useState(1); // 1: بيانات الفاتورة، 2: الأقساط
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (!hasPermission('invoices_create')) {
      alert('ليس لديك صلاحية لإنشاء فاتورة جديدة');
      navigate('/invoices');
    }
  }, [hasPermission, navigate]);
  
  // تحميل بيانات الطلب
  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const data = await window.api.orders.getById(orderId);
        setOrder(data);
        
        // تعيين المبلغ الإجمالي من الطلب
        setInvoice(prev => ({
          ...prev,
          total_amount: data.final_price || 0
        }));
      } catch (error) {
        console.error('خطأ في تحميل بيانات الطلب:', error);
        setError('حدث خطأ أثناء تحميل بيانات الطلب. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);
  
  // تغيير قيمة في نموذج الفاتورة
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // معالجة خاصة للقيم العددية
    if (name === 'total_amount' || name === 'paid_amount') {
      setInvoice(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else {
      setInvoice(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // تحديث حالة الفاتورة بناءً على المبلغ المدفوع
    if (name === 'paid_amount') {
      const paidAmount = parseFloat(value) || 0;
      const totalAmount = invoice.total_amount;
      
      let status = 'غير مدفوعة';
      if (paidAmount >= totalAmount) {
        status = 'مدفوعة';
      } else if (paidAmount > 0) {
        status = 'مدفوعة جزئياً';
      }
      
      setInvoice(prev => ({
        ...prev,
        status
      }));
    }
    
    // تحديث نوع الدفع
    if (name === 'payment_type') {
      if (value === 'أقساط') {
        // إعادة تعيين المبلغ المدفوع إلى 0 عند اختيار الأقساط
        setInvoice(prev => ({
          ...prev,
          paid_amount: 0,
          status: 'غير مدفوعة'
        }));
      }
    }
  };
  
  // إرسال نموذج الفاتورة
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // التحقق من صحة المدخلات
      if (!invoice.order_id) {
        alert('يجب اختيار طلب');
        return;
      }
      
      if (!invoice.issue_date) {
        alert('يجب تحديد تاريخ الإصدار');
        return;
      }
      
      if (invoice.total_amount <= 0) {
        alert('يجب أن يكون المبلغ الإجمالي أكبر من صفر');
        return;
      }
      
      if (invoice.paid_amount < 0 || invoice.paid_amount > invoice.total_amount) {
        alert('المبلغ المدفوع غير صحيح');
        return;
      }
      
      // إذا كان نوع الدفع أقساط، الانتقال إلى الخطوة التالية
      if (invoice.payment_type === 'أقساط' && step === 1) {
        setStep(2);
        return;
      }
      
      // إنشاء الفاتورة
      const result = await window.api.invoices.create(invoice);
      
      if (result && result.id) {
        alert('تم إنشاء الفاتورة بنجاح');
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'create_invoice',
          action_details: `إنشاء فاتورة جديدة رقم ${result.invoice_number}`
        });
        
        // الانتقال إلى صفحة عرض الفاتورة
        navigate(`/invoices/${result.id}`);
      } else {
        throw new Error('فشل في إنشاء الفاتورة');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة:', error);
      alert('حدث خطأ أثناء إنشاء الفاتورة. يرجى المحاولة مرة أخرى.');
    }
  };
  
  // إنشاء فاتورة بالأقساط
  const handleCreateWithInstallments = async (invoiceData, installments) => {
    try {
      // إنشاء الفاتورة مع الأقساط
      const result = await window.api.invoices.createWithInstallments(invoiceData, installments);
      
      if (result && result.id) {
        alert('تم إنشاء الفاتورة والأقساط بنجاح');
        
        // إضافة سجل في سجل العمليات
        await window.api.activityLog.create({
          action_type: 'create_invoice',
          action_details: `إنشاء فاتورة جديدة بالأقساط رقم ${result.invoice_number}`
        });
        
        // الانتقال إلى صفحة عرض الفاتورة
        navigate(`/invoices/${result.id}`);
      } else {
        throw new Error('فشل في إنشاء الفاتورة');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة بالأقساط:', error);
      alert('حدث خطأ أثناء إنشاء الفاتورة بالأقساط. يرجى المحاولة مرة أخرى.');
    }
  };
  
  // العودة إلى الخطوة السابقة
  const handleBack = () => {
    setStep(1);
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  // عرض نموذج الأقساط
  if (step === 2) {
    return (
      <div className="create-invoice-page">
        <div className="page-header">
          <h2>إنشاء فاتورة بالأقساط</h2>
          <button className="btn btn-secondary" onClick={handleBack}>
            <i className="fas fa-arrow-right"></i> العودة
          </button>
        </div>
        
        <InstallmentsForm 
          invoice={invoice}
          onSubmit={handleCreateWithInstallments}
        />
      </div>
    );
  }
  
  return (
    <div className="create-invoice-page">
      <div className="page-header">
        <h2>إنشاء فاتورة جديدة</h2>
        <button className="btn btn-secondary" onClick={() => navigate('/invoices')}>
          <i className="fas fa-times"></i> إلغاء
        </button>
      </div>
      
      <div className="card">
        <div className="card-header">بيانات الفاتورة</div>
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">الطلب</label>
                  <input
                    type="text"
                    className="form-control"
                    value={order ? `${order.order_number} - ${order.customer_name}` : ''}
                    disabled
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">تاريخ الإصدار</label>
                  <input
                    type="date"
                    className="form-control"
                    name="issue_date"
                    value={invoice.issue_date}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">تاريخ الاستحقاق</label>
                  <input
                    type="date"
                    className="form-control"
                    name="due_date"
                    value={invoice.due_date}
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">المبلغ الإجمالي</label>
                  <input
                    type="number"
                    className="form-control"
                    name="total_amount"
                    value={invoice.total_amount}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label className="form-label">نوع الدفع</label>
                  <select
                    className="form-control"
                    name="payment_type"
                    value={invoice.payment_type}
                    onChange={handleChange}
                  >
                    <option value="كامل">دفعة واحدة</option>
                    <option value="أقساط">أقساط</option>
                  </select>
                </div>
              </div>
              
              {invoice.payment_type === 'كامل' && (
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">المبلغ المدفوع</label>
                    <input
                      type="number"
                      className="form-control"
                      name="paid_amount"
                      value={invoice.paid_amount}
                      onChange={handleChange}
                      min="0"
                      max={invoice.total_amount}
                      step="0.01"
                    />
                  </div>
                </div>
              )}
              
              {invoice.payment_type === 'كامل' && invoice.paid_amount > 0 && (
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">طريقة الدفع</label>
                    <select
                      className="form-control"
                      name="payment_method"
                      value={invoice.payment_method}
                      onChange={handleChange}
                    >
                      <option value="نقدي">نقدي</option>
                      <option value="شيك">شيك</option>
                      <option value="تحويل بنكي">تحويل بنكي</option>
                      <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
            
            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                name="notes"
                value={invoice.notes}
                onChange={handleChange}
                rows="3"
              ></textarea>
            </div>
            
            <div className="form-actions">
              <button type="submit" className="btn btn-primary">
                <i className="fas fa-save"></i> {invoice.payment_type === 'أقساط' ? 'متابعة لإعداد الأقساط' : 'حفظ الفاتورة'}
              </button>
              <button type="button" className="btn btn-secondary" onClick={() => navigate('/invoices')}>
                <i className="fas fa-times"></i> إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = CreateInvoice;
