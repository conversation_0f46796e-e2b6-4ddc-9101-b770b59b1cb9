const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ProductDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [product, setProduct] = useState(null);
  const [productOrders, setProductOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // تحميل بيانات المنتج
  useEffect(() => {
    const fetchProductData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل بيانات المنتج
        const productData = await window.api.products.getById(id);
        if (!productData) {
          throw new Error('لم يتم العثور على المنتج');
        }
        setProduct(productData);
        
        // تحميل طلبات المنتج
        const orders = await window.api.orders.getByProductId(id);
        setProductOrders(orders);
        
      } catch (error) {
        console.error('خطأ في تحميل بيانات المنتج:', error);
        setError('حدث خطأ أثناء تحميل بيانات المنتج. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProductData();
  }, [id]);
  
  // تعديل المنتج
  const handleEditProduct = () => {
    if (!hasPermission('products_edit')) {
      alert('ليس لديك صلاحية لتعديل المنتج');
      return;
    }
    
    navigate(`/products/edit/${id}`);
  };
  
  // حذف المنتج
  const handleDeleteProduct = async () => {
    if (!hasPermission('products_delete')) {
      alert('ليس لديك صلاحية لحذف المنتج');
      return;
    }
    
    if (productOrders.length > 0) {
      alert('لا يمكن حذف المنتج لأنه مرتبط بطلبات. يجب حذف الطلبات أولاً.');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        const result = await window.api.products.delete(id);
        if (result.success) {
          alert('تم حذف المنتج بنجاح');
          navigate('/products');
        } else {
          throw new Error('فشل في حذف المنتج');
        }
      } catch (error) {
        console.error('خطأ في حذف المنتج:', error);
        alert('حدث خطأ أثناء حذف المنتج. يرجى المحاولة مرة أخرى.');
      }
    }
  };
  
  // إنشاء طلب جديد باستخدام هذا المنتج
  const handleCreateOrder = () => {
    if (!hasPermission('orders_create')) {
      alert('ليس لديك صلاحية لإنشاء طلب جديد');
      return;
    }
    
    navigate(`/orders/new?product=${id}`);
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل بيانات المنتج...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  if (!product) {
    return <div className="alert alert-warning">لم يتم العثور على المنتج</div>;
  }
  
  return (
    <div className="product-details-page">
      <div className="page-header">
        <h2>تفاصيل المنتج</h2>
        <div className="page-actions">
          {hasPermission('products_edit') && (
            <button className="btn btn-primary" onClick={handleEditProduct}>
              <i className="fas fa-edit"></i> تعديل
            </button>
          )}
          
          {hasPermission('products_delete') && (
            <button className="btn btn-danger" onClick={handleDeleteProduct}>
              <i className="fas fa-trash"></i> حذف
            </button>
          )}
          
          {hasPermission('orders_create') && (
            <button className="btn btn-success" onClick={handleCreateOrder}>
              <i className="fas fa-plus"></i> إنشاء طلب جديد
            </button>
          )}
          
          <Link to="/products" className="btn btn-secondary">
            <i className="fas fa-arrow-right"></i> العودة للقائمة
          </Link>
        </div>
      </div>
      
      {/* بيانات المنتج */}
      <div className="card mb-4">
        <div className="card-header">معلومات المنتج</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="product-info-item">
                <span className="info-label">اسم المنتج:</span>
                <span className="info-value">{product.name}</span>
              </div>
              
              <div className="product-info-item">
                <span className="info-label">الفئة:</span>
                <span className="info-value">{product.category || '-'}</span>
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="product-info-item">
                <span className="info-label">السعر الافتراضي:</span>
                <span className="info-value">
                  {product.default_price ? `${product.default_price.toLocaleString()} ر.س` : '-'}
                </span>
              </div>
              
              <div className="product-info-item">
                <span className="info-label">الوصف:</span>
                <span className="info-value">{product.description || '-'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* طلبات المنتج */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>طلبات المنتج</h5>
            <span className="badge bg-primary">{productOrders.length} طلب</span>
          </div>
        </div>
        <div className="card-body">
          {productOrders.length === 0 ? (
            <div className="alert alert-info">لا توجد طلبات لهذا المنتج</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>العميل</th>
                    <th>تاريخ الطلب</th>
                    <th>تاريخ التسليم</th>
                    <th>الحالة</th>
                    <th>السعر النهائي</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {productOrders.map(order => (
                    <tr key={order.id}>
                      <td>{order.order_number}</td>
                      <td>{order.customer_name}</td>
                      <td>{new Date(order.order_date).toLocaleDateString('ar-SA')}</td>
                      <td>{order.delivery_date ? new Date(order.delivery_date).toLocaleDateString('ar-SA') : '-'}</td>
                      <td>
                        <span className={`status-badge ${order.status}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>{order.final_price.toLocaleString()} ر.س</td>
                      <td>
                        <Link to={`/orders/${order.id}`} className="btn btn-sm btn-info">
                          <i className="fas fa-eye"></i>
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = ProductDetails;
