const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const CustomerForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  // تحديد ما إذا كان النموذج للتعديل أو للإضافة
  const isEditMode = !!id;

  // حالة النموذج
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  });

  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // تحميل بيانات العميل في حالة التعديل
  useEffect(() => {
    const fetchCustomer = async () => {
      if (!isEditMode) return;

      try {
        setLoading(true);
        setError(null);

        const customerData = await window.api.customers.getById(id);
        if (!customerData) {
          throw new Error('لم يتم العثور على العميل');
        }

        setFormData({
          name: customerData.name || '',
          phone: customerData.phone || '',
          email: customerData.email || '',
          address: customerData.address || '',
          notes: customerData.notes || ''
        });
      } catch (error) {
        console.error('خطأ في تحميل بيانات العميل:', error);
        setError('حدث خطأ أثناء تحميل بيانات العميل. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [id, isEditMode]);

  // التحقق من الصلاحيات
  useEffect(() => {
    if (isEditMode && !hasPermission('customers_edit')) {
      alert('ليس لديك صلاحية لتعديل العملاء');
      navigate('/customers');
    } else if (!isEditMode && !hasPermission('customers_create')) {
      alert('ليس لديك صلاحية لإضافة عملاء جدد');
      navigate('/customers');
    }
  }, [isEditMode, hasPermission, navigate]);

  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم العميل');
      return false;
    }

    // التحقق من صحة رقم الهاتف (اختياري)
    if (formData.phone && !/^[0-9+\s-]{8,15}$/.test(formData.phone)) {
      alert('يرجى إدخال رقم هاتف صحيح');
      return false;
    }

    // التحقق من صحة البريد الإلكتروني (اختياري)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      alert('يرجى إدخال بريد إلكتروني صحيح');
      return false;
    }

    return true;
  };

  // حفظ البيانات
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      let result;

      if (isEditMode) {
        // تعديل العميل
        result = await window.api.customers.update(id, formData);
        if (result.success) {
          alert('تم تعديل العميل بنجاح');
          navigate(`/customers/${id}`);
        } else {
          throw new Error('فشل في تعديل العميل');
        }
      } else {
        // إضافة عميل جديد
        result = await window.api.customers.create(formData);
        if (result.success) {
          alert('تم إضافة العميل بنجاح');
          navigate(`/customers/${result.id}`);
        } else {
          throw new Error('فشل في إضافة العميل');
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ بيانات العميل:', error);
      setError('حدث خطأ أثناء حفظ بيانات العميل. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }

  return (
    <div className="customer-form-page">
      <div className="page-header">
        <h2>{isEditMode ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}</h2>
        <div className="page-actions">
          <Link to={isEditMode ? `/customers/${id}` : '/customers'} className="btn btn-secondary">
            <i className="fas fa-times"></i> إلغاء
          </Link>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">اسم العميل <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">رقم الهاتف</label>
                  <input
                    type="text"
                    className="form-control"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="05xxxxxxxx"
                  />
                </div>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">البريد الإلكتروني</label>
                  <input
                    type="email"
                    className="form-control"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">العنوان</label>
                  <input
                    type="text"
                    className="form-control"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            <div className="form-group mb-3">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows="3"
              ></textarea>
            </div>

            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span className="ms-1">جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    <span className="ms-1">{isEditMode ? 'حفظ التعديلات' : 'إضافة العميل'}</span>
                  </>
                )}
              </button>

              <Link to={isEditMode ? `/customers/${id}` : '/customers'} className="btn btn-secondary">
                <i className="fas fa-times"></i> إلغاء
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = CustomerForm;
