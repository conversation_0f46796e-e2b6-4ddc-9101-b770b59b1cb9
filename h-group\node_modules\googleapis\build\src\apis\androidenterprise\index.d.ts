/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { androidenterprise_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof androidenterprise_v1.Androidenterprise;
};
export declare function androidenterprise(version: 'v1'): androidenterprise_v1.Androidenterprise;
export declare function androidenterprise(options: androidenterprise_v1.Options): androidenterprise_v1.Androidenterprise;
declare const auth: AuthPlus;
export { auth };
export { androidenterprise_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
