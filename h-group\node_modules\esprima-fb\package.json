{"name": "esprima-fb", "description": "Facebook-specific fork of the esprima project", "homepage": "https://github.com/facebook/esprima/tree/fb-harmony", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "3001.0001.0000-dev-harmony-fb", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "https://www.facebook.com/lbljeffmo"}], "repository": {"type": "git", "url": "http://github.com/facebook/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/facebook/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.1.0", "istanbul": "~0.1.27", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1"}, "scripts": {"test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "node tools/check-version.js && node_modules/eslint/bin/eslint.js esprima.js && node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement -8 --branch -28 --function 99.69", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 31 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}}