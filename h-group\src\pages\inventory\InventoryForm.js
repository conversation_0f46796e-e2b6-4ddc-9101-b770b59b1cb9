const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const InventoryForm = () => {
  const { id } = useParams(); // معرف المادة الخام
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [material, setMaterial] = useState(null);
  const [inventory, setInventory] = useState(null);
  
  const [formData, setFormData] = useState({
    operation: 'add', // add, subtract, set
    quantity: '',
    notes: ''
  });
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // تحميل بيانات المادة الخام والمخزون
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // التحقق من الصلاحيات
        if (!hasPermission('inventory_edit')) {
          alert('ليس لديك صلاحية لتحديث المخزون');
          navigate('/inventory');
          return;
        }
        
        // تحميل بيانات المادة الخام
        const materialData = await window.api.materials.getById(id);
        if (!materialData) {
          throw new Error('لم يتم العثور على المادة الخام');
        }
        setMaterial(materialData);
        
        // تحميل بيانات المخزون
        const inventoryData = await window.api.inventory.getByMaterialId(id);
        setInventory(inventoryData);
        
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        setError('حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, hasPermission, navigate]);
  
  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // معالجة تغيير الكمية (للتأكد من أنها رقم)
  const handleQuantityChange = (e) => {
    const value = e.target.value;
    if (value === '' || /^\d+(\.\d{0,2})?$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        quantity: value
      }));
    }
  };
  
  // التحقق من صحة البيانات
  const validateForm = () => {
    if (!formData.quantity.trim() || parseFloat(formData.quantity) <= 0) {
      alert('يرجى إدخال كمية صحيحة أكبر من صفر');
      return false;
    }
    
    // التحقق من عدم طرح كمية أكبر من المتوفرة
    if (formData.operation === 'subtract') {
      const currentQuantity = inventory ? inventory.quantity : 0;
      const subtractQuantity = parseFloat(formData.quantity);
      
      if (subtractQuantity > currentQuantity) {
        alert(`لا يمكن طرح كمية أكبر من المتوفرة. الكمية المتوفرة حالياً: ${currentQuantity} ${material.unit}`);
        return false;
      }
    }
    
    return true;
  };
  
  // حساب الكمية الجديدة
  const calculateNewQuantity = () => {
    const currentQuantity = inventory ? inventory.quantity : 0;
    const inputQuantity = parseFloat(formData.quantity);
    
    switch (formData.operation) {
      case 'add':
        return currentQuantity + inputQuantity;
      case 'subtract':
        return currentQuantity - inputQuantity;
      case 'set':
        return inputQuantity;
      default:
        return currentQuantity;
    }
  };
  
  // حفظ البيانات
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setSubmitting(true);
      setError(null);
      
      const newQuantity = calculateNewQuantity();
      
      // تحديث المخزون
      const result = await window.api.inventory.update(id, newQuantity);
      
      if (result.success) {
        // إنشاء سجل حركة المخزون
        const movementData = {
          material_id: id,
          operation: formData.operation,
          quantity: parseFloat(formData.quantity),
          previous_quantity: inventory ? inventory.quantity : 0,
          new_quantity: newQuantity,
          notes: formData.notes,
          user_id: null // سيتم تعيينه في الخلفية
        };
        
        await window.api.inventory.addMovement(movementData);
        
        alert('تم تحديث المخزون بنجاح');
        navigate('/inventory');
      } else {
        throw new Error('فشل في تحديث المخزون');
      }
    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);
      setError('حدث خطأ أثناء تحديث المخزون. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  if (!material) {
    return <div className="alert alert-warning">لم يتم العثور على المادة الخام</div>;
  }
  
  return (
    <div className="inventory-form-page">
      <div className="page-header">
        <h2>تحديث المخزون</h2>
        <div className="page-actions">
          <Link to="/inventory" className="btn btn-secondary">
            <i className="fas fa-times"></i> إلغاء
          </Link>
        </div>
      </div>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="row">
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">معلومات المادة الخام</div>
            <div className="card-body">
              <div className="material-info-item">
                <span className="info-label">اسم المادة:</span>
                <span className="info-value">{material.name}</span>
              </div>
              
              <div className="material-info-item">
                <span className="info-label">وحدة القياس:</span>
                <span className="info-value">{material.unit || '-'}</span>
              </div>
              
              <div className="material-info-item">
                <span className="info-label">الكمية الحالية:</span>
                <span className="info-value">
                  <strong className={inventory && inventory.quantity < material.min_quantity ? 'text-danger' : 'text-success'}>
                    {inventory ? inventory.quantity : 0} {material.unit}
                  </strong>
                </span>
              </div>
              
              <div className="material-info-item">
                <span className="info-label">الحد الأدنى:</span>
                <span className="info-value">{material.min_quantity || 0} {material.unit}</span>
              </div>
              
              <div className="material-info-item">
                <span className="info-label">حالة المخزون:</span>
                <span className="info-value">
                  <span className={`badge ${inventory && inventory.quantity < material.min_quantity ? 'bg-danger' : 'bg-success'}`}>
                    {inventory && inventory.quantity < material.min_quantity ? 'منخفض' : 'متوفر'}
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">تحديث المخزون</div>
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                <div className="form-group mb-3">
                  <label className="form-label">نوع العملية <span className="text-danger">*</span></label>
                  <div className="form-check">
                    <input
                      type="radio"
                      className="form-check-input"
                      id="operationAdd"
                      name="operation"
                      value="add"
                      checked={formData.operation === 'add'}
                      onChange={handleChange}
                    />
                    <label className="form-check-label" htmlFor="operationAdd">
                      إضافة كمية
                    </label>
                  </div>
                  
                  <div className="form-check">
                    <input
                      type="radio"
                      className="form-check-input"
                      id="operationSubtract"
                      name="operation"
                      value="subtract"
                      checked={formData.operation === 'subtract'}
                      onChange={handleChange}
                    />
                    <label className="form-check-label" htmlFor="operationSubtract">
                      طرح كمية
                    </label>
                  </div>
                  
                  <div className="form-check">
                    <input
                      type="radio"
                      className="form-check-input"
                      id="operationSet"
                      name="operation"
                      value="set"
                      checked={formData.operation === 'set'}
                      onChange={handleChange}
                    />
                    <label className="form-check-label" htmlFor="operationSet">
                      تعيين كمية محددة
                    </label>
                  </div>
                </div>
                
                <div className="form-group mb-3">
                  <label className="form-label">الكمية <span className="text-danger">*</span></label>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      name="quantity"
                      value={formData.quantity}
                      onChange={handleQuantityChange}
                      placeholder="0"
                      required
                    />
                    <span className="input-group-text">{material.unit}</span>
                  </div>
                </div>
                
                <div className="form-group mb-3">
                  <label className="form-label">ملاحظات</label>
                  <textarea
                    className="form-control"
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    rows="3"
                    placeholder="سبب التحديث أو أي ملاحظات إضافية"
                  ></textarea>
                </div>
                
                <div className="form-group mb-3">
                  <label className="form-label">الكمية الجديدة بعد التحديث:</label>
                  <div className="new-quantity-preview">
                    <strong className={calculateNewQuantity() < material.min_quantity ? 'text-danger' : 'text-success'}>
                      {calculateNewQuantity()} {material.unit}
                    </strong>
                    
                    {calculateNewQuantity() < material.min_quantity && (
                      <div className="text-danger mt-1">
                        <i className="fas fa-exclamation-triangle"></i> تحذير: الكمية الجديدة أقل من الحد الأدنى ({material.min_quantity} {material.unit})
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="form-actions">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span className="ms-1">جاري الحفظ...</span>
                      </>
                    ) : (
                      <>
                        <i className="fas fa-save"></i>
                        <span className="ms-1">تحديث المخزون</span>
                      </>
                    )}
                  </button>
                  
                  <Link to="/inventory" className="btn btn-secondary">
                    <i className="fas fa-times"></i> إلغاء
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

module.exports = InventoryForm;
