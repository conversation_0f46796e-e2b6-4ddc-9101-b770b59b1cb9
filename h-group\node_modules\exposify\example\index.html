<!DOCTYPE html>
<html>
<head>
  <meta charset=utf-8 />
  <title>exposify example</title>
  
  <script type="text/javascript" src="http://cdnjs.cloudflare.com/ajax/libs/three.js/r61/three.min.js"></script>
  <script type="text/javascript" src="http://code.jquery.com/jquery-2.0.3.min.js"></script>
</head>
<body>
  <h2>exposify example</h2>

  <p>Both jquery and three.js are loaded from a cdn via a script tag inside the head</p>
  <p>
    <span>However they are required as usual inside our modules, i.e. <pre><code>var $ = require('jquery')</code></pre></span>
    <span>exposify makes the adjustements to make this all work</span>
  </p>

  <p>Please open the dev console to see logged versions of both libraries and investigate the <pre>main.js</pre> in the sources tab
     in order to find out how it works (all jquery and three require calls were replaced with assignments from the window).
  </p>

  <script type="text/javascript" src="bundle.js"></script>
</body>
</html>
