/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { youtubereporting_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof youtubereporting_v1.Youtubereporting;
};
export declare function youtubereporting(version: 'v1'): youtubereporting_v1.Youtubereporting;
export declare function youtubereporting(options: youtubereporting_v1.Options): youtubereporting_v1.Youtubereporting;
declare const auth: AuthPlus;
export { auth };
export { youtubereporting_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
