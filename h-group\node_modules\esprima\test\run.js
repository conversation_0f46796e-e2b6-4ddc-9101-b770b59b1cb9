/*
  Copyright (C) 2012 <PERSON><PERSON> <<EMAIL>>
  Copyright (C) 2012 <PERSON><PERSON> Hidayat <<EMAIL>>

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/*jslint node:true */

(function () {
    'use strict';

    var child = require('child_process'),
        nodejs = '"' + process.execPath + '"',
        ret = 0,
        suites,
        index;

    suites = [
        'runner',
        'compat'
    ];

    function nextTest() {
        var suite = suites[index];

        if (index < suites.length) {
            child.exec(nodejs + ' ./test/' + suite + '.js', function (err, stdout, stderr) {
                if (stdout) {
                    process.stdout.write(suite + ': ' + stdout);
                }
                if (stderr) {
                    process.stderr.write(suite + ': ' + stderr);
                }
                if (err) {
                    ret = err.code;
                }
                index += 1;
                nextTest();
            });
        } else {
            process.exit(ret);
        }
    }

    index = 0;
    nextTest();
}());
