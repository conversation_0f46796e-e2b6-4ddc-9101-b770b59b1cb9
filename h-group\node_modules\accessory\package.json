{"name": "accessory", "main": "index.js", "version": "1.1.0", "description": "Create property accessor/caller statements for dot paths", "license": "MIT", "repository": "bendrucker/accessory", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "scripts": {"test": "standard && tape test.js"}, "keywords": ["dot", "path", "property", "accessor", "bracket", "js"], "devDependencies": {"tape": "^4.0.0", "standard": "^4.0.0"}, "files": ["index.js"], "dependencies": {"ap": "~0.2.0", "balanced-match": "~0.2.0", "dot-parts": "~1.0.0"}}