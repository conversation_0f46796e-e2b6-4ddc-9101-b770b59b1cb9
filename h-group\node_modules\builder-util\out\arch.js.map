{"version": 3, "file": "arch.js", "sourceRoot": "", "sources": ["../src/arch.ts"], "names": [], "mappings": ";;;AAAA,IAAY,IAMX;AAND,WAAY,IAAI;IACd,+BAAI,CAAA;IACJ,6BAAG,CAAA;IACH,mCAAM,CAAA;IACN,iCAAK,CAAA;IACL,yCAAS,CAAA;AACX,CAAC,EANW,IAAI,oBAAJ,IAAI,QAMf;AAID,SAAgB,iBAAiB,CAAC,IAAU,EAAE,UAAkB;IAC9D,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,IAAI,CAAC,GAAG;YACX,OAAO,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAA;QACtD,KAAK,IAAI,CAAC,IAAI;YACZ,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAA;QAClD,KAAK,IAAI,CAAC,MAAM;YACd,OAAO,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAA;QAC9G,KAAK,IAAI,CAAC,KAAK;YACb,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAA;QAE1G;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAA;IAC/C,CAAC;AACH,CAAC;AAdD,8CAcC;AAED,SAAgB,eAAe;IAC7B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;AAC/E,CAAC;AAFD,0CAEC;AAED,SAAgB,aAAa,CAAC,IAAU,EAAE,WAAoB;IAC5D,OAAO,IAAI,KAAK,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;AAC5E,CAAC;AAFD,sCAEC;AAED,SAAgB,cAAc,CAAC,IAAY;IACzC,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,KAAK;YACR,OAAO,IAAI,CAAC,GAAG,CAAA;QACjB,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,IAAI,CAAA;QAClB,KAAK,OAAO;YACV,OAAO,IAAI,CAAC,KAAK,CAAA;QACnB,KAAK,KAAK,CAAC;QACX,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC,MAAM,CAAA;QACpB,KAAK,WAAW;YACd,OAAO,IAAI,CAAC,SAAS,CAAA;QACvB;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAA;IAC/C,CAAC;AACH,CAAC;AAhBD,wCAgBC;AAED,SAAgB,qBAAqB,CAAC,IAAa;IACjD,OAAO,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAA;AAC/C,CAAC;AAFD,sDAEC;AAED,SAAgB,mBAAmB,CAAC,IAAU,EAAE,GAAW;IACzD,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;IACzB,MAAM,UAAU,GAAG,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,UAAU,CAAA;IAC3D,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;QACtB,IAAI,UAAU,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACrD,QAAQ,GAAG,QAAQ,CAAA;QACrB,CAAC;aAAM,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;YAC3C,QAAQ,GAAG,OAAO,CAAA;QACpB,CAAC;IACH,CAAC;SAAM,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAC9B,IAAI,GAAG,KAAK,KAAK,IAAI,UAAU,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACvE,QAAQ,GAAG,MAAM,CAAA;QACnB,CAAC;aAAM,IAAI,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAC7C,QAAQ,GAAG,MAAM,CAAA;QACnB,CAAC;IACH,CAAC;SAAM,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QAChC,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;YACnB,QAAQ,GAAG,OAAO,CAAA;QACpB,CAAC;aAAM,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YAC7B,QAAQ,GAAG,KAAK,CAAA;QAClB,CAAC;IACH,CAAC;SAAM,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YAC3D,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AA3BD,kDA2BC", "sourcesContent": ["export enum Arch {\n  ia32,\n  x64,\n  armv7l,\n  arm64,\n  universal,\n}\n\nexport type ArchType = \"x64\" | \"ia32\" | \"armv7l\" | \"arm64\" | \"universal\"\n\nexport function toLinuxArchString(arch: Arch, targetName: string): string {\n  switch (arch) {\n    case Arch.x64:\n      return targetName === \"flatpak\" ? \"x86_64\" : \"amd64\"\n    case Arch.ia32:\n      return targetName === \"pacman\" ? \"i686\" : \"i386\"\n    case Arch.armv7l:\n      return targetName === \"snap\" || targetName === \"deb\" ? \"armhf\" : targetName === \"flatpak\" ? \"arm\" : \"armv7l\"\n    case Arch.arm64:\n      return targetName === \"pacman\" || targetName === \"rpm\" || targetName === \"flatpak\" ? \"aarch64\" : \"arm64\"\n\n    default:\n      throw new Error(`Unsupported arch ${arch}`)\n  }\n}\n\nexport function getArchCliNames(): Array<string> {\n  return [Arch[Arch.ia32], Arch[Arch.x64], Arch[Arch.armv7l], Arch[Arch.arm64]]\n}\n\nexport function getArchSuffix(arch: Arch, defaultArch?: string): string {\n  return arch === defaultArchFromString(defaultArch) ? \"\" : `-${Arch[arch]}`\n}\n\nexport function archFromString(name: string): Arch {\n  switch (name) {\n    case \"x64\":\n      return Arch.x64\n    case \"ia32\":\n      return Arch.ia32\n    case \"arm64\":\n      return Arch.arm64\n    case \"arm\":\n    case \"armv7l\":\n      return Arch.armv7l\n    case \"universal\":\n      return Arch.universal\n    default:\n      throw new Error(`Unsupported arch ${name}`)\n  }\n}\n\nexport function defaultArchFromString(name?: string): Arch {\n  return name ? archFromString(name) : Arch.x64\n}\n\nexport function getArtifactArchName(arch: Arch, ext: string): string {\n  let archName = Arch[arch]\n  const isAppImage = ext === \"AppImage\" || ext === \"appimage\"\n  if (arch === Arch.x64) {\n    if (isAppImage || ext === \"rpm\" || ext === \"flatpak\") {\n      archName = \"x86_64\"\n    } else if (ext === \"deb\" || ext === \"snap\") {\n      archName = \"amd64\"\n    }\n  } else if (arch === Arch.ia32) {\n    if (ext === \"deb\" || isAppImage || ext === \"snap\" || ext === \"flatpak\") {\n      archName = \"i386\"\n    } else if (ext === \"pacman\" || ext === \"rpm\") {\n      archName = \"i686\"\n    }\n  } else if (arch === Arch.armv7l) {\n    if (ext === \"snap\") {\n      archName = \"armhf\"\n    } else if (ext === \"flatpak\") {\n      archName = \"arm\"\n    }\n  } else if (arch === Arch.arm64) {\n    if (ext === \"pacman\" || ext === \"rpm\" || ext === \"flatpak\") {\n      archName = \"aarch64\"\n    }\n  }\n  return archName\n}\n"]}