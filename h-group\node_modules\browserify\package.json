{"name": "browserify", "version": "17.0.1", "description": "browser-side require() the node way", "main": "index.js", "bin": {"browserify": "bin/cmd.js"}, "repository": {"type": "git", "url": "http://github.com/browserify/browserify.git"}, "engines": {"node": ">= 0.8"}, "keywords": ["browser", "require", "commonjs", "commonj-esque", "bundle", "npm", "javascript"], "dependencies": {"assert": "^1.4.0", "browser-pack": "^6.0.1", "browser-resolve": "^2.0.0", "browserify-zlib": "~0.2.0", "buffer": "~5.2.1", "cached-path-relative": "^1.0.0", "concat-stream": "^1.6.0", "console-browserify": "^1.1.0", "constants-browserify": "~1.0.0", "crypto-browserify": "^3.0.0", "defined": "^1.0.0", "deps-sort": "^2.0.1", "domain-browser": "^1.2.0", "duplexer2": "~0.1.2", "events": "^3.0.0", "glob": "^7.1.0", "hasown": "^2.0.0", "htmlescape": "^1.1.0", "https-browserify": "^1.0.0", "inherits": "~2.0.1", "insert-module-globals": "^7.2.1", "JSONStream": "^1.0.3", "labeled-stream-splicer": "^2.0.0", "mkdirp-classic": "^0.5.2", "module-deps": "^6.2.3", "os-browserify": "~0.3.0", "parents": "^1.0.1", "path-browserify": "^1.0.0", "process": "~0.11.0", "punycode": "^1.3.2", "querystring-es3": "~0.2.0", "read-only-stream": "^2.0.0", "readable-stream": "^2.0.2", "resolve": "^1.1.4", "shasum-object": "^1.0.0", "shell-quote": "^1.6.1", "stream-browserify": "^3.0.0", "stream-http": "^3.0.0", "string_decoder": "^1.1.1", "subarg": "^1.0.0", "syntax-error": "^1.1.1", "through2": "^2.0.0", "timers-browserify": "^1.0.1", "tty-browserify": "0.0.1", "url": "~0.11.0", "util": "~0.12.0", "vm-browserify": "^1.0.0", "xtend": "^4.0.0"}, "devDependencies": {"backbone": "~0.9.2", "browser-unpack": "^1.1.1", "coffee-script": "~1.10.0", "coffeeify": "~1.1.0", "has-object-spread": "^1.0.0", "has-template-literals": "^1.0.0", "isstream": "^0.1.2", "make-generator-function": "^1.1.0", "semver": "^5.5.0", "seq": "0.3.5", "tap": "^10.7.2", "temp": "=0.8.3", "through": "^2.3.4"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "scripts": {"test": "tap test/*.js"}, "license": "MIT"}