const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');

const CustomersList = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setCustomers([
        { id: 1, name: 'أحمد محمد', phone: '0501234567', email: '<EMAIL>' },
        { id: 2, name: 'فاطمة علي', phone: '0507654321', email: '<EMAIL>' },
        { id: 3, name: 'محمد سالم', phone: '0509876543', email: '<EMAIL>' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل العملاء...');
  }

  return React.createElement('div', { className: 'customers-list-page' },
    React.createElement('div', { className: 'page-header' },
      React.createElement('h2', null, 'العملاء'),
      React.createElement('div', { className: 'page-actions' },
        React.createElement('button', {
          className: 'btn btn-primary',
          onClick: () => navigate('/customers/new')
        }, 'إضافة عميل جديد')
      )
    ),

    React.createElement('div', { className: 'card' },
      React.createElement('div', { className: 'card-header' },
        React.createElement('h5', null, 'قائمة العملاء'),
        React.createElement('span', { className: 'badge bg-primary' }, `${customers.length} عميل`)
      ),
      React.createElement('div', { className: 'card-body' },
        React.createElement('div', { className: 'table-responsive' },
          React.createElement('table', { className: 'table table-hover' },
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'الاسم'),
                React.createElement('th', null, 'الهاتف'),
                React.createElement('th', null, 'البريد الإلكتروني'),
                React.createElement('th', null, 'الإجراءات')
              )
            ),
            React.createElement('tbody', null,
              customers.map(customer =>
                React.createElement('tr', { key: customer.id },
                  React.createElement('td', null, customer.name),
                  React.createElement('td', null, customer.phone),
                  React.createElement('td', null, customer.email),
                  React.createElement('td', null,
                    React.createElement('div', { className: 'btn-group' },
                      React.createElement('button', {
                        className: 'btn btn-sm btn-info',
                        onClick: () => navigate(`/customers/${customer.id}`)
                      }, 'عرض'),
                      React.createElement('button', {
                        className: 'btn btn-sm btn-primary',
                        onClick: () => navigate(`/customers/edit/${customer.id}`)
                      }, 'تعديل')
                    )
                  )
                )
              )
            )
          )
        )
      )
    )
  );
};

module.exports = CustomersList;
