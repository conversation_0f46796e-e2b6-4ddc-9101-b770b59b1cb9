# browserify-sign

[![NPM Package](https://img.shields.io/npm/v/browserify-sign.svg?style=flat-square)](https://www.npmjs.org/package/browserify-sign)
[![Build Status](https://img.shields.io/travis/crypto-browserify/browserify-sign.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/browserify-sign)
[![Dependency status](https://img.shields.io/david/crypto-browserify/browserify-sign.svg?style=flat-square)](https://david-dm.org/crypto-browserify/browserify-sign#info=dependencies)

[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)

A package to duplicate the functionality of node's crypto public key functions, much of this is based on [<PERSON><PERSON>du<PERSON>'s](https://github.com/indutny) work on [indutny/tls.js](https://github.com/indutny/tls.js).

## LICENSE

ISC
