import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

console.log('تم تحميل webpack-entry.js');

// تحميل تطبيق React عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  const appDiv = document.getElementById('app');
  
  if (appDiv) {
    // تنظيف محتوى العنصر
    appDiv.innerHTML = '';
    
    // تحميل تطبيق React
    ReactDOM.render(<App />, appDiv);
    
    console.log('تم تحميل تطبيق React بنجاح من webpack');
  } else {
    console.error('لم يتم العثور على عنصر #app');
  }
});

// تصدير للاستخدام العام
window.React = React;
window.ReactDOM = ReactDOM;
