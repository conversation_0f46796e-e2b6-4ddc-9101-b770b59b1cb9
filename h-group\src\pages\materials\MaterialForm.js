const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const MaterialForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  // تحديد ما إذا كان النموذج للتعديل أو للإضافة
  const isEditMode = !!id;
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    unit: '',
    cost_per_unit: '',
    min_quantity: '',
    initial_quantity: '' // فقط للإضافة الجديدة
  });
  
  const [units, setUnits] = useState(['متر', 'قطعة', 'كيلوجرام', 'لتر', 'مربع', 'طن']);
  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // تحميل بيانات المادة الخام في حالة التعديل
  useEffect(() => {
    const fetchMaterial = async () => {
      if (!isEditMode) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const materialData = await window.api.materials.getById(id);
        if (!materialData) {
          throw new Error('لم يتم العثور على المادة الخام');
        }
        
        // تحميل بيانات المخزون
        const inventoryData = await window.api.inventory.getByMaterialId(id);
        
        setFormData({
          name: materialData.name || '',
          description: materialData.description || '',
          unit: materialData.unit || '',
          cost_per_unit: materialData.cost_per_unit ? materialData.cost_per_unit.toString() : '',
          min_quantity: materialData.min_quantity ? materialData.min_quantity.toString() : '',
          initial_quantity: inventoryData ? inventoryData.quantity.toString() : '0'
        });
        
        // إضافة الوحدة إلى القائمة إذا لم تكن موجودة
        if (materialData.unit && !units.includes(materialData.unit)) {
          setUnits(prev => [...prev, materialData.unit]);
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات المادة الخام:', error);
        setError('حدث خطأ أثناء تحميل بيانات المادة الخام. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMaterial();
  }, [id, isEditMode]);
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (isEditMode && !hasPermission('materials_edit')) {
      alert('ليس لديك صلاحية لتعديل المواد الخام');
      navigate('/materials');
    } else if (!isEditMode && !hasPermission('materials_create')) {
      alert('ليس لديك صلاحية لإضافة مواد خام جديدة');
      navigate('/materials');
    }
  }, [isEditMode, hasPermission, navigate]);
  
  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // معالجة تغيير القيم الرقمية (للتأكد من أنها أرقام)
  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    if (value === '' || /^\d+(\.\d{0,2})?$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // إضافة وحدة قياس جديدة
  const handleAddUnit = () => {
    const newUnit = prompt('أدخل وحدة القياس الجديدة:');
    if (newUnit && newUnit.trim() && !units.includes(newUnit.trim())) {
      setUnits([...units, newUnit.trim()]);
      setFormData(prev => ({
        ...prev,
        unit: newUnit.trim()
      }));
    }
  };
  
  // التحقق من صحة البيانات
  const validateForm = () => {
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم المادة الخام');
      return false;
    }
    
    if (!formData.unit.trim()) {
      alert('يرجى اختيار وحدة القياس');
      return false;
    }
    
    if (!formData.cost_per_unit.trim()) {
      alert('يرجى إدخال التكلفة لكل وحدة');
      return false;
    }
    
    if (!formData.min_quantity.trim()) {
      alert('يرجى إدخال الحد الأدنى للمخزون');
      return false;
    }
    
    if (!isEditMode && !formData.initial_quantity.trim()) {
      alert('يرجى إدخال الكمية الأولية');
      return false;
    }
    
    return true;
  };
  
  // حفظ البيانات
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setSubmitting(true);
      setError(null);
      
      // تحويل القيم إلى أرقام
      const materialData = {
        name: formData.name,
        description: formData.description,
        unit: formData.unit,
        cost_per_unit: parseFloat(formData.cost_per_unit),
        min_quantity: parseInt(formData.min_quantity, 10)
      };
      
      let result;
      
      if (isEditMode) {
        // تعديل المادة الخام
        result = await window.api.materials.update(id, materialData);
        
        if (result.success) {
          // تحديث المخزون إذا تغيرت الكمية
          const inventoryData = await window.api.inventory.getByMaterialId(id);
          const currentQuantity = inventoryData ? inventoryData.quantity : 0;
          const newQuantity = parseInt(formData.initial_quantity, 10);
          
          if (currentQuantity !== newQuantity) {
            await window.api.inventory.update(id, newQuantity);
          }
          
          alert('تم تعديل المادة الخام بنجاح');
          navigate(`/materials/${id}`);
        } else {
          throw new Error('فشل في تعديل المادة الخام');
        }
      } else {
        // إضافة مادة خام جديدة
        result = await window.api.materials.create(materialData);
        
        if (result.success) {
          // إضافة المادة الخام للمخزون
          const initialQuantity = parseInt(formData.initial_quantity, 10);
          if (initialQuantity > 0) {
            await window.api.inventory.update(result.id, initialQuantity);
          }
          
          alert('تم إضافة المادة الخام بنجاح');
          navigate(`/materials/${result.id}`);
        } else {
          throw new Error('فشل في إضافة المادة الخام');
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ بيانات المادة الخام:', error);
      setError('حدث خطأ أثناء حفظ بيانات المادة الخام. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  return (
    <div className="material-form-page">
      <div className="page-header">
        <h2>{isEditMode ? 'تعديل بيانات المادة الخام' : 'إضافة مادة خام جديدة'}</h2>
        <div className="page-actions">
          <Link to={isEditMode ? `/materials/${id}` : '/materials'} className="btn btn-secondary">
            <i className="fas fa-times"></i> إلغاء
          </Link>
        </div>
      </div>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">اسم المادة الخام <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">وحدة القياس <span className="text-danger">*</span></label>
                  <div className="input-group">
                    <select
                      className="form-control"
                      name="unit"
                      value={formData.unit}
                      onChange={handleChange}
                      required
                    >
                      <option value="">اختر وحدة القياس</option>
                      {units.map((unit, index) => (
                        <option key={index} value={unit}>{unit}</option>
                      ))}
                    </select>
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={handleAddUnit}
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-4">
                <div className="form-group mb-3">
                  <label className="form-label">التكلفة لكل وحدة (ر.س) <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="cost_per_unit"
                    value={formData.cost_per_unit}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-4">
                <div className="form-group mb-3">
                  <label className="form-label">الحد الأدنى للمخزون <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="min_quantity"
                    value={formData.min_quantity}
                    onChange={handleNumberChange}
                    placeholder="0"
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-4">
                <div className="form-group mb-3">
                  <label className="form-label">
                    {isEditMode ? 'الكمية الحالية' : 'الكمية الأولية'} <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    name="initial_quantity"
                    value={formData.initial_quantity}
                    onChange={handleNumberChange}
                    placeholder="0"
                    required
                  />
                </div>
              </div>
            </div>
            
            <div className="form-group mb-3">
              <label className="form-label">الوصف</label>
              <textarea
                className="form-control"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
              ></textarea>
            </div>
            
            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span className="ms-1">جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    <span className="ms-1">{isEditMode ? 'حفظ التعديلات' : 'إضافة المادة الخام'}</span>
                  </>
                )}
              </button>
              
              <Link to={isEditMode ? `/materials/${id}` : '/materials'} className="btn btn-secondary">
                <i className="fas fa-times"></i> إلغاء
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = MaterialForm;
