const React = require('react');
const { useState } = React;
const { useNavigate } = require('react-router-dom');

const OrderForm = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  return React.createElement('div', { className: 'order-form-page' },
    React.createElement('div', { className: 'page-header' },
      React.createElement('h2', null, 'إضافة طلب جديد'),
      React.createElement('div', { className: 'page-actions' },
        React.createElement('button', {
          className: 'btn btn-secondary',
          onClick: () => navigate('/orders')
        }, 'العودة إلى قائمة الطلبات')
      )
    ),

    React.createElement('div', { className: 'card' },
      React.createElement('div', { className: 'card-header' },
        React.createElement('h5', null, 'معلومات الطلب')
      ),
      React.createElement('div', { className: 'card-body' },
        React.createElement('div', { className: 'alert alert-info' },
          'صفحة إضافة الطلبات قيد التطوير. يرجى المحاولة لاحقاً.'
        ),
        React.createElement('div', { className: 'form-actions' },
          React.createElement('button', {
            className: 'btn btn-primary',
            disabled: true
          }, 'حفظ الطلب'),
          React.createElement('button', {
            className: 'btn btn-secondary',
            onClick: () => navigate('/orders')
          }, 'إلغاء')
        )
      )
    )
  );
};

module.exports = OrderForm;
