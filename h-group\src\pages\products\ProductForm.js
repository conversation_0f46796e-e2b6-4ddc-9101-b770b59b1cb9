const React = require('react');
const { useState, useEffect } = React;
const { useParams, useNavigate, Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const ProductForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  // تحديد ما إذا كان النموذج للتعديل أو للإضافة
  const isEditMode = !!id;
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    default_price: ''
  });
  
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // تحميل بيانات المنتج في حالة التعديل وتحميل الفئات
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل جميع المنتجات للحصول على الفئات
        const allProducts = await window.api.products.getAll();
        const uniqueCategories = [...new Set(allProducts.map(product => product.category))].filter(Boolean);
        setCategories(uniqueCategories);
        
        // تحميل بيانات المنتج في حالة التعديل
        if (isEditMode) {
          const productData = await window.api.products.getById(id);
          if (!productData) {
            throw new Error('لم يتم العثور على المنتج');
          }
          
          setFormData({
            name: productData.name || '',
            description: productData.description || '',
            category: productData.category || '',
            default_price: productData.default_price ? productData.default_price.toString() : ''
          });
        }
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        setError('حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, isEditMode]);
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (isEditMode && !hasPermission('products_edit')) {
      alert('ليس لديك صلاحية لتعديل المنتجات');
      navigate('/products');
    } else if (!isEditMode && !hasPermission('products_create')) {
      alert('ليس لديك صلاحية لإضافة منتجات جديدة');
      navigate('/products');
    }
  }, [isEditMode, hasPermission, navigate]);
  
  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // معالجة تغيير السعر (للتأكد من أنه رقم)
  const handlePriceChange = (e) => {
    const value = e.target.value;
    if (value === '' || /^\d+(\.\d{0,2})?$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        default_price: value
      }));
    }
  };
  
  // إضافة فئة جديدة
  const handleAddCategory = () => {
    const newCategory = prompt('أدخل اسم الفئة الجديدة:');
    if (newCategory && newCategory.trim() && !categories.includes(newCategory.trim())) {
      setCategories([...categories, newCategory.trim()]);
      setFormData(prev => ({
        ...prev,
        category: newCategory.trim()
      }));
    }
  };
  
  // التحقق من صحة البيانات
  const validateForm = () => {
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم المنتج');
      return false;
    }
    
    return true;
  };
  
  // حفظ البيانات
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setSubmitting(true);
      setError(null);
      
      // تحويل السعر إلى رقم
      const productData = {
        ...formData,
        default_price: formData.default_price ? parseFloat(formData.default_price) : null
      };
      
      let result;
      
      if (isEditMode) {
        // تعديل المنتج
        result = await window.api.products.update(id, productData);
        if (result.success) {
          alert('تم تعديل المنتج بنجاح');
          navigate(`/products/${id}`);
        } else {
          throw new Error('فشل في تعديل المنتج');
        }
      } else {
        // إضافة منتج جديد
        result = await window.api.products.create(productData);
        if (result.success) {
          alert('تم إضافة المنتج بنجاح');
          navigate(`/products/${result.id}`);
        } else {
          throw new Error('فشل في إضافة المنتج');
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ بيانات المنتج:', error);
      setError('حدث خطأ أثناء حفظ بيانات المنتج. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }
  
  return (
    <div className="product-form-page">
      <div className="page-header">
        <h2>{isEditMode ? 'تعديل بيانات المنتج' : 'إضافة منتج جديد'}</h2>
        <div className="page-actions">
          <Link to={isEditMode ? `/products/${id}` : '/products'} className="btn btn-secondary">
            <i className="fas fa-times"></i> إلغاء
          </Link>
        </div>
      </div>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">اسم المنتج <span className="text-danger">*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">الفئة</label>
                  <div className="input-group">
                    <select
                      className="form-control"
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                    >
                      <option value="">اختر الفئة</option>
                      {categories.map((category, index) => (
                        <option key={index} value={category}>{category}</option>
                      ))}
                    </select>
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={handleAddCategory}
                    >
                      <i className="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">السعر الافتراضي (ر.س)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="default_price"
                    value={formData.default_price}
                    onChange={handlePriceChange}
                    placeholder="0.00"
                  />
                </div>
              </div>
            </div>
            
            <div className="form-group mb-3">
              <label className="form-label">الوصف</label>
              <textarea
                className="form-control"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
              ></textarea>
            </div>
            
            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span className="ms-1">جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    <span className="ms-1">{isEditMode ? 'حفظ التعديلات' : 'إضافة المنتج'}</span>
                  </>
                )}
              </button>
              
              <Link to={isEditMode ? `/products/${id}` : '/products'} className="btn btn-secondary">
                <i className="fas fa-times"></i> إلغاء
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = ProductForm;
