const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
// const sqlite3 = require('better-sqlite3'); // تم تعطيل قاعدة البيانات مؤقتاً
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');
const ExcelJS = require('exceljs');

// استيراد electron-store بطريقة CommonJS
const Store = require('electron-store');

// تكوين مخزن الإعدادات
const defaultSettings = {
  defaultWorkerFee: 0,
  defaultFactoryFee: 0,
  defaultDesignerFee: 0,
  defaultOwnerMargin: 0,
  showLowStockNotifications: true,
  showDueInvoicesNotifications: true
};

const store = new Store({
  name: 'h-group-settings',
  defaults: defaultSettings
});

// متغير لتخزين النافذة الرئيسية
let mainWindow;

// إنشاء قاعدة البيانات أو الاتصال بها إذا كانت موجودة
let db;

function createDatabase() {
  try {
    console.log('بدء إنشاء قاعدة البيانات...');
    console.log('مسار قاعدة البيانات:', path.join(app.getPath('userData'), 'hgroup.db'));

    // التحقق من وجود مجلد البيانات
    const userDataPath = app.getPath('userData');
    if (!fs.existsSync(userDataPath)) {
      console.log('إنشاء مجلد البيانات:', userDataPath);
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    // محاولة إنشاء قاعدة البيانات (معطلة مؤقتاً)
    try {
      // db = new sqlite3(path.join(userDataPath, 'hgroup.db'), { verbose: console.log });
      console.log('تم تجاهل قاعدة البيانات مؤقتاً - سيتم استخدام بيانات وهمية');

      // إنشاء كائن وهمي لقاعدة البيانات
      db = {
        prepare: () => ({
          get: () => ({ count: 0 }),
          all: () => [],
          run: () => ({ lastInsertRowid: 1 })
        }),
        exec: () => {},
        close: () => {}
      };

    } catch (dbError) {
      console.error('خطأ في فتح قاعدة البيانات:', dbError);
      dialog.showErrorBox('خطأ في قاعدة البيانات', `فشل فتح قاعدة البيانات: ${dbError.message}`);
      return false;
    }

    // إنشاء جداول قاعدة البيانات إذا لم تكن موجودة

    // جدول العملاء
    db.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        address TEXT,
        email TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المنتجات
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        default_price REAL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المواد الخام
    db.exec(`
      CREATE TABLE IF NOT EXISTS materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        unit TEXT,
        cost_per_unit REAL DEFAULT 0,
        min_quantity REAL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المخزون
    db.exec(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        material_id INTEGER,
        quantity REAL DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (material_id) REFERENCES materials (id)
      )
    `);

    // جدول الطلبات
    db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE,
        customer_id INTEGER,
        product_id INTEGER,
        order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivery_date TIMESTAMP,
        specifications TEXT,
        status TEXT DEFAULT 'قيد التنفيذ',
        worker_fee REAL DEFAULT 0,
        factory_fee REAL DEFAULT 0,
        designer_fee REAL DEFAULT 0,
        owner_margin REAL DEFAULT 0,
        materials_cost REAL DEFAULT 0,
        final_price REAL DEFAULT 0,
        notes TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    `);

    // جدول المواد الخام المستخدمة في الطلبات
    db.exec(`
      CREATE TABLE IF NOT EXISTS order_materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER,
        material_id INTEGER,
        quantity REAL,
        cost_per_unit REAL,
        total_cost REAL,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (material_id) REFERENCES materials (id)
      )
    `);

    // جدول العاملين
    db.exec(`
      CREATE TABLE IF NOT EXISTS workers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        role TEXT,
        phone TEXT,
        address TEXT,
        fee_per_order REAL DEFAULT 0,
        monthly_salary REAL DEFAULT 0,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول تعيين العمال للطلبات
    db.exec(`
      CREATE TABLE IF NOT EXISTS order_workers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER,
        worker_id INTEGER,
        assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        fee REAL DEFAULT 0,
        status TEXT DEFAULT 'قيد التنفيذ',
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (worker_id) REFERENCES workers (id)
      )
    `);

    // جدول المصنع والتكاليف التشغيلية
    db.exec(`
      CREATE TABLE IF NOT EXISTS factory_expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        expense_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        category TEXT,
        description TEXT,
        amount REAL,
        recurring BOOLEAN DEFAULT 0,
        period TEXT
      )
    `);

    // جدول الفواتير
    db.exec(`
      CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE,
        order_id INTEGER,
        issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        due_date TIMESTAMP,
        total_amount REAL,
        paid_amount REAL DEFAULT 0,
        status TEXT DEFAULT 'غير مدفوعة',
        payment_type TEXT DEFAULT 'كامل', /* كامل، أقساط */
        installments_count INTEGER DEFAULT 1,
        notes TEXT,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `);

    // جدول المدفوعات
    db.exec(`
      CREATE TABLE IF NOT EXISTS payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        amount REAL,
        payment_method TEXT,
        notes TEXT,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id)
      )
    `);

    // جدول الأقساط
    db.exec(`
      CREATE TABLE IF NOT EXISTS installments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER,
        installment_number INTEGER,
        amount REAL,
        due_date TIMESTAMP,
        payment_date TIMESTAMP,
        status TEXT DEFAULT 'غير مدفوع', /* غير مدفوع، مدفوع، متأخر */
        payment_id INTEGER,
        notes TEXT,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id),
        FOREIGN KEY (payment_id) REFERENCES payments (id)
      )
    `);

    // جدول العمليات المالية
    db.exec(`
      CREATE TABLE IF NOT EXISTS financial_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        type TEXT,
        category TEXT,
        amount REAL,
        description TEXT,
        related_order_id INTEGER,
        related_worker_id INTEGER,
        related_invoice_id INTEGER,
        related_expense_id INTEGER,
        FOREIGN KEY (related_order_id) REFERENCES orders (id),
        FOREIGN KEY (related_worker_id) REFERENCES workers (id),
        FOREIGN KEY (related_invoice_id) REFERENCES invoices (id),
        FOREIGN KEY (related_expense_id) REFERENCES factory_expenses (id)
      )
    `);

    // جدول المستخدمين
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT,
        role TEXT DEFAULT 'user',
        permissions TEXT,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول سجل العمليات
    db.exec(`
      CREATE TABLE IF NOT EXISTS activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action_type TEXT,
        action_details TEXT,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // جدول الإشعارات
    db.exec(`
      CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        link TEXT,
        read BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
    if (userCount.count === 0) {
      db.prepare('INSERT INTO users (username, password, full_name, role, permissions) VALUES (?, ?, ?, ?, ?)')
        .run('admin', 'admin123', 'مدير النظام', 'admin', 'all');
    }

    console.log('تم إنشاء قاعدة البيانات بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في إنشاء قاعدة البيانات:', error);
    return false;
  }
}

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    icon: path.join(__dirname, 'assets/icons/icon.png')
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // فتح أدوات المطور في بيئة التطوير
  if (process.env.NODE_ENV === 'development' || process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // التعامل مع إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // إضافة معالج للأخطاء
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('فشل تحميل الصفحة:', errorCode, errorDescription);

    // إعادة تحميل الصفحة في حالة فشل التحميل
    if (mainWindow) {
      mainWindow.loadFile('index.html');
    }
  });
}

// عند جاهزية التطبيق
app.whenReady().then(() => {
  try {
    console.log('بدء تشغيل التطبيق...');

    // إنشاء النافذة الرئيسية أولاً
    createWindow();

    // إنشاء قاعدة البيانات
    const dbCreated = createDatabase();

    if (dbCreated) {
      console.log('تم إنشاء قاعدة البيانات بنجاح');

      // إنشاء مستخدم افتراضي إذا لم يكن موجوداً
      try {
        const userExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE username = ?').get('admin');

        if (!userExists || userExists.count === 0) {
          console.log('إنشاء مستخدم افتراضي...');

          // إنشاء مستخدم افتراضي
          db.prepare(`
            INSERT INTO users (username, password, full_name, role, permissions)
            VALUES (?, ?, ?, ?, ?)
          `).run('admin', 'admin123', 'مدير النظام', 'admin', 'all');

          console.log('تم إنشاء مستخدم افتراضي بنجاح');
        }

        // تسجيل دخول المستخدم الافتراضي تلقائياً
        const user = db.prepare('SELECT * FROM users WHERE username = ?').get('admin');
        if (user) {
          currentUser = {
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            role: user.role,
            permissions: user.permissions
          };
          console.log('تم تسجيل دخول المستخدم الافتراضي تلقائياً');
        }
      } catch (userError) {
        console.error('خطأ في إنشاء المستخدم الافتراضي:', userError);
      }
    } else {
      console.error('فشل إنشاء قاعدة البيانات');
      dialog.showErrorBox('خطأ في قاعدة البيانات', 'فشل إنشاء قاعدة البيانات. قد تواجه مشاكل في استخدام التطبيق.');
    }
  } catch (error) {
    console.error('خطأ أثناء بدء التطبيق:', error);
    dialog.showErrorBox('خطأ في بدء التطبيق', `حدث خطأ أثناء بدء التطبيق: ${error.message}`);
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// إغلاق التطبيق عند إغلاق جميع النوافذ (ماعدا في macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// إغلاق قاعدة البيانات عند إغلاق التطبيق
app.on('will-quit', () => {
  if (db) {
    db.close();
  }
});

// استقبال طلبات IPC من الواجهة
// معالجات الأحداث للعملاء
ipcMain.handle('customers:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM customers ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على العملاء:', error);
    throw error;
  }
});

ipcMain.handle('customers:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM customers WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على العميل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customers:create', async (event, customer) => {
  try {
    const result = db.prepare(
      'INSERT INTO customers (name, phone, address, email, notes) VALUES (?, ?, ?, ?, ?)'
    ).run(customer.name, customer.phone, customer.address, customer.email, customer.notes);

    return { id: result.lastInsertRowid, ...customer };
  } catch (error) {
    console.error('خطأ في إنشاء عميل جديد:', error);
    throw error;
  }
});

ipcMain.handle('customers:update', async (event, id, customer) => {
  try {
    db.prepare(
      'UPDATE customers SET name = ?, phone = ?, address = ?, email = ?, notes = ? WHERE id = ?'
    ).run(customer.name, customer.phone, customer.address, customer.email, customer.notes, id);

    return { id, ...customer };
  } catch (error) {
    console.error(`خطأ في تحديث العميل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customers:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM customers WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف العميل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('customers:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM customers WHERE name LIKE ? OR phone LIKE ? OR address LIKE ? OR email LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن العملاء بالاستعلام "${query}":`, error);
    throw error;
  }
});

// معالجات الأحداث للمنتجات
ipcMain.handle('products:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM products ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على المنتجات:', error);
    throw error;
  }
});

ipcMain.handle('products:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM products WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على المنتج رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('products:create', async (event, product) => {
  try {
    const result = db.prepare(
      'INSERT INTO products (name, description, category, default_price) VALUES (?, ?, ?, ?)'
    ).run(product.name, product.description, product.category, product.default_price);

    return { id: result.lastInsertRowid, ...product };
  } catch (error) {
    console.error('خطأ في إنشاء منتج جديد:', error);
    throw error;
  }
});

ipcMain.handle('products:update', async (event, id, product) => {
  try {
    db.prepare(
      'UPDATE products SET name = ?, description = ?, category = ?, default_price = ? WHERE id = ?'
    ).run(product.name, product.description, product.category, product.default_price, id);

    return { id, ...product };
  } catch (error) {
    console.error(`خطأ في تحديث المنتج رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('products:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM products WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف المنتج رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('products:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM products WHERE name LIKE ? OR description LIKE ? OR category LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن المنتجات بالاستعلام "${query}":`, error);
    throw error;
  }
});

// معالجات الأحداث للمواد الخام
ipcMain.handle('materials:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM materials ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد الخام:', error);
    throw error;
  }
});

ipcMain.handle('materials:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM materials WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على المادة الخام رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('materials:create', async (event, material) => {
  try {
    const result = db.prepare(
      'INSERT INTO materials (name, description, unit, cost_per_unit, min_quantity) VALUES (?, ?, ?, ?, ?)'
    ).run(material.name, material.description, material.unit, material.cost_per_unit, material.min_quantity);

    // إنشاء سجل مخزون أولي للمادة الخام
    db.prepare('INSERT INTO inventory (material_id, quantity) VALUES (?, 0)').run(result.lastInsertRowid);

    return { id: result.lastInsertRowid, ...material };
  } catch (error) {
    console.error('خطأ في إنشاء مادة خام جديدة:', error);
    throw error;
  }
});

ipcMain.handle('materials:update', async (event, id, material) => {
  try {
    db.prepare(
      'UPDATE materials SET name = ?, description = ?, unit = ?, cost_per_unit = ?, min_quantity = ? WHERE id = ?'
    ).run(material.name, material.description, material.unit, material.cost_per_unit, material.min_quantity, id);

    return { id, ...material };
  } catch (error) {
    console.error(`خطأ في تحديث المادة الخام رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('materials:delete', async (event, id) => {
  try {
    // حذف سجل المخزون المرتبط بالمادة الخام
    db.prepare('DELETE FROM inventory WHERE material_id = ?').run(id);

    // حذف المادة الخام
    db.prepare('DELETE FROM materials WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف المادة الخام رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('materials:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM materials WHERE name LIKE ? OR description LIKE ? OR unit LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن المواد الخام بالاستعلام "${query}":`, error);
    throw error;
  }
});

// معالجات الأحداث للمخزون
ipcMain.handle('inventory:getAll', async () => {
  try {
    return db.prepare(`
      SELECT i.*, m.name as material_name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المخزون:', error);
    throw error;
  }
});

ipcMain.handle('inventory:getByMaterialId', async (event, materialId) => {
  try {
    return db.prepare(`
      SELECT i.*, m.name as material_name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      WHERE i.material_id = ?
    `).get(materialId);
  } catch (error) {
    console.error(`خطأ في الحصول على مخزون المادة الخام رقم ${materialId}:`, error);
    throw error;
  }
});

ipcMain.handle('inventory:update', async (event, materialId, quantity) => {
  try {
    db.prepare(
      'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
    ).run(quantity, materialId);

    return { success: true, materialId, quantity };
  } catch (error) {
    console.error(`خطأ في تحديث مخزون المادة الخام رقم ${materialId}:`, error);
    throw error;
  }
});

ipcMain.handle('inventory:getLowStock', async () => {
  try {
    return db.prepare(`
      SELECT i.*, m.name as material_name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      WHERE i.quantity <= m.min_quantity
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد منخفضة المخزون:', error);
    throw error;
  }
});

ipcMain.handle('inventory:getLowInventoryItems', async () => {
  try {
    return db.prepare(`
      SELECT i.*, m.name, m.unit, m.min_quantity
      FROM inventory i
      JOIN materials m ON i.material_id = m.id
      WHERE i.quantity <= m.min_quantity
      ORDER BY m.name
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على المواد ذات المخزون المنخفض:', error);
    throw error;
  }
});

// معالجات الأحداث للطلبات
ipcMain.handle('orders:getAll', async () => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      ORDER BY o.order_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الطلبات:', error);
    throw error;
  }
});

ipcMain.handle('orders:getById', async (event, id) => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE o.id = ?
    `).get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:create', async (event, order) => {
  try {
    // إنشاء رقم طلب فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    // الحصول على آخر رقم تسلسلي للطلبات في الشهر الحالي
    const lastOrder = db.prepare(`
      SELECT order_number FROM orders
      WHERE order_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`ORD-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.order_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const orderNumber = `ORD-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الطلب الجديد
    const result = db.prepare(`
      INSERT INTO orders (
        order_number, customer_id, product_id, order_date, delivery_date,
        specifications, status, worker_fee, factory_fee, designer_fee,
        owner_margin, materials_cost, final_price, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      orderNumber,
      order.customer_id,
      order.product_id,
      order.order_date || new Date().toISOString(),
      order.delivery_date,
      order.specifications,
      order.status || 'قيد التنفيذ',
      order.worker_fee || 0,
      order.factory_fee || 0,
      order.designer_fee || 0,
      order.owner_margin || 0,
      order.materials_cost || 0,
      order.final_price || 0,
      order.notes
    );

    return {
      id: result.lastInsertRowid,
      order_number: orderNumber,
      ...order
    };
  } catch (error) {
    console.error('خطأ في إنشاء طلب جديد:', error);
    throw error;
  }
});

ipcMain.handle('orders:update', async (event, id, order) => {
  try {
    db.prepare(`
      UPDATE orders SET
        customer_id = ?, product_id = ?, delivery_date = ?,
        specifications = ?, status = ?, worker_fee = ?,
        factory_fee = ?, designer_fee = ?, owner_margin = ?,
        materials_cost = ?, final_price = ?, notes = ?
      WHERE id = ?
    `).run(
      order.customer_id,
      order.product_id,
      order.delivery_date,
      order.specifications,
      order.status,
      order.worker_fee,
      order.factory_fee,
      order.designer_fee,
      order.owner_margin,
      order.materials_cost,
      order.final_price,
      order.notes,
      id
    );

    return { id, ...order };
  } catch (error) {
    console.error(`خطأ في تحديث الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:delete', async (event, id) => {
  try {
    // حذف المواد المستخدمة في الطلب
    db.prepare('DELETE FROM order_materials WHERE order_id = ?').run(id);

    // حذف العمال المعينين للطلب
    db.prepare('DELETE FROM order_workers WHERE order_id = ?').run(id);

    // حذف الطلب
    db.prepare('DELETE FROM orders WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:search', async (event, query) => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE o.order_number LIKE ?
        OR c.name LIKE ?
        OR p.name LIKE ?
        OR o.specifications LIKE ?
      ORDER BY o.order_date DESC
    `).all(`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن الطلبات بالاستعلام "${query}":`, error);
    throw error;
  }
});

ipcMain.handle('orders:getByStatus', async (event, status) => {
  try {
    return db.prepare(`
      SELECT o.*, c.name as customer_name, p.name as product_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE o.status = ?
      ORDER BY o.order_date DESC
    `).all(status);
  } catch (error) {
    console.error(`خطأ في الحصول على الطلبات بحالة "${status}":`, error);
    throw error;
  }
});

ipcMain.handle('orders:updateStatus', async (event, id, status) => {
  try {
    db.prepare('UPDATE orders SET status = ? WHERE id = ?').run(status, id);
    return { success: true, id, status };
  } catch (error) {
    console.error(`خطأ في تحديث حالة الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orders:getLateOrders', async () => {
  try {
    const today = new Date().toISOString().split('T')[0];

    return db.prepare(`
      SELECT o.*, c.name as customer_name, c.phone as customer_phone
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      WHERE o.status = 'قيد التنفيذ'
        AND o.delivery_date < ?
        AND o.delivery_date IS NOT NULL
      ORDER BY o.delivery_date
    `).all(today + 'T00:00:00.000Z');
  } catch (error) {
    console.error('خطأ في الحصول على الطلبات المتأخرة:', error);
    throw error;
  }
});

ipcMain.handle('orders:calculatePrice', async (event, orderData) => {
  try {
    // حساب تكلفة المواد
    let materialsCost = 0;
    if (orderData.materials && orderData.materials.length > 0) {
      for (const material of orderData.materials) {
        materialsCost += material.quantity * material.cost_per_unit;
      }
    }

    // حساب السعر النهائي
    const workerFee = orderData.worker_fee || 0;
    const factoryFee = orderData.factory_fee || 0;
    const designerFee = orderData.designer_fee || 0;
    const ownerMargin = orderData.owner_margin || 0;

    const totalCost = materialsCost + workerFee + factoryFee + designerFee;
    const finalPrice = totalCost + ownerMargin;

    return {
      materials_cost: materialsCost,
      total_cost: totalCost,
      final_price: finalPrice
    };
  } catch (error) {
    console.error('خطأ في حساب سعر الطلب:', error);
    throw error;
  }
});

// التنبؤ بتكلفة الطلب قبل إنشائه
ipcMain.handle('orders:predictCost', async (event, productId, specifications) => {
  try {
    // الحصول على معلومات المنتج
    let product = null;
    if (productId) {
      product = db.prepare('SELECT * FROM products WHERE id = ?').get(productId);
    }

    // البحث عن طلبات مشابهة
    let similarOrders = [];

    if (product) {
      // البحث عن طلبات لنفس المنتج
      similarOrders = db.prepare(`
        SELECT o.*, COUNT(om.id) as materials_count, SUM(om.total_cost) as materials_cost
        FROM orders o
        LEFT JOIN order_materials om ON o.id = om.order_id
        WHERE o.product_id = ?
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT 5
      `).all(productId);
    } else if (specifications) {
      // البحث عن طلبات بمواصفات مشابهة
      similarOrders = db.prepare(`
        SELECT o.*, COUNT(om.id) as materials_count, SUM(om.total_cost) as materials_cost
        FROM orders o
        LEFT JOIN order_materials om ON o.id = om.order_id
        WHERE o.specifications LIKE ?
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT 5
      `).all(`%${specifications}%`);
    }

    // إذا لم يتم العثور على طلبات مشابهة، استخدم متوسط جميع الطلبات
    if (similarOrders.length === 0) {
      similarOrders = db.prepare(`
        SELECT o.*, COUNT(om.id) as materials_count, SUM(om.total_cost) as materials_cost
        FROM orders o
        LEFT JOIN order_materials om ON o.id = om.order_id
        GROUP BY o.id
        ORDER BY o.order_date DESC
        LIMIT 10
      `).all();
    }

    // حساب متوسط التكاليف من الطلبات المشابهة
    let avgMaterialsCost = 0;
    let avgWorkerFee = 0;
    let avgFactoryFee = 0;
    let avgDesignerFee = 0;
    let avgOwnerMargin = 0;
    let avgTotalCost = 0;
    let avgFinalPrice = 0;

    if (similarOrders.length > 0) {
      // حساب المتوسطات
      for (const order of similarOrders) {
        avgMaterialsCost += order.materials_cost || 0;
        avgWorkerFee += order.worker_fee || 0;
        avgFactoryFee += order.factory_fee || 0;
        avgDesignerFee += order.designer_fee || 0;
        avgOwnerMargin += order.owner_margin || 0;
        avgTotalCost += (order.materials_cost || 0) + (order.worker_fee || 0) + (order.factory_fee || 0) + (order.designer_fee || 0);
        avgFinalPrice += order.final_price || 0;
      }

      avgMaterialsCost /= similarOrders.length;
      avgWorkerFee /= similarOrders.length;
      avgFactoryFee /= similarOrders.length;
      avgDesignerFee /= similarOrders.length;
      avgOwnerMargin /= similarOrders.length;
      avgTotalCost /= similarOrders.length;
      avgFinalPrice /= similarOrders.length;
    }

    // الحصول على الإعدادات الافتراضية
    const defaultWorkerFee = store.get('defaultWorkerFee') || 0;
    const defaultFactoryFee = store.get('defaultFactoryFee') || 0;
    const defaultDesignerFee = store.get('defaultDesignerFee') || 0;
    const defaultOwnerMargin = store.get('defaultOwnerMargin') || 0;

    // استخدام القيم الافتراضية إذا لم يتم العثور على طلبات مشابهة
    if (similarOrders.length === 0) {
      avgWorkerFee = defaultWorkerFee;
      avgFactoryFee = defaultFactoryFee;
      avgDesignerFee = defaultDesignerFee;
      avgOwnerMargin = defaultOwnerMargin;
    }

    // الحصول على المواد المستخدمة في الطلبات المشابهة
    let suggestedMaterials = [];

    if (similarOrders.length > 0) {
      // الحصول على المواد الأكثر استخداماً في الطلبات المشابهة
      suggestedMaterials = db.prepare(`
        SELECT m.id, m.name, m.unit, m.cost_per_unit,
               AVG(om.quantity) as avg_quantity,
               COUNT(om.id) as usage_count
        FROM order_materials om
        JOIN materials m ON om.material_id = m.id
        WHERE om.order_id IN (${similarOrders.map(o => o.id).join(',')})
        GROUP BY m.id
        ORDER BY usage_count DESC, avg_quantity DESC
      `).all();
    }

    return {
      predicted_costs: {
        materials_cost: avgMaterialsCost,
        worker_fee: avgWorkerFee || defaultWorkerFee,
        factory_fee: avgFactoryFee || defaultFactoryFee,
        designer_fee: avgDesignerFee || defaultDesignerFee,
        owner_margin: avgOwnerMargin || defaultOwnerMargin,
        total_cost: avgTotalCost,
        final_price: avgFinalPrice
      },
      suggested_materials: suggestedMaterials,
      similar_orders_count: similarOrders.length,
      product: product
    };
  } catch (error) {
    console.error('خطأ في التنبؤ بتكلفة الطلب:', error);
    throw error;
  }
});

// معالجات الأحداث للمواد المستخدمة في الطلبات
ipcMain.handle('orderMaterials:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT om.*, m.name as material_name, m.unit
      FROM order_materials om
      JOIN materials m ON om.material_id = m.id
      WHERE om.order_id = ?
      ORDER BY m.name
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على مواد الطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('orderMaterials:add', async (event, orderMaterial) => {
  try {
    // حساب التكلفة الإجمالية
    const totalCost = orderMaterial.quantity * orderMaterial.cost_per_unit;

    const result = db.prepare(`
      INSERT INTO order_materials (order_id, material_id, quantity, cost_per_unit, total_cost)
      VALUES (?, ?, ?, ?, ?)
    `).run(
      orderMaterial.order_id,
      orderMaterial.material_id,
      orderMaterial.quantity,
      orderMaterial.cost_per_unit,
      totalCost
    );

    // تحديث مخزون المادة الخام
    const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(orderMaterial.material_id);
    if (inventory) {
      const newQuantity = Math.max(0, inventory.quantity - orderMaterial.quantity);
      db.prepare(
        'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
      ).run(newQuantity, orderMaterial.material_id);
    }

    // تحديث تكلفة المواد في الطلب
    const orderMaterials = db.prepare('SELECT SUM(total_cost) as total FROM order_materials WHERE order_id = ?').get(orderMaterial.order_id);
    if (orderMaterials && orderMaterials.total) {
      db.prepare('UPDATE orders SET materials_cost = ? WHERE id = ?').run(orderMaterials.total, orderMaterial.order_id);
    }

    return {
      id: result.lastInsertRowid,
      ...orderMaterial,
      total_cost: totalCost
    };
  } catch (error) {
    console.error('خطأ في إضافة مادة للطلب:', error);
    throw error;
  }
});

ipcMain.handle('orderMaterials:update', async (event, id, orderMaterial) => {
  try {
    // الحصول على الكمية السابقة
    const oldOrderMaterial = db.prepare('SELECT * FROM order_materials WHERE id = ?').get(id);

    // حساب التكلفة الإجمالية الجديدة
    const totalCost = orderMaterial.quantity * orderMaterial.cost_per_unit;

    db.prepare(`
      UPDATE order_materials
      SET material_id = ?, quantity = ?, cost_per_unit = ?, total_cost = ?
      WHERE id = ?
    `).run(
      orderMaterial.material_id,
      orderMaterial.quantity,
      orderMaterial.cost_per_unit,
      totalCost,
      id
    );

    // تحديث مخزون المادة الخام
    if (oldOrderMaterial) {
      const quantityDiff = orderMaterial.quantity - oldOrderMaterial.quantity;

      if (quantityDiff !== 0) {
        const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(orderMaterial.material_id);
        if (inventory) {
          const newQuantity = Math.max(0, inventory.quantity - quantityDiff);
          db.prepare(
            'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
          ).run(newQuantity, orderMaterial.material_id);
        }
      }
    }

    // تحديث تكلفة المواد في الطلب
    const orderMaterials = db.prepare('SELECT SUM(total_cost) as total FROM order_materials WHERE order_id = ?').get(orderMaterial.order_id);
    if (orderMaterials && orderMaterials.total) {
      db.prepare('UPDATE orders SET materials_cost = ? WHERE id = ?').run(orderMaterials.total, orderMaterial.order_id);
    }

    return {
      id,
      ...orderMaterial,
      total_cost: totalCost
    };
  } catch (error) {
    console.error(`خطأ في تحديث مادة الطلب رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orderMaterials:delete', async (event, id) => {
  try {
    // الحصول على معلومات المادة قبل الحذف
    const orderMaterial = db.prepare('SELECT * FROM order_materials WHERE id = ?').get(id);

    if (orderMaterial) {
      // حذف المادة من الطلب
      db.prepare('DELETE FROM order_materials WHERE id = ?').run(id);

      // إعادة الكمية إلى المخزون
      const inventory = db.prepare('SELECT quantity FROM inventory WHERE material_id = ?').get(orderMaterial.material_id);
      if (inventory) {
        const newQuantity = inventory.quantity + orderMaterial.quantity;
        db.prepare(
          'UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE material_id = ?'
        ).run(newQuantity, orderMaterial.material_id);
      }

      // تحديث تكلفة المواد في الطلب
      const orderMaterials = db.prepare('SELECT SUM(total_cost) as total FROM order_materials WHERE order_id = ?').get(orderMaterial.order_id);
      const totalCost = orderMaterials && orderMaterials.total ? orderMaterials.total : 0;
      db.prepare('UPDATE orders SET materials_cost = ? WHERE id = ?').run(totalCost, orderMaterial.order_id);
    }

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف مادة الطلب رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للعمال
ipcMain.handle('workers:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM workers ORDER BY name').all();
  } catch (error) {
    console.error('خطأ في الحصول على العمال:', error);
    throw error;
  }
});

ipcMain.handle('workers:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM workers WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:create', async (event, worker) => {
  try {
    const result = db.prepare(
      'INSERT INTO workers (name, role, phone, address, fee_per_order, monthly_salary, notes) VALUES (?, ?, ?, ?, ?, ?, ?)'
    ).run(
      worker.name,
      worker.role,
      worker.phone,
      worker.address,
      worker.fee_per_order || 0,
      worker.monthly_salary || 0,
      worker.notes
    );

    return { id: result.lastInsertRowid, ...worker };
  } catch (error) {
    console.error('خطأ في إنشاء عامل جديد:', error);
    throw error;
  }
});

ipcMain.handle('workers:update', async (event, id, worker) => {
  try {
    db.prepare(
      'UPDATE workers SET name = ?, role = ?, phone = ?, address = ?, fee_per_order = ?, monthly_salary = ?, notes = ? WHERE id = ?'
    ).run(
      worker.name,
      worker.role,
      worker.phone,
      worker.address,
      worker.fee_per_order || 0,
      worker.monthly_salary || 0,
      worker.notes,
      id
    );

    return { id, ...worker };
  } catch (error) {
    console.error(`خطأ في تحديث العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:delete', async (event, id) => {
  try {
    // التحقق من عدم وجود طلبات مرتبطة بالعامل
    const orderWorkers = db.prepare('SELECT COUNT(*) as count FROM order_workers WHERE worker_id = ?').get(id);
    if (orderWorkers && orderWorkers.count > 0) {
      throw new Error('لا يمكن حذف العامل لأنه مرتبط بطلبات');
    }

    db.prepare('DELETE FROM workers WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('workers:search', async (event, query) => {
  try {
    return db.prepare(
      'SELECT * FROM workers WHERE name LIKE ? OR role LIKE ? OR phone LIKE ? OR address LIKE ? ORDER BY name'
    ).all(`%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`);
  } catch (error) {
    console.error(`خطأ في البحث عن العمال بالاستعلام "${query}":`, error);
    throw error;
  }
});

ipcMain.handle('workers:getByRole', async (event, role) => {
  try {
    return db.prepare('SELECT * FROM workers WHERE role = ? ORDER BY name').all(role);
  } catch (error) {
    console.error(`خطأ في الحصول على العمال بدور "${role}":`, error);
    throw error;
  }
});

// معالجات الأحداث لتعيين العمال للطلبات
ipcMain.handle('orderWorkers:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT ow.*, w.name as worker_name, w.role as worker_role
      FROM order_workers ow
      JOIN workers w ON ow.worker_id = w.id
      WHERE ow.order_id = ?
      ORDER BY ow.assigned_date
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على العمال المعينين للطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:getByWorkerId', async (event, workerId) => {
  try {
    return db.prepare(`
      SELECT ow.*, o.order_number, o.specifications, o.status as order_status
      FROM order_workers ow
      JOIN orders o ON ow.order_id = o.id
      WHERE ow.worker_id = ?
      ORDER BY ow.assigned_date DESC
    `).all(workerId);
  } catch (error) {
    console.error(`خطأ في الحصول على الطلبات المعينة للعامل رقم ${workerId}:`, error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:assign', async (event, orderWorker) => {
  try {
    const result = db.prepare(
      'INSERT INTO order_workers (order_id, worker_id, assigned_date, fee, status) VALUES (?, ?, ?, ?, ?)'
    ).run(
      orderWorker.order_id,
      orderWorker.worker_id,
      orderWorker.assigned_date || new Date().toISOString(),
      orderWorker.fee || 0,
      orderWorker.status || 'قيد التنفيذ'
    );

    // تحديث أجرة العامل في الطلب إذا كان الأول
    const worker = db.prepare('SELECT fee_per_order FROM workers WHERE id = ?').get(orderWorker.worker_id);
    if (worker) {
      const orderWorkersCount = db.prepare('SELECT COUNT(*) as count FROM order_workers WHERE order_id = ?').get(orderWorker.order_id);
      if (orderWorkersCount && orderWorkersCount.count === 1) {
        db.prepare('UPDATE orders SET worker_fee = ? WHERE id = ?').run(worker.fee_per_order, orderWorker.order_id);
      }
    }

    return { id: result.lastInsertRowid, ...orderWorker };
  } catch (error) {
    console.error('خطأ في تعيين عامل للطلب:', error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:update', async (event, id, orderWorker) => {
  try {
    db.prepare(
      'UPDATE order_workers SET worker_id = ?, assigned_date = ?, fee = ?, status = ? WHERE id = ?'
    ).run(
      orderWorker.worker_id,
      orderWorker.assigned_date,
      orderWorker.fee,
      orderWorker.status,
      id
    );

    return { id, ...orderWorker };
  } catch (error) {
    console.error(`خطأ في تحديث تعيين العامل رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('orderWorkers:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM order_workers WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف تعيين العامل رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للمصنع والتكاليف التشغيلية
ipcMain.handle('factoryExpenses:getAll', async () => {
  try {
    return db.prepare('SELECT * FROM factory_expenses ORDER BY expense_date DESC').all();
  } catch (error) {
    console.error('خطأ في الحصول على مصاريف المصنع:', error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM factory_expenses WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على مصروف المصنع رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:create', async (event, expense) => {
  try {
    const result = db.prepare(
      'INSERT INTO factory_expenses (expense_date, category, description, amount, recurring, period) VALUES (?, ?, ?, ?, ?, ?)'
    ).run(
      expense.expense_date || new Date().toISOString(),
      expense.category,
      expense.description,
      expense.amount,
      expense.recurring ? 1 : 0,
      expense.period
    );

    // إنشاء عملية مالية للمصروف
    db.prepare(`
      INSERT INTO financial_transactions (
        transaction_date, type, category, amount, description, related_expense_id
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      expense.expense_date || new Date().toISOString(),
      'expense',
      expense.category,
      -Math.abs(expense.amount),
      expense.description,
      result.lastInsertRowid
    );

    return { id: result.lastInsertRowid, ...expense };
  } catch (error) {
    console.error('خطأ في إنشاء مصروف مصنع جديد:', error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:update', async (event, id, expense) => {
  try {
    db.prepare(
      'UPDATE factory_expenses SET expense_date = ?, category = ?, description = ?, amount = ?, recurring = ?, period = ? WHERE id = ?'
    ).run(
      expense.expense_date,
      expense.category,
      expense.description,
      expense.amount,
      expense.recurring ? 1 : 0,
      expense.period,
      id
    );

    // تحديث العملية المالية المرتبطة
    db.prepare(`
      UPDATE financial_transactions
      SET transaction_date = ?, category = ?, amount = ?, description = ?
      WHERE related_expense_id = ?
    `).run(
      expense.expense_date,
      expense.category,
      -Math.abs(expense.amount),
      expense.description,
      id
    );

    return { id, ...expense };
  } catch (error) {
    console.error(`خطأ في تحديث مصروف المصنع رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:delete', async (event, id) => {
  try {
    // حذف العملية المالية المرتبطة
    db.prepare('DELETE FROM financial_transactions WHERE related_expense_id = ?').run(id);

    // حذف المصروف
    db.prepare('DELETE FROM factory_expenses WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف مصروف المصنع رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:getByCategory', async (event, category) => {
  try {
    return db.prepare('SELECT * FROM factory_expenses WHERE category = ? ORDER BY expense_date DESC').all(category);
  } catch (error) {
    console.error(`خطأ في الحصول على مصاريف المصنع بفئة "${category}":`, error);
    throw error;
  }
});

ipcMain.handle('factoryExpenses:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(
      'SELECT * FROM factory_expenses WHERE expense_date BETWEEN ? AND ? ORDER BY expense_date DESC'
    ).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على مصاريف المصنع بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// معالجات الأحداث للفواتير
ipcMain.handle('invoices:getAll', async () => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      ORDER BY i.issue_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الفواتير:', error);
    throw error;
  }
});

ipcMain.handle('invoices:getById', async (event, id) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name, o.specifications
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.id = ?
    `).get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على الفاتورة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:getByOrderId', async (event, orderId) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.order_id = ?
      ORDER BY i.issue_date DESC
    `).all(orderId);
  } catch (error) {
    console.error(`خطأ في الحصول على فواتير الطلب رقم ${orderId}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:create', async (event, invoice) => {
  try {
    // إنشاء رقم فاتورة فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    // الحصول على آخر رقم تسلسلي للفواتير في الشهر الحالي
    const lastInvoice = db.prepare(`
      SELECT invoice_number FROM invoices
      WHERE invoice_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`INV-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastInvoice) {
      const lastSequence = parseInt(lastInvoice.invoice_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const invoiceNumber = `INV-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الفاتورة الجديدة
    const result = db.prepare(`
      INSERT INTO invoices (
        invoice_number, order_id, issue_date, due_date,
        total_amount, paid_amount, status, payment_type, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      invoiceNumber,
      invoice.order_id,
      invoice.issue_date || new Date().toISOString(),
      invoice.due_date,
      invoice.total_amount,
      invoice.paid_amount || 0,
      invoice.status || 'غير مدفوعة',
      invoice.payment_type || 'كامل',
      invoice.notes
    );

    // إنشاء عملية مالية للفاتورة إذا كان هناك مبلغ مدفوع
    if (invoice.paid_amount && invoice.paid_amount > 0) {
      db.prepare(`
        INSERT INTO financial_transactions (
          transaction_date, type, category, amount, description, related_invoice_id, related_order_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoice.issue_date || new Date().toISOString(),
        'income',
        'invoice_payment',
        invoice.paid_amount,
        `دفعة من الفاتورة ${invoiceNumber}`,
        result.lastInsertRowid,
        invoice.order_id
      );

      // إنشاء سجل دفع
      db.prepare(`
        INSERT INTO payments (
          invoice_id, payment_date, amount, payment_method, notes
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        result.lastInsertRowid,
        invoice.issue_date || new Date().toISOString(),
        invoice.paid_amount,
        invoice.payment_method || 'نقدي',
        'دفعة أولى'
      );
    }

    return {
      id: result.lastInsertRowid,
      invoice_number: invoiceNumber,
      ...invoice
    };
  } catch (error) {
    console.error('خطأ في إنشاء فاتورة جديدة:', error);
    throw error;
  }
});

// إنشاء فاتورة مع أقساط
ipcMain.handle('invoices:createWithInstallments', async (event, invoice, installmentsData) => {
  try {
    // بدء المعاملة
    db.prepare('BEGIN TRANSACTION').run();

    // إنشاء رقم فاتورة فريد
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');

    // الحصول على آخر رقم تسلسلي للفواتير في الشهر الحالي
    const lastInvoice = db.prepare(`
      SELECT invoice_number FROM invoices
      WHERE invoice_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`INV-${year}${month}%`);

    let sequenceNumber = 1;
    if (lastInvoice) {
      const lastSequence = parseInt(lastInvoice.invoice_number.split('-')[2]);
      sequenceNumber = lastSequence + 1;
    }

    const invoiceNumber = `INV-${year}${month}-${sequenceNumber.toString().padStart(3, '0')}`;

    // إدخال الفاتورة الجديدة
    const result = db.prepare(`
      INSERT INTO invoices (
        invoice_number, order_id, issue_date, due_date,
        total_amount, paid_amount, status, payment_type, installments_count, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      invoiceNumber,
      invoice.order_id,
      invoice.issue_date || new Date().toISOString(),
      invoice.due_date,
      invoice.total_amount,
      invoice.paid_amount || 0,
      invoice.status || 'غير مدفوعة',
      'أقساط',
      installmentsData.length,
      invoice.notes
    );

    const invoiceId = result.lastInsertRowid;

    // إنشاء الأقساط
    for (let i = 0; i < installmentsData.length; i++) {
      const installment = installmentsData[i];

      db.prepare(`
        INSERT INTO installments (
          invoice_id, installment_number, amount, due_date, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        invoiceId,
        i + 1,
        installment.amount,
        installment.due_date,
        'غير مدفوع',
        installment.notes || `القسط رقم ${i + 1}`
      );
    }

    // إنشاء عملية مالية للفاتورة إذا كان هناك مبلغ مدفوع
    if (invoice.paid_amount && invoice.paid_amount > 0) {
      const paymentResult = db.prepare(`
        INSERT INTO payments (
          invoice_id, payment_date, amount, payment_method, notes
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        invoiceId,
        invoice.issue_date || new Date().toISOString(),
        invoice.paid_amount,
        invoice.payment_method || 'نقدي',
        'دفعة أولى'
      );

      db.prepare(`
        INSERT INTO financial_transactions (
          transaction_date, type, category, amount, description, related_invoice_id, related_order_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoice.issue_date || new Date().toISOString(),
        'income',
        'invoice_payment',
        invoice.paid_amount,
        `دفعة أولى للفاتورة ${invoiceNumber}`,
        invoiceId,
        invoice.order_id
      );

      // تحديث القسط الأول إذا كانت الدفعة الأولى تغطيه
      if (installmentsData.length > 0 && invoice.paid_amount >= installmentsData[0].amount) {
        const firstInstallment = db.prepare(`
          SELECT id FROM installments
          WHERE invoice_id = ? AND installment_number = 1
        `).get(invoiceId);

        if (firstInstallment) {
          db.prepare(`
            UPDATE installments
            SET status = ?, payment_date = ?, payment_id = ?
            WHERE id = ?
          `).run(
            'مدفوع',
            invoice.issue_date || new Date().toISOString(),
            paymentResult.lastInsertRowid,
            firstInstallment.id
          );
        }
      }
    }

    // إنهاء المعاملة
    db.prepare('COMMIT').run();

    return {
      id: invoiceId,
      invoice_number: invoiceNumber,
      ...invoice,
      installments: installmentsData
    };
  } catch (error) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    db.prepare('ROLLBACK').run();
    console.error('خطأ في إنشاء فاتورة مع أقساط:', error);
    throw error;
  }
});

ipcMain.handle('invoices:update', async (event, id, invoice) => {
  try {
    // الحصول على الفاتورة الحالية
    const currentInvoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(id);

    db.prepare(`
      UPDATE invoices SET
        order_id = ?, issue_date = ?, due_date = ?,
        total_amount = ?, status = ?, notes = ?
      WHERE id = ?
    `).run(
      invoice.order_id,
      invoice.issue_date,
      invoice.due_date,
      invoice.total_amount,
      invoice.status,
      invoice.notes,
      id
    );

    // تحديث المبلغ المدفوع فقط إذا تغير
    if (currentInvoice && invoice.paid_amount !== currentInvoice.paid_amount) {
      const difference = invoice.paid_amount - currentInvoice.paid_amount;

      if (difference !== 0) {
        // تحديث المبلغ المدفوع في الفاتورة
        db.prepare('UPDATE invoices SET paid_amount = ? WHERE id = ?').run(invoice.paid_amount, id);

        // إنشاء سجل دفع جديد للفرق
        if (difference > 0) {
          db.prepare(`
            INSERT INTO payments (
              invoice_id, payment_date, amount, payment_method, notes
            ) VALUES (?, ?, ?, ?, ?)
          `).run(
            id,
            new Date().toISOString(),
            difference,
            invoice.payment_method || 'نقدي',
            invoice.payment_notes || 'دفعة إضافية'
          );

          // إنشاء عملية مالية للدفعة الجديدة
          db.prepare(`
            INSERT INTO financial_transactions (
              transaction_date, type, category, amount, description, related_invoice_id, related_order_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            new Date().toISOString(),
            'income',
            'invoice_payment',
            difference,
            `دفعة إضافية للفاتورة ${currentInvoice.invoice_number}`,
            id,
            invoice.order_id
          );
        }
      }
    }

    return { id, ...invoice };
  } catch (error) {
    console.error(`خطأ في تحديث الفاتورة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:delete', async (event, id) => {
  try {
    // حذف المدفوعات المرتبطة بالفاتورة
    db.prepare('DELETE FROM payments WHERE invoice_id = ?').run(id);

    // حذف العمليات المالية المرتبطة بالفاتورة
    db.prepare('DELETE FROM financial_transactions WHERE related_invoice_id = ?').run(id);

    // حذف الفاتورة
    db.prepare('DELETE FROM invoices WHERE id = ?').run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الفاتورة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('invoices:getByStatus', async (event, status) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.status = ?
      ORDER BY i.issue_date DESC
    `).all(status);
  } catch (error) {
    console.error(`خطأ في الحصول على الفواتير بحالة "${status}":`, error);
    throw error;
  }
});

ipcMain.handle('invoices:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT i.*, o.order_number, c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.issue_date BETWEEN ? AND ?
      ORDER BY i.issue_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على الفواتير بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// توليد فاتورة PDF
ipcMain.handle('invoices:generatePDF', async (event, id) => {
  try {
    // الحصول على معلومات الفاتورة
    const invoice = db.prepare(`
      SELECT i.*, o.order_number, o.specifications, o.product_id,
             c.name as customer_name, c.phone as customer_phone, c.address as customer_address,
             p.name as product_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      LEFT JOIN products p ON o.product_id = p.id
      WHERE i.id = ?
    `).get(id);

    if (!invoice) {
      throw new Error('الفاتورة غير موجودة');
    }

    // الحصول على المواد المستخدمة في الطلب
    const materials = db.prepare(`
      SELECT om.*, m.name as material_name, m.unit
      FROM order_materials om
      JOIN materials m ON om.material_id = m.id
      WHERE om.order_id = ?
    `).all(invoice.order_id);

    // الحصول على المدفوعات
    const payments = db.prepare(`
      SELECT *
      FROM payments
      WHERE invoice_id = ?
      ORDER BY payment_date
    `).all(id);

    // الحصول على الأقساط إذا كانت الفاتورة بالأقساط
    let installments = [];
    if (invoice.payment_type === 'أقساط') {
      installments = db.prepare(`
        SELECT *
        FROM installments
        WHERE invoice_id = ?
        ORDER BY installment_number
      `).all(id);
    }

    // إنشاء مسار للملف
    const pdfFileName = `فاتورة_${invoice.invoice_number.replace(/[\/\\:*?"<>|]/g, '_')}.pdf`;
    const downloadsPath = app.getPath('downloads');
    const pdfPath = path.join(downloadsPath, pdfFileName);

    // إنشاء ملف PDF
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      bufferPages: true
    });

    // تكوين الملف للدعم العربي
    doc.registerFont('Arabic', path.join(__dirname, 'assets/fonts/NotoSansArabic-Regular.ttf'));
    doc.font('Arabic');

    // إنشاء تدفق الكتابة
    const stream = fs.createWriteStream(pdfPath);
    doc.pipe(stream);

    // إضافة الترويسة
    doc.fontSize(24).text('اتش قروب', { align: 'center' });
    doc.fontSize(16).text('لصناعة الأثاث والديكور', { align: 'center' });
    doc.moveDown();

    // إضافة معلومات الفاتورة
    doc.fontSize(18).text(`فاتورة رقم: ${invoice.invoice_number}`, { align: 'right' });
    doc.fontSize(12).text(`التاريخ: ${new Date(invoice.issue_date).toLocaleDateString('ar-SA')}`, { align: 'right' });
    doc.fontSize(12).text(`تاريخ الاستحقاق: ${invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('ar-SA') : 'غير محدد'}`, { align: 'right' });
    doc.moveDown();

    // إضافة معلومات العميل
    doc.fontSize(14).text('معلومات العميل:', { align: 'right' });
    doc.fontSize(12).text(`الاسم: ${invoice.customer_name}`, { align: 'right' });
    if (invoice.customer_phone) doc.text(`الهاتف: ${invoice.customer_phone}`, { align: 'right' });
    if (invoice.customer_address) doc.text(`العنوان: ${invoice.customer_address}`, { align: 'right' });
    doc.moveDown();

    // إضافة معلومات الطلب
    doc.fontSize(14).text('معلومات الطلب:', { align: 'right' });
    doc.fontSize(12).text(`رقم الطلب: ${invoice.order_number}`, { align: 'right' });
    doc.fontSize(12).text(`المنتج: ${invoice.product_name || 'غير محدد'}`, { align: 'right' });
    if (invoice.specifications) doc.text(`المواصفات: ${invoice.specifications}`, { align: 'right' });
    doc.moveDown();

    // إضافة جدول المواد المستخدمة
    if (materials.length > 0) {
      doc.fontSize(14).text('المواد المستخدمة:', { align: 'right' });

      // إنشاء جدول
      const materialsTableTop = doc.y + 10;
      const materialsTableWidth = doc.page.width - 100;

      // رسم رأس الجدول
      doc.fontSize(10);
      doc.text('المادة', doc.page.width - 100, materialsTableTop, { width: 150, align: 'right' });
      doc.text('الكمية', doc.page.width - 250, materialsTableTop, { width: 50, align: 'center' });
      doc.text('الوحدة', doc.page.width - 300, materialsTableTop, { width: 50, align: 'center' });
      doc.text('السعر', doc.page.width - 350, materialsTableTop, { width: 50, align: 'center' });
      doc.text('الإجمالي', doc.page.width - 400, materialsTableTop, { width: 50, align: 'center' });

      // رسم خط أفقي
      doc.moveTo(doc.page.width - 450, materialsTableTop + 15)
         .lineTo(doc.page.width - 50, materialsTableTop + 15)
         .stroke();

      // إضافة بيانات المواد
      let materialsTableY = materialsTableTop + 20;

      for (const material of materials) {
        doc.text(material.material_name, doc.page.width - 100, materialsTableY, { width: 150, align: 'right' });
        doc.text(material.quantity.toString(), doc.page.width - 250, materialsTableY, { width: 50, align: 'center' });
        doc.text(material.unit, doc.page.width - 300, materialsTableY, { width: 50, align: 'center' });
        doc.text(material.cost_per_unit.toLocaleString(), doc.page.width - 350, materialsTableY, { width: 50, align: 'center' });
        doc.text(material.total_cost.toLocaleString(), doc.page.width - 400, materialsTableY, { width: 50, align: 'center' });

        materialsTableY += 20;

        // التحقق من الحاجة لصفحة جديدة
        if (materialsTableY > doc.page.height - 100) {
          doc.addPage();
          materialsTableY = 50;
        }
      }

      // رسم خط أفقي
      doc.moveTo(doc.page.width - 450, materialsTableY)
         .lineTo(doc.page.width - 50, materialsTableY)
         .stroke();

      doc.y = materialsTableY + 10;
    }

    // إضافة معلومات الدفع
    doc.fontSize(14).text('معلومات الدفع:', { align: 'right' });

    // إذا كانت الفاتورة بالأقساط
    if (invoice.payment_type === 'أقساط') {
      doc.fontSize(12).text(`نوع الدفع: بالأقساط (${invoice.installments_count} قسط)`, { align: 'right' });

      if (installments.length > 0) {
        // إنشاء جدول الأقساط
        const installmentsTableTop = doc.y + 10;

        // رسم رأس الجدول
        doc.fontSize(10);
        doc.text('رقم القسط', doc.page.width - 100, installmentsTableTop, { width: 50, align: 'center' });
        doc.text('المبلغ', doc.page.width - 200, installmentsTableTop, { width: 100, align: 'center' });
        doc.text('تاريخ الاستحقاق', doc.page.width - 300, installmentsTableTop, { width: 100, align: 'center' });
        doc.text('الحالة', doc.page.width - 400, installmentsTableTop, { width: 100, align: 'center' });

        // رسم خط أفقي
        doc.moveTo(doc.page.width - 450, installmentsTableTop + 15)
           .lineTo(doc.page.width - 50, installmentsTableTop + 15)
           .stroke();

        // إضافة بيانات الأقساط
        let installmentsTableY = installmentsTableTop + 20;

        for (const installment of installments) {
          doc.text(installment.installment_number.toString(), doc.page.width - 100, installmentsTableY, { width: 50, align: 'center' });
          doc.text(installment.amount.toLocaleString(), doc.page.width - 200, installmentsTableY, { width: 100, align: 'center' });
          doc.text(new Date(installment.due_date).toLocaleDateString('ar-SA'), doc.page.width - 300, installmentsTableY, { width: 100, align: 'center' });
          doc.text(installment.status, doc.page.width - 400, installmentsTableY, { width: 100, align: 'center' });

          installmentsTableY += 20;

          // التحقق من الحاجة لصفحة جديدة
          if (installmentsTableY > doc.page.height - 100) {
            doc.addPage();
            installmentsTableY = 50;
          }
        }

        // رسم خط أفقي
        doc.moveTo(doc.page.width - 450, installmentsTableY)
           .lineTo(doc.page.width - 50, installmentsTableY)
           .stroke();

        doc.y = installmentsTableY + 10;
      }
    } else {
      doc.fontSize(12).text('نوع الدفع: دفعة واحدة', { align: 'right' });
    }

    // إضافة ملخص الفاتورة
    doc.moveDown();
    doc.fontSize(14).text('ملخص الفاتورة:', { align: 'right' });
    doc.fontSize(12).text(`إجمالي المبلغ: ${invoice.total_amount.toLocaleString()} ر.س`, { align: 'right' });
    doc.fontSize(12).text(`المبلغ المدفوع: ${invoice.paid_amount.toLocaleString()} ر.س`, { align: 'right' });
    doc.fontSize(12).text(`المبلغ المتبقي: ${(invoice.total_amount - invoice.paid_amount).toLocaleString()} ر.س`, { align: 'right' });
    doc.fontSize(12).text(`حالة الفاتورة: ${invoice.status}`, { align: 'right' });

    // إضافة QR Code للفاتورة
    const qrData = JSON.stringify({
      invoiceNumber: invoice.invoice_number,
      customerName: invoice.customer_name,
      totalAmount: invoice.total_amount,
      issueDate: invoice.issue_date
    });

    try {
      const qrDataURL = await QRCode.toDataURL(qrData);
      doc.image(qrDataURL, doc.page.width - 150, doc.page.height - 150, { width: 100 });
    } catch (qrError) {
      console.error('خطأ في إنشاء رمز QR:', qrError);
    }

    // إضافة تذييل الصفحة
    const pageCount = doc.bufferedPageRange().count;
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);

      // إضافة رقم الصفحة
      doc.fontSize(8).text(
        `الصفحة ${i + 1} من ${pageCount}`,
        50,
        doc.page.height - 50,
        { align: 'center', width: doc.page.width - 100 }
      );

      // إضافة معلومات الشركة
      doc.fontSize(8).text(
        'اتش قروب لصناعة الأثاث والديكور - جميع الحقوق محفوظة © ' + new Date().getFullYear(),
        50,
        doc.page.height - 40,
        { align: 'center', width: doc.page.width - 100 }
      );
    }

    // إنهاء الملف
    doc.end();

    // انتظار انتهاء الكتابة
    return new Promise((resolve, reject) => {
      stream.on('finish', () => {
        resolve({ success: true, path: pdfPath });
      });

      stream.on('error', (error) => {
        reject(error);
      });
    });
  } catch (error) {
    console.error(`خطأ في إنشاء ملف PDF للفاتورة رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث للمدفوعات
ipcMain.handle('payments:getByInvoiceId', async (event, invoiceId) => {
  try {
    return db.prepare('SELECT * FROM payments WHERE invoice_id = ? ORDER BY payment_date DESC').all(invoiceId);
  } catch (error) {
    console.error(`خطأ في الحصول على مدفوعات الفاتورة رقم ${invoiceId}:`, error);
    throw error;
  }
});

ipcMain.handle('payments:create', async (event, payment) => {
  try {
    const result = db.prepare(
      'INSERT INTO payments (invoice_id, payment_date, amount, payment_method, notes) VALUES (?, ?, ?, ?, ?)'
    ).run(
      payment.invoice_id,
      payment.payment_date || new Date().toISOString(),
      payment.amount,
      payment.payment_method,
      payment.notes
    );

    // تحديث المبلغ المدفوع في الفاتورة
    const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(payment.invoice_id);
    if (invoice) {
      const newPaidAmount = invoice.paid_amount + payment.amount;
      const newStatus = newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً';

      db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
        newPaidAmount,
        newStatus,
        payment.invoice_id
      );

      // إنشاء عملية مالية للدفعة
      db.prepare(`
        INSERT INTO financial_transactions (
          transaction_date, type, category, amount, description, related_invoice_id, related_order_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        payment.payment_date || new Date().toISOString(),
        'income',
        'invoice_payment',
        payment.amount,
        payment.notes || `دفعة للفاتورة ${invoice.invoice_number}`,
        payment.invoice_id,
        invoice.order_id
      );
    }

    return { id: result.lastInsertRowid, ...payment };
  } catch (error) {
    console.error('خطأ في إنشاء دفعة جديدة:', error);
    throw error;
  }
});

ipcMain.handle('payments:update', async (event, id, payment) => {
  try {
    // الحصول على الدفعة الحالية
    const currentPayment = db.prepare('SELECT * FROM payments WHERE id = ?').get(id);

    if (currentPayment) {
      // حساب الفرق في المبلغ
      const amountDifference = payment.amount - currentPayment.amount;

      // تحديث الدفعة
      db.prepare(
        'UPDATE payments SET payment_date = ?, amount = ?, payment_method = ?, notes = ? WHERE id = ?'
      ).run(
        payment.payment_date,
        payment.amount,
        payment.payment_method,
        payment.notes,
        id
      );

      // تحديث المبلغ المدفوع في الفاتورة إذا تغير المبلغ
      if (amountDifference !== 0) {
        const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(payment.invoice_id);
        if (invoice) {
          const newPaidAmount = invoice.paid_amount + amountDifference;
          const newStatus = newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً';

          db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
            newPaidAmount,
            newStatus,
            payment.invoice_id
          );

          // تحديث العملية المالية المرتبطة
          const transaction = db.prepare(
            'SELECT * FROM financial_transactions WHERE description LIKE ? AND related_invoice_id = ? LIMIT 1'
          ).get(`%${currentPayment.notes || 'دفعة'}%`, payment.invoice_id);

          if (transaction) {
            db.prepare(`
              UPDATE financial_transactions
              SET transaction_date = ?, amount = ?, description = ?
              WHERE id = ?
            `).run(
              payment.payment_date,
              payment.amount,
              payment.notes || `دفعة للفاتورة ${invoice.invoice_number}`,
              transaction.id
            );
          }
        }
      }
    }

    return { id, ...payment };
  } catch (error) {
    console.error(`خطأ في تحديث الدفعة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('payments:delete', async (event, id) => {
  try {
    // الحصول على معلومات الدفعة قبل الحذف
    const payment = db.prepare('SELECT * FROM payments WHERE id = ?').get(id);

    if (payment) {
      // حذف الدفعة
      db.prepare('DELETE FROM payments WHERE id = ?').run(id);

      // تحديث المبلغ المدفوع في الفاتورة
      const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(payment.invoice_id);
      if (invoice) {
        const newPaidAmount = Math.max(0, invoice.paid_amount - payment.amount);
        const newStatus = newPaidAmount <= 0 ? 'غير مدفوعة' : (newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً');

        db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
          newPaidAmount,
          newStatus,
          payment.invoice_id
        );

        // حذف العملية المالية المرتبطة
        db.prepare(
          'DELETE FROM financial_transactions WHERE description LIKE ? AND related_invoice_id = ? LIMIT 1'
        ).run(`%${payment.notes || 'دفعة'}%`, payment.invoice_id);
      }
    }

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الدفعة رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('payments:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT p.*, i.invoice_number, o.order_number, c.name as customer_name
      FROM payments p
      JOIN invoices i ON p.invoice_id = i.id
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE p.payment_date BETWEEN ? AND ?
      ORDER BY p.payment_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على المدفوعات بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// معالجات الأحداث للأقساط
ipcMain.handle('installments:getByInvoiceId', async (event, invoiceId) => {
  try {
    return db.prepare(`
      SELECT * FROM installments
      WHERE invoice_id = ?
      ORDER BY installment_number
    `).all(invoiceId);
  } catch (error) {
    console.error(`خطأ في الحصول على أقساط الفاتورة رقم ${invoiceId}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM installments WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:create', async (event, installment) => {
  try {
    const result = db.prepare(`
      INSERT INTO installments (
        invoice_id, installment_number, amount, due_date, status, notes
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      installment.invoice_id,
      installment.installment_number,
      installment.amount,
      installment.due_date,
      installment.status || 'غير مدفوع',
      installment.notes
    );

    return { id: result.lastInsertRowid, ...installment };
  } catch (error) {
    console.error('خطأ في إنشاء قسط جديد:', error);
    throw error;
  }
});

ipcMain.handle('installments:update', async (event, id, installment) => {
  try {
    db.prepare(`
      UPDATE installments SET
        amount = ?, due_date = ?, status = ?, notes = ?
      WHERE id = ?
    `).run(
      installment.amount,
      installment.due_date,
      installment.status,
      installment.notes,
      id
    );

    return { id, ...installment };
  } catch (error) {
    console.error(`خطأ في تحديث القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:delete', async (event, id) => {
  try {
    // التحقق من عدم وجود دفعة مرتبطة بالقسط
    const installment = db.prepare('SELECT * FROM installments WHERE id = ?').get(id);
    if (installment && installment.payment_id) {
      throw new Error('لا يمكن حذف القسط لأنه مرتبط بدفعة');
    }

    db.prepare('DELETE FROM installments WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:pay', async (event, id, paymentData) => {
  try {
    // بدء المعاملة
    db.prepare('BEGIN TRANSACTION').run();

    // الحصول على معلومات القسط
    const installment = db.prepare('SELECT * FROM installments WHERE id = ?').get(id);
    if (!installment) {
      throw new Error('القسط غير موجود');
    }

    // الحصول على معلومات الفاتورة
    const invoice = db.prepare('SELECT * FROM invoices WHERE id = ?').get(installment.invoice_id);
    if (!invoice) {
      throw new Error('الفاتورة غير موجودة');
    }

    // إنشاء دفعة جديدة
    const paymentResult = db.prepare(`
      INSERT INTO payments (
        invoice_id, payment_date, amount, payment_method, notes
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      installment.invoice_id,
      paymentData.payment_date || new Date().toISOString(),
      installment.amount,
      paymentData.payment_method || 'نقدي',
      paymentData.notes || `دفعة للقسط رقم ${installment.installment_number}`
    );

    // تحديث حالة القسط
    db.prepare(`
      UPDATE installments SET
        status = ?, payment_date = ?, payment_id = ?
      WHERE id = ?
    `).run(
      'مدفوع',
      paymentData.payment_date || new Date().toISOString(),
      paymentResult.lastInsertRowid,
      id
    );

    // تحديث المبلغ المدفوع في الفاتورة
    const newPaidAmount = invoice.paid_amount + installment.amount;
    const newStatus = newPaidAmount >= invoice.total_amount ? 'مدفوعة' : 'مدفوعة جزئياً';

    db.prepare('UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?').run(
      newPaidAmount,
      newStatus,
      installment.invoice_id
    );

    // إنشاء عملية مالية للدفعة
    db.prepare(`
      INSERT INTO financial_transactions (
        transaction_date, type, category, amount, description, related_invoice_id, related_order_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      paymentData.payment_date || new Date().toISOString(),
      'income',
      'installment_payment',
      installment.amount,
      `دفعة للقسط رقم ${installment.installment_number} من الفاتورة ${invoice.invoice_number}`,
      installment.invoice_id,
      invoice.order_id
    );

    // إنهاء المعاملة
    db.prepare('COMMIT').run();

    return {
      success: true,
      installment_id: id,
      payment_id: paymentResult.lastInsertRowid,
      amount: installment.amount
    };
  } catch (error) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    db.prepare('ROLLBACK').run();
    console.error(`خطأ في دفع القسط رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('installments:getDueInstallments', async () => {
  try {
    const today = new Date().toISOString().split('T')[0];

    return db.prepare(`
      SELECT i.*, inv.invoice_number, o.order_number, c.name as customer_name
      FROM installments i
      JOIN invoices inv ON i.invoice_id = inv.id
      JOIN orders o ON inv.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.status = 'غير مدفوع' AND i.due_date <= ? AND i.due_date >= ?
      ORDER BY i.due_date
    `).all(today + 'T23:59:59.999Z', today + 'T00:00:00.000Z');
  } catch (error) {
    console.error('خطأ في الحصول على الأقساط المستحقة اليوم:', error);
    throw error;
  }
});

ipcMain.handle('installments:getLateInstallments', async () => {
  try {
    const today = new Date().toISOString().split('T')[0];

    return db.prepare(`
      SELECT i.*, inv.invoice_number, o.order_number, c.name as customer_name
      FROM installments i
      JOIN invoices inv ON i.invoice_id = inv.id
      JOIN orders o ON inv.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      WHERE i.status = 'غير مدفوع' AND i.due_date < ?
      ORDER BY i.due_date
    `).all(today + 'T00:00:00.000Z');
  } catch (error) {
    console.error('خطأ في الحصول على الأقساط المتأخرة:', error);
    throw error;
  }
});

// معالجات الأحداث للإشعارات
ipcMain.handle('notifications:getAll', async () => {
  try {
    return db.prepare(`
      SELECT * FROM notifications
      ORDER BY created_at DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الإشعارات:', error);
    throw error;
  }
});

ipcMain.handle('notifications:getUnread', async () => {
  try {
    return db.prepare(`
      SELECT * FROM notifications
      WHERE read = 0
      ORDER BY created_at DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على الإشعارات غير المقروءة:', error);
    throw error;
  }
});

ipcMain.handle('notifications:create', async (event, notification) => {
  try {
    const result = db.prepare(`
      INSERT INTO notifications (
        type, title, message, link, read, created_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      notification.type,
      notification.title,
      notification.message,
      notification.link,
      notification.read || 0,
      notification.created_at || new Date().toISOString()
    );

    return { id: result.lastInsertRowid, ...notification };
  } catch (error) {
    console.error('خطأ في إنشاء إشعار جديد:', error);
    throw error;
  }
});

ipcMain.handle('notifications:markAsRead', async (event, id) => {
  try {
    db.prepare(`
      UPDATE notifications
      SET read = 1
      WHERE id = ?
    `).run(id);

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في تعليم الإشعار رقم ${id} كمقروء:`, error);
    throw error;
  }
});

ipcMain.handle('notifications:markAllAsRead', async () => {
  try {
    db.prepare(`
      UPDATE notifications
      SET read = 1
      WHERE read = 0
    `).run();

    return { success: true };
  } catch (error) {
    console.error('خطأ في تعليم جميع الإشعارات كمقروءة:', error);
    throw error;
  }
});

ipcMain.handle('notifications:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM notifications WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف الإشعار رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('notifications:deleteAll', async () => {
  try {
    db.prepare('DELETE FROM notifications').run();
    return { success: true };
  } catch (error) {
    console.error('خطأ في حذف جميع الإشعارات:', error);
    throw error;
  }
});

// وظيفة تصدير البيانات إلى Excel
ipcMain.handle('export:toExcel', async (event, data, sheetName) => {
  try {
    // إنشاء مصنف Excel جديد
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'اتش قروب';
    workbook.lastModifiedBy = 'اتش قروب';
    workbook.created = new Date();
    workbook.modified = new Date();

    // إضافة ورقة عمل
    const worksheet = workbook.addWorksheet(sheetName || 'البيانات');

    // إعداد رأس الجدول
    if (data.length > 0) {
      const headers = Object.keys(data[0]);
      worksheet.columns = headers.map(header => ({
        header,
        key: header,
        width: 20
      }));

      // تنسيق رأس الجدول
      worksheet.getRow(1).font = {
        bold: true,
        size: 12
      };

      worksheet.getRow(1).alignment = {
        vertical: 'middle',
        horizontal: 'center'
      };

      // إضافة البيانات
      worksheet.addRows(data);

      // تنسيق الخلايا
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
          row.alignment = {
            vertical: 'middle',
            horizontal: 'right'
          };
        }

        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });
    }

    // إنشاء مسار للملف
    const excelFileName = `${sheetName || 'تقرير'}_${new Date().toISOString().split('T')[0]}.xlsx`;
    const downloadsPath = app.getPath('downloads');
    const excelPath = path.join(downloadsPath, excelFileName);

    // حفظ الملف
    await workbook.xlsx.writeFile(excelPath);

    return { success: true, path: excelPath };
  } catch (error) {
    console.error('خطأ في تصدير البيانات إلى Excel:', error);
    throw error;
  }
});

// معالجات الأحداث للعمليات المالية
ipcMain.handle('financialTransactions:getAll', async () => {
  try {
    return db.prepare(`
      SELECT * FROM financial_transactions
      ORDER BY transaction_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على العمليات المالية:', error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getById', async (event, id) => {
  try {
    return db.prepare('SELECT * FROM financial_transactions WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على العملية المالية رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:create', async (event, transaction) => {
  try {
    const result = db.prepare(`
      INSERT INTO financial_transactions (
        transaction_date, type, category, amount, description,
        related_order_id, related_worker_id, related_invoice_id, related_expense_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      transaction.transaction_date || new Date().toISOString(),
      transaction.type,
      transaction.category,
      transaction.amount,
      transaction.description,
      transaction.related_order_id || null,
      transaction.related_worker_id || null,
      transaction.related_invoice_id || null,
      transaction.related_expense_id || null
    );

    return { id: result.lastInsertRowid, ...transaction };
  } catch (error) {
    console.error('خطأ في إنشاء عملية مالية جديدة:', error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:update', async (event, id, transaction) => {
  try {
    db.prepare(`
      UPDATE financial_transactions SET
        transaction_date = ?, type = ?, category = ?, amount = ?, description = ?,
        related_order_id = ?, related_worker_id = ?, related_invoice_id = ?, related_expense_id = ?
      WHERE id = ?
    `).run(
      transaction.transaction_date,
      transaction.type,
      transaction.category,
      transaction.amount,
      transaction.description,
      transaction.related_order_id || null,
      transaction.related_worker_id || null,
      transaction.related_invoice_id || null,
      transaction.related_expense_id || null,
      id
    );

    return { id, ...transaction };
  } catch (error) {
    console.error(`خطأ في تحديث العملية المالية رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:delete', async (event, id) => {
  try {
    db.prepare('DELETE FROM financial_transactions WHERE id = ?').run(id);
    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف العملية المالية رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getByType', async (event, type) => {
  try {
    return db.prepare(
      'SELECT * FROM financial_transactions WHERE type = ? ORDER BY transaction_date DESC'
    ).all(type);
  } catch (error) {
    console.error(`خطأ في الحصول على العمليات المالية بنوع "${type}":`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getByCategory', async (event, category) => {
  try {
    return db.prepare(
      'SELECT * FROM financial_transactions WHERE category = ? ORDER BY transaction_date DESC'
    ).all(category);
  } catch (error) {
    console.error(`خطأ في الحصول على العمليات المالية بفئة "${category}":`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT * FROM financial_transactions
      WHERE transaction_date BETWEEN ? AND ?
      ORDER BY transaction_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على العمليات المالية بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

ipcMain.handle('financialTransactions:getBalance', async () => {
  try {
    const result = db.prepare('SELECT SUM(amount) as balance FROM financial_transactions').get();
    return { balance: result.balance || 0 };
  } catch (error) {
    console.error('خطأ في الحصول على الرصيد:', error);
    throw error;
  }
});

// معالجات الأحداث للتقارير
ipcMain.handle('reports:getSalesByPeriod', async (event, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `WHERE i.issue_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    return db.prepare(`
      SELECT
        i.id, i.invoice_number, i.issue_date, i.total_amount, i.paid_amount,
        o.order_number, o.specifications,
        c.name as customer_name
      FROM invoices i
      JOIN orders o ON i.order_id = o.id
      JOIN customers c ON o.customer_id = c.id
      ${dateFilter}
      ORDER BY i.issue_date DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير المبيعات لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getExpensesByPeriod', async (event, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    return db.prepare(`
      SELECT * FROM financial_transactions
      ${dateFilter} AND type = 'expense'
      ORDER BY transaction_date DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير المصروفات لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getProfitByPeriod', async (event, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setHours(0, 0, 0, 0);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `WHERE transaction_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const income = db.prepare(`
      SELECT SUM(amount) as total FROM financial_transactions
      ${dateFilter} AND type = 'income'
    `).get();

    const expenses = db.prepare(`
      SELECT SUM(amount) as total FROM financial_transactions
      ${dateFilter} AND type = 'expense'
    `).get();

    const incomeTotal = income.total || 0;
    const expensesTotal = Math.abs(expenses.total || 0);
    const profit = incomeTotal - expensesTotal;

    return {
      income: incomeTotal,
      expenses: expensesTotal,
      profit: profit
    };
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير الأرباح لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getWorkerPerformance', async (event, workerId, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `AND ow.assigned_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `AND ow.assigned_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `AND ow.assigned_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const workerFilter = workerId ? `AND ow.worker_id = ${workerId}` : '';

    return db.prepare(`
      SELECT
        w.id as worker_id, w.name as worker_name, w.role,
        COUNT(ow.id) as total_orders,
        SUM(ow.fee) as total_fees,
        COUNT(CASE WHEN ow.status = 'مكتمل' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN ow.status = 'قيد التنفيذ' THEN 1 END) as in_progress_orders
      FROM workers w
      LEFT JOIN order_workers ow ON w.id = ow.worker_id
      WHERE 1=1 ${workerFilter} ${dateFilter}
      GROUP BY w.id
      ORDER BY total_orders DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير أداء العمال لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getMaterialUsage', async (event, materialId, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const materialFilter = materialId ? `AND om.material_id = ${materialId}` : '';

    return db.prepare(`
      SELECT
        m.id as material_id, m.name as material_name, m.unit,
        SUM(om.quantity) as total_quantity,
        SUM(om.total_cost) as total_cost,
        COUNT(DISTINCT om.order_id) as order_count
      FROM materials m
      LEFT JOIN order_materials om ON m.id = om.material_id
      LEFT JOIN orders o ON om.order_id = o.id
      WHERE 1=1 ${materialFilter} ${dateFilter}
      GROUP BY m.id
      ORDER BY total_quantity DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير استخدام المواد لفترة "${period}":`, error);
    throw error;
  }
});

ipcMain.handle('reports:getProductPerformance', async (event, productId, period) => {
  try {
    let dateFilter = '';
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFilter = `AND o.order_date >= '${startDate.toISOString()}'`;
        break;
      default:
        dateFilter = '';
    }

    const productFilter = productId ? `AND o.product_id = ${productId}` : '';

    return db.prepare(`
      SELECT
        p.id as product_id, p.name as product_name, p.category,
        COUNT(o.id) as order_count,
        SUM(o.final_price) as total_sales,
        SUM(o.materials_cost) as total_materials_cost,
        SUM(o.worker_fee) as total_worker_fees,
        SUM(o.factory_fee) as total_factory_fees,
        SUM(o.designer_fee) as total_designer_fees,
        SUM(o.owner_margin) as total_margin
      FROM products p
      LEFT JOIN orders o ON p.id = o.product_id
      WHERE 1=1 ${productFilter} ${dateFilter}
      GROUP BY p.id
      ORDER BY total_sales DESC
    `).all();
  } catch (error) {
    console.error(`خطأ في الحصول على تقرير أداء المنتجات لفترة "${period}":`, error);
    throw error;
  }
});

// معالجات الأحداث للمستخدمين
let currentUser = null;

ipcMain.handle('users:login', async (event, username, password) => {
  try {
    const user = db.prepare('SELECT * FROM users WHERE username = ?').get(username);

    if (!user) {
      throw new Error('اسم المستخدم غير موجود');
    }

    if (user.password !== password) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // تحديث آخر تسجيل دخول
    db.prepare('UPDATE users SET last_login = ? WHERE id = ?').run(new Date().toISOString(), user.id);

    // تسجيل العملية في سجل العمليات
    db.prepare(
      'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
    ).run(user.id, 'login', 'تسجيل دخول للنظام');

    // حفظ المستخدم الحالي
    currentUser = {
      id: user.id,
      username: user.username,
      full_name: user.full_name,
      role: user.role,
      permissions: user.permissions
    };

    return currentUser;
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    throw error;
  }
});

ipcMain.handle('users:logout', async () => {
  try {
    if (currentUser) {
      // تسجيل العملية في سجل العمليات
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'logout', 'تسجيل خروج من النظام');

      currentUser = null;
    }

    return { success: true };
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    throw error;
  }
});

ipcMain.handle('users:getCurrentUser', async () => {
  return currentUser;
});

ipcMain.handle('users:getAll', async () => {
  try {
    return db.prepare('SELECT id, username, full_name, role, permissions, last_login, created_at FROM users').all();
  } catch (error) {
    console.error('خطأ في الحصول على المستخدمين:', error);
    throw error;
  }
});

ipcMain.handle('users:getById', async (event, id) => {
  try {
    return db.prepare('SELECT id, username, full_name, role, permissions, last_login, created_at FROM users WHERE id = ?').get(id);
  } catch (error) {
    console.error(`خطأ في الحصول على المستخدم رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('users:create', async (event, user) => {
  try {
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = db.prepare('SELECT id FROM users WHERE username = ?').get(user.username);
    if (existingUser) {
      throw new Error('اسم المستخدم موجود بالفعل');
    }

    const result = db.prepare(
      'INSERT INTO users (username, password, full_name, role, permissions) VALUES (?, ?, ?, ?, ?)'
    ).run(
      user.username,
      user.password,
      user.full_name,
      user.role || 'user',
      user.permissions || ''
    );

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'create_user', `إنشاء مستخدم جديد: ${user.username}`);
    }

    return {
      id: result.lastInsertRowid,
      username: user.username,
      full_name: user.full_name,
      role: user.role || 'user',
      permissions: user.permissions || ''
    };
  } catch (error) {
    console.error('خطأ في إنشاء مستخدم جديد:', error);
    throw error;
  }
});

ipcMain.handle('users:update', async (event, id, user) => {
  try {
    // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
    const existingUser = db.prepare('SELECT id FROM users WHERE username = ? AND id != ?').get(user.username, id);
    if (existingUser) {
      throw new Error('اسم المستخدم موجود بالفعل');
    }

    const updateQuery = user.password
      ? 'UPDATE users SET username = ?, password = ?, full_name = ?, role = ?, permissions = ? WHERE id = ?'
      : 'UPDATE users SET username = ?, full_name = ?, role = ?, permissions = ? WHERE id = ?';

    const params = user.password
      ? [user.username, user.password, user.full_name, user.role, user.permissions, id]
      : [user.username, user.full_name, user.role, user.permissions, id];

    db.prepare(updateQuery).run(...params);

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'update_user', `تحديث بيانات المستخدم: ${user.username}`);
    }

    // تحديث المستخدم الحالي إذا كان هو نفسه
    if (currentUser && currentUser.id === id) {
      currentUser = {
        id,
        username: user.username,
        full_name: user.full_name,
        role: user.role,
        permissions: user.permissions
      };
    }

    return {
      id,
      username: user.username,
      full_name: user.full_name,
      role: user.role,
      permissions: user.permissions
    };
  } catch (error) {
    console.error(`خطأ في تحديث المستخدم رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('users:delete', async (event, id) => {
  try {
    // التحقق من عدم حذف المستخدم الحالي
    if (currentUser && currentUser.id === id) {
      throw new Error('لا يمكن حذف المستخدم الحالي');
    }

    // التحقق من عدم حذف المستخدم الوحيد بدور المدير
    const user = db.prepare('SELECT role FROM users WHERE id = ?').get(id);
    if (user && user.role === 'admin') {
      const adminCount = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = "admin"').get();
      if (adminCount.count <= 1) {
        throw new Error('لا يمكن حذف المستخدم الوحيد بدور المدير');
      }
    }

    // حذف سجل العمليات المرتبطة بالمستخدم
    db.prepare('DELETE FROM activity_log WHERE user_id = ?').run(id);

    // حذف المستخدم
    db.prepare('DELETE FROM users WHERE id = ?').run(id);

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'delete_user', `حذف المستخدم رقم: ${id}`);
    }

    return { success: true, id };
  } catch (error) {
    console.error(`خطأ في حذف المستخدم رقم ${id}:`, error);
    throw error;
  }
});

ipcMain.handle('users:changePassword', async (event, id, oldPassword, newPassword) => {
  try {
    // التحقق من كلمة المرور القديمة
    const user = db.prepare('SELECT password FROM users WHERE id = ?').get(id);
    if (!user) {
      throw new Error('المستخدم غير موجود');
    }

    if (user.password !== oldPassword) {
      throw new Error('كلمة المرور القديمة غير صحيحة');
    }

    // تحديث كلمة المرور
    db.prepare('UPDATE users SET password = ? WHERE id = ?').run(newPassword, id);

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'change_password', `تغيير كلمة المرور للمستخدم رقم: ${id}`);
    }

    return { success: true };
  } catch (error) {
    console.error(`خطأ في تغيير كلمة المرور للمستخدم رقم ${id}:`, error);
    throw error;
  }
});

// معالجات الأحداث لسجل العمليات
ipcMain.handle('activityLog:getAll', async () => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      ORDER BY a.action_date DESC
    `).all();
  } catch (error) {
    console.error('خطأ في الحصول على سجل العمليات:', error);
    throw error;
  }
});

ipcMain.handle('activityLog:getByUserId', async (event, userId) => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      WHERE a.user_id = ?
      ORDER BY a.action_date DESC
    `).all(userId);
  } catch (error) {
    console.error(`خطأ في الحصول على سجل عمليات المستخدم رقم ${userId}:`, error);
    throw error;
  }
});

ipcMain.handle('activityLog:getByActionType', async (event, actionType) => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      WHERE a.action_type = ?
      ORDER BY a.action_date DESC
    `).all(actionType);
  } catch (error) {
    console.error(`خطأ في الحصول على سجل العمليات بنوع "${actionType}":`, error);
    throw error;
  }
});

ipcMain.handle('activityLog:getByDateRange', async (event, startDate, endDate) => {
  try {
    return db.prepare(`
      SELECT a.*, u.username, u.full_name
      FROM activity_log a
      JOIN users u ON a.user_id = u.id
      WHERE a.action_date BETWEEN ? AND ?
      ORDER BY a.action_date DESC
    `).all(startDate, endDate);
  } catch (error) {
    console.error(`خطأ في الحصول على سجل العمليات بين ${startDate} و ${endDate}:`, error);
    throw error;
  }
});

// معالجات الأحداث للإعدادات
ipcMain.handle('settings:get', async (event, key) => {
  try {
    return store.get(key);
  } catch (error) {
    console.error(`خطأ في الحصول على الإعداد "${key}":`, error);
    throw error;
  }
});

ipcMain.handle('settings:set', async (event, key, value) => {
  try {
    store.set(key, value);
    return { success: true, key, value };
  } catch (error) {
    console.error(`خطأ في تعيين الإعداد "${key}":`, error);
    throw error;
  }
});

ipcMain.handle('settings:getAll', async () => {
  try {
    return store.store;
  } catch (error) {
    console.error('خطأ في الحصول على جميع الإعدادات:', error);
    throw error;
  }
});

ipcMain.handle('settings:backup', async (event, path) => {
  try {
    // إغلاق قاعدة البيانات مؤقتاً
    if (db) {
      db.close();
    }

    // نسخ ملف قاعدة البيانات
    const dbPath = app.getPath('userData') + '/hgroup.db';
    fs.copyFileSync(dbPath, path);

    // إعادة فتح قاعدة البيانات
    db = new sqlite3(app.getPath('userData') + '/hgroup.db');

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'backup', `إنشاء نسخة احتياطية في: ${path}`);
    }

    return { success: true, path };
  } catch (error) {
    console.error(`خطأ في إنشاء نسخة احتياطية في "${path}":`, error);
    throw error;
  }
});

ipcMain.handle('settings:restore', async (event, path) => {
  try {
    // إغلاق قاعدة البيانات مؤقتاً
    if (db) {
      db.close();
    }

    // استعادة ملف قاعدة البيانات
    const dbPath = app.getPath('userData') + '/hgroup.db';
    fs.copyFileSync(path, dbPath);

    // إعادة فتح قاعدة البيانات
    db = new sqlite3(app.getPath('userData') + '/hgroup.db');

    // تسجيل العملية في سجل العمليات
    if (currentUser) {
      db.prepare(
        'INSERT INTO activity_log (user_id, action_type, action_details) VALUES (?, ?, ?)'
      ).run(currentUser.id, 'restore', `استعادة نسخة احتياطية من: ${path}`);
    }

    return { success: true, path };
  } catch (error) {
    console.error(`خطأ في استعادة نسخة احتياطية من "${path}":`, error);
    throw error;
  }
});
