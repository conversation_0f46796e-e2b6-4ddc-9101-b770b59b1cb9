{"env": {"browser": true, "node": true, "amd": true}, "rules": {"no-alert": 2, "no-caller": 2, "no-bitwise": 0, "no-catch-shadow": 2, "no-console": 2, "no-comma-dangle": 2, "no-control-regex": 2, "no-debugger": 2, "no-div-regex": 2, "no-dupe-keys": 2, "no-else-return": 2, "no-empty": 2, "no-empty-class": 2, "no-eq-null": 2, "no-eval": 2, "no-ex-assign": 2, "no-func-assign": 0, "no-floating-decimal": 2, "no-implied-eval": 2, "no-with": 2, "no-fallthrough": 2, "no-global-strict": 2, "no-unreachable": 2, "no-undef": 2, "no-undef-init": 2, "no-unused-expressions": 0, "no-octal": 2, "no-octal-escape": 2, "no-obj-calls": 2, "no-multi-str": 2, "no-new-wrappers": 2, "no-new": 2, "no-new-func": 2, "no-native-reassign": 2, "no-plusplus": 0, "no-delete-var": 2, "no-return-assign": 2, "no-new-array": 2, "no-new-object": 2, "no-label-var": 2, "no-ternary": 0, "no-self-compare": 2, "no-sync": 2, "no-underscore-dangle": 2, "no-loop-func": 2, "no-empty-label": 2, "no-unused-vars": 0, "no-script-url": 2, "no-proto": 2, "no-iterator": 2, "no-mixed-requires": [0, false], "no-wrap-func": 2, "no-shadow": 2, "no-use-before-define": 0, "no-redeclare": 2, "brace-style": 0, "block-scoped-var": 0, "camelcase": 2, "complexity": [0, 11], "consistent-this": [0, "that"], "curly": 2, "dot-notation": 2, "eqeqeq": 2, "guard-for-in": 0, "max-depth": [0, 4], "max-len": [0, 80, 4], "max-params": [0, 3], "max-statements": [0, 10], "new-cap": 2, "new-parens": 2, "one-var": 0, "quotes": [2, "single"], "quote-props": 0, "radix": 0, "regex-spaces": 2, "semi": 2, "strict": 2, "unnecessary-strict": 0, "use-isnan": 2, "wrap-iife": 2, "wrap-regex": 0}}