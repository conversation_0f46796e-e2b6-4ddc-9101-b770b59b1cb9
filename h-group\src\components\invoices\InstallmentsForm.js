const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');

const InstallmentsForm = ({ invoice, onSubmit }) => {
  const [installmentsCount, setInstallmentsCount] = useState(1);
  const [downPayment, setDownPayment] = useState(0);
  const [installments, setInstallments] = useState([]);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // إعادة حساب الأقساط عند تغيير عدد الأقساط أو الدفعة الأولى
  useEffect(() => {
    if (!invoice || !invoice.total_amount) return;
    
    // التحقق من صحة المدخلات
    if (installmentsCount < 1) {
      setInstallmentsCount(1);
      return;
    }
    
    if (downPayment < 0) {
      setDownPayment(0);
      return;
    }
    
    if (downPayment > invoice.total_amount) {
      setDownPayment(invoice.total_amount);
      return;
    }
    
    // حساب المبلغ المتبقي بعد الدفعة الأولى
    const remainingAmount = invoice.total_amount - downPayment;
    
    // حساب قيمة كل قسط
    const installmentAmount = remainingAmount / installmentsCount;
    
    // إنشاء الأقساط
    const newInstallments = [];
    const today = new Date();
    
    for (let i = 0; i < installmentsCount; i++) {
      // حساب تاريخ استحقاق القسط (شهر واحد بعد القسط السابق)
      const dueDate = new Date(today);
      dueDate.setMonth(today.getMonth() + i + 1);
      
      newInstallments.push({
        installment_number: i + 1,
        amount: installmentAmount,
        due_date: dueDate.toISOString().split('T')[0],
        status: 'غير مدفوع',
        notes: `القسط رقم ${i + 1}`
      });
    }
    
    setInstallments(newInstallments);
  }, [invoice, installmentsCount, downPayment]);

  // تعديل قيمة قسط
  const handleInstallmentAmountChange = (index, value) => {
    const newAmount = parseFloat(value);
    
    if (isNaN(newAmount) || newAmount <= 0) return;
    
    const newInstallments = [...installments];
    newInstallments[index].amount = newAmount;
    
    // التحقق من أن مجموع الأقساط يساوي المبلغ المتبقي
    const totalInstallmentsAmount = newInstallments.reduce((sum, inst) => sum + inst.amount, 0);
    const remainingAmount = invoice.total_amount - downPayment;
    
    if (Math.abs(totalInstallmentsAmount - remainingAmount) > 0.01) {
      // تعديل القسط الأخير ليكون الفرق
      const lastIndex = newInstallments.length - 1;
      if (index !== lastIndex) {
        newInstallments[lastIndex].amount = remainingAmount - newInstallments.slice(0, lastIndex).reduce((sum, inst) => sum + inst.amount, 0);
      }
    }
    
    setInstallments(newInstallments);
  };

  // تعديل تاريخ استحقاق قسط
  const handleInstallmentDueDateChange = (index, value) => {
    const newInstallments = [...installments];
    newInstallments[index].due_date = value;
    setInstallments(newInstallments);
  };

  // إرسال نموذج الأقساط
  const handleSubmit = (e) => {
    e.preventDefault();
    
    try {
      // التحقق من صحة المدخلات
      if (installmentsCount < 1) {
        setError('يجب أن يكون عدد الأقساط أكبر من صفر');
        return;
      }
      
      if (downPayment < 0 || downPayment > invoice.total_amount) {
        setError('الدفعة الأولى غير صحيحة');
        return;
      }
      
      // التحقق من أن مجموع الأقساط يساوي المبلغ المتبقي
      const totalInstallmentsAmount = installments.reduce((sum, inst) => sum + inst.amount, 0);
      const remainingAmount = invoice.total_amount - downPayment;
      
      if (Math.abs(totalInstallmentsAmount - remainingAmount) > 0.01) {
        setError('مجموع الأقساط لا يساوي المبلغ المتبقي');
        return;
      }
      
      // إعداد بيانات الفاتورة
      const invoiceData = {
        ...invoice,
        payment_type: 'أقساط',
        installments_count: installmentsCount,
        paid_amount: downPayment,
        status: downPayment > 0 ? 'مدفوعة جزئياً' : 'غير مدفوعة'
      };
      
      // استدعاء دالة الإرسال
      onSubmit(invoiceData, installments);
    } catch (error) {
      console.error('خطأ في إعداد الأقساط:', error);
      setError('حدث خطأ أثناء إعداد الأقساط. يرجى المحاولة مرة أخرى.');
    }
  };

  return (
    <div className="card">
      <div className="card-header">إعداد الأقساط</div>
      <div className="card-body">
        {error && <div className="alert alert-danger">{error}</div>}
        
        <form onSubmit={handleSubmit}>
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">المبلغ الإجمالي</label>
                <input
                  type="text"
                  className="form-control"
                  value={invoice?.total_amount?.toLocaleString() || 0}
                  disabled
                />
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">الدفعة الأولى</label>
                <input
                  type="number"
                  className="form-control"
                  value={downPayment}
                  onChange={(e) => setDownPayment(parseFloat(e.target.value) || 0)}
                  min="0"
                  max={invoice?.total_amount || 0}
                  step="0.01"
                />
              </div>
            </div>
          </div>
          
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">المبلغ المتبقي</label>
                <input
                  type="text"
                  className="form-control"
                  value={(invoice?.total_amount - downPayment)?.toLocaleString() || 0}
                  disabled
                />
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">عدد الأقساط</label>
                <input
                  type="number"
                  className="form-control"
                  value={installmentsCount}
                  onChange={(e) => setInstallmentsCount(parseInt(e.target.value) || 1)}
                  min="1"
                  max="36"
                />
              </div>
            </div>
          </div>
          
          <h4 className="mt-4">تفاصيل الأقساط</h4>
          
          <div className="table-responsive">
            <table className="table">
              <thead>
                <tr>
                  <th>رقم القسط</th>
                  <th>المبلغ</th>
                  <th>تاريخ الاستحقاق</th>
                </tr>
              </thead>
              <tbody>
                {installments.map((installment, index) => (
                  <tr key={index}>
                    <td>{installment.installment_number}</td>
                    <td>
                      <input
                        type="number"
                        className="form-control"
                        value={installment.amount}
                        onChange={(e) => handleInstallmentAmountChange(index, e.target.value)}
                        min="0.01"
                        step="0.01"
                      />
                    </td>
                    <td>
                      <input
                        type="date"
                        className="form-control"
                        value={installment.due_date}
                        onChange={(e) => handleInstallmentDueDateChange(index, e.target.value)}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="form-actions">
            <button type="submit" className="btn btn-primary">
              <i className="fas fa-save"></i> حفظ الأقساط
            </button>
            <button type="button" className="btn btn-secondary" onClick={() => navigate(-1)}>
              <i className="fas fa-times"></i> إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

module.exports = InstallmentsForm;
