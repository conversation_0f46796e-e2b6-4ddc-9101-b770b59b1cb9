const React = require('react');
const { useState, useEffect } = React;
const { Link } = require('react-router-dom');
const { useAuth } = require('../contexts/AuthContext');

const Dashboard = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // بيانات لوحة التحكم
  const [stats, setStats] = useState({
    ordersCount: 0,
    pendingOrdersCount: 0,
    completedOrdersCount: 0,
    customersCount: 0,
    workersCount: 0,
    lowStockItems: 0,
    unpaidInvoices: 0,
    thisMonthSales: 0,
    thisMonthExpenses: 0,
    thisMonthProfit: 0
  });

  // الطلبات الأخيرة
  const [recentOrders, setRecentOrders] = useState([]);

  // المدفوعات الأخيرة
  const [recentPayments, setRecentPayments] = useState([]);

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // محاولة تحميل البيانات من API
        if (window.api) {
          // الإحصائيات العامة
          const ordersCount = await window.api.orders.getAll().then(orders => orders.length);
          const pendingOrdersCount = await window.api.orders.getByStatus('قيد التنفيذ').then(orders => orders.length);
          const completedOrdersCount = await window.api.orders.getByStatus('مكتمل').then(orders => orders.length);
          const customersCount = await window.api.customers.getAll().then(customers => customers.length);
          const workersCount = await window.api.workers.getAll().then(workers => workers.length);
          const lowStockItems = await window.api.inventory.getLowStock().then(items => items.length);
          const unpaidInvoices = await window.api.invoices.getByStatus('غير مدفوعة').then(invoices => invoices.length);

          // الإحصائيات المالية
          const financialStats = await window.api.reports.getProfitByPeriod('month');

          // تحديث الإحصائيات
          setStats({
            ordersCount,
            pendingOrdersCount,
            completedOrdersCount,
            customersCount,
            workersCount,
            lowStockItems,
            unpaidInvoices,
            thisMonthSales: financialStats.income,
            thisMonthExpenses: financialStats.expenses,
            thisMonthProfit: financialStats.profit
          });

          // الطلبات الأخيرة
          const allOrders = await window.api.orders.getAll();
          setRecentOrders(allOrders.slice(0, 5));

          // المدفوعات الأخيرة
          const payments = await window.api.financialTransactions.getByType('income');
          setRecentPayments(payments.slice(0, 5));
        } else {
          // بيانات وهمية في حالة عدم وجود API
          setStats({
            ordersCount: 25,
            pendingOrdersCount: 8,
            completedOrdersCount: 17,
            customersCount: 45,
            workersCount: 12,
            lowStockItems: 3,
            unpaidInvoices: 5,
            thisMonthSales: 125000,
            thisMonthExpenses: 85000,
            thisMonthProfit: 40000
          });
        }

      } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        setError('حدث خطأ أثناء تحميل البيانات. يرجى تحديث الصفحة.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل البيانات...');
  }

  if (error) {
    return React.createElement('div', { className: 'alert alert-danger' }, error);
  }

  return React.createElement('div', { className: 'dashboard' },
    React.createElement('h1', { className: 'page-title' }, 'لوحة التحكم'),

    // ترحيب بالمستخدم
    React.createElement('div', { className: 'welcome-card' },
      React.createElement('h2', null, `مرحباً، ${currentUser?.full_name || currentUser?.username || 'المستخدم'}`),
      React.createElement('p', null, `آخر تسجيل دخول: ${new Date().toLocaleDateString('ar-SA')}`)
    ),

    // الإحصائيات
    React.createElement('div', { className: 'stats-container' },
      React.createElement('div', { className: 'stats-row' },
        // إحصائيات الطلبات
        React.createElement('div', { className: 'stat-card' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-shopping-cart' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'الطلبات'),
            React.createElement('div', { className: 'stat-value' }, stats.ordersCount),
            React.createElement('div', { className: 'stat-details' },
              React.createElement('span', null, `${stats.pendingOrdersCount} قيد التنفيذ`),
              React.createElement('span', null, `${stats.completedOrdersCount} مكتمل`)
            )
          )
        ),

        // إحصائيات العملاء
        React.createElement('div', { className: 'stat-card' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-users' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'العملاء'),
            React.createElement('div', { className: 'stat-value' }, stats.customersCount)
          )
        ),

        // إحصائيات العمال
        React.createElement('div', { className: 'stat-card' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-hard-hat' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'العمال والمصممين'),
            React.createElement('div', { className: 'stat-value' }, stats.workersCount)
          )
        ),

        // إحصائيات المخزون
        React.createElement('div', { className: 'stat-card' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-boxes' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'المواد منخفضة المخزون'),
            React.createElement('div', { className: 'stat-value' }, stats.lowStockItems),
            stats.lowStockItems > 0 && React.createElement(Link, { to: '/inventory', className: 'stat-action' }, 'عرض التفاصيل')
          )
        )
      ),

      React.createElement('div', { className: 'stats-row' },
        // إحصائيات المبيعات
        React.createElement('div', { className: 'stat-card financial-stat' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-chart-line' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'مبيعات الشهر'),
            React.createElement('div', { className: 'stat-value' }, `${stats.thisMonthSales.toLocaleString()} ر.س`)
          )
        ),

        // إحصائيات المصروفات
        React.createElement('div', { className: 'stat-card financial-stat' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-chart-pie' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'مصروفات الشهر'),
            React.createElement('div', { className: 'stat-value' }, `${stats.thisMonthExpenses.toLocaleString()} ر.س`)
          )
        ),

        // إحصائيات الأرباح
        React.createElement('div', { className: 'stat-card financial-stat' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-coins' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'أرباح الشهر'),
            React.createElement('div', { className: 'stat-value' }, `${stats.thisMonthProfit.toLocaleString()} ر.س`)
          )
        ),

        // إحصائيات الفواتير
        React.createElement('div', { className: 'stat-card financial-stat' },
          React.createElement('div', { className: 'stat-icon' },
            React.createElement('i', { className: 'fas fa-file-invoice-dollar' })
          ),
          React.createElement('div', { className: 'stat-content' },
            React.createElement('h3', null, 'الفواتير غير المدفوعة'),
            React.createElement('div', { className: 'stat-value' }, stats.unpaidInvoices),
            stats.unpaidInvoices > 0 && React.createElement(Link, { to: '/invoices', className: 'stat-action' }, 'عرض التفاصيل')
          )
        )
      )
    ),

    // الطلبات الأخيرة والمدفوعات الأخيرة
    React.createElement('div', { className: 'dashboard-tables' },
      // الطلبات الأخيرة
      React.createElement('div', { className: 'dashboard-table' },
        React.createElement('div', { className: 'table-header' },
          React.createElement('h3', null, 'الطلبات الأخيرة'),
          React.createElement(Link, { to: '/orders', className: 'view-all' }, 'عرض الكل')
        ),

        React.createElement('table', { className: 'table' },
          React.createElement('thead', null,
            React.createElement('tr', null,
              React.createElement('th', null, 'رقم الطلب'),
              React.createElement('th', null, 'العميل'),
              React.createElement('th', null, 'التاريخ'),
              React.createElement('th', null, 'الحالة'),
              React.createElement('th', null, 'السعر')
            )
          ),
          React.createElement('tbody', null,
            recentOrders.length > 0 ? (
              recentOrders.map(order =>
                React.createElement('tr', { key: order.id },
                  React.createElement('td', null,
                    React.createElement(Link, { to: `/orders/${order.id}` }, order.order_number)
                  ),
                  React.createElement('td', null, order.customer_name),
                  React.createElement('td', null, new Date(order.order_date).toLocaleDateString('ar-SA')),
                  React.createElement('td', null,
                    React.createElement('span', { className: `status-badge status-${order.status}` }, order.status)
                  ),
                  React.createElement('td', null, `${order.final_price.toLocaleString()} ر.س`)
                )
              )
            ) : (
              React.createElement('tr', null,
                React.createElement('td', { colSpan: '5', className: 'text-center' }, 'لا توجد طلبات حالياً')
              )
            )
          )
        )
      ),

      // المدفوعات الأخيرة
      React.createElement('div', { className: 'dashboard-table' },
        React.createElement('div', { className: 'table-header' },
          React.createElement('h3', null, 'المدفوعات الأخيرة'),
          React.createElement(Link, { to: '/invoices', className: 'view-all' }, 'عرض الكل')
        ),

        React.createElement('table', { className: 'table' },
          React.createElement('thead', null,
            React.createElement('tr', null,
              React.createElement('th', null, 'التاريخ'),
              React.createElement('th', null, 'الوصف'),
              React.createElement('th', null, 'المبلغ')
            )
          ),
          React.createElement('tbody', null,
            recentPayments.length > 0 ? (
              recentPayments.map(payment =>
                React.createElement('tr', { key: payment.id },
                  React.createElement('td', null, new Date(payment.transaction_date).toLocaleDateString('ar-SA')),
                  React.createElement('td', null, payment.description),
                  React.createElement('td', null, `${payment.amount.toLocaleString()} ر.س`)
                )
              )
            ) : (
              React.createElement('tr', null,
                React.createElement('td', { colSpan: '3', className: 'text-center' }, 'لا توجد مدفوعات حالياً')
              )
            )
          )
        )
      )
    )
  );
};

module.exports = Dashboard;
