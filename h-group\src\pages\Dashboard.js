const React = require('react');
const { useState, useEffect } = React;
const { Link } = require('react-router-dom');
const { useAuth } = require('../contexts/AuthContext');

const Dashboard = () => {
  const { currentUser, hasPermission } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // بيانات لوحة التحكم
  const [stats, setStats] = useState({
    ordersCount: 0,
    pendingOrdersCount: 0,
    completedOrdersCount: 0,
    customersCount: 0,
    workersCount: 0,
    lowStockItems: 0,
    unpaidInvoices: 0,
    thisMonthSales: 0,
    thisMonthExpenses: 0,
    thisMonthProfit: 0
  });

  // الطلبات الأخيرة
  const [recentOrders, setRecentOrders] = useState([]);

  // المدفوعات الأخيرة
  const [recentPayments, setRecentPayments] = useState([]);

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // الإحصائيات العامة
        const ordersCount = await window.api.orders.getAll().then(orders => orders.length);
        const pendingOrdersCount = await window.api.orders.getByStatus('قيد التنفيذ').then(orders => orders.length);
        const completedOrdersCount = await window.api.orders.getByStatus('مكتمل').then(orders => orders.length);
        const customersCount = await window.api.customers.getAll().then(customers => customers.length);
        const workersCount = await window.api.workers.getAll().then(workers => workers.length);
        const lowStockItems = await window.api.inventory.getLowStock().then(items => items.length);
        const unpaidInvoices = await window.api.invoices.getByStatus('غير مدفوعة').then(invoices => invoices.length);

        // الإحصائيات المالية
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
        const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString();

        const financialStats = await window.api.reports.getProfitByPeriod('month');

        // تحديث الإحصائيات
        setStats({
          ordersCount,
          pendingOrdersCount,
          completedOrdersCount,
          customersCount,
          workersCount,
          lowStockItems,
          unpaidInvoices,
          thisMonthSales: financialStats.income,
          thisMonthExpenses: financialStats.expenses,
          thisMonthProfit: financialStats.profit
        });

        // الطلبات الأخيرة
        const allOrders = await window.api.orders.getAll();
        setRecentOrders(allOrders.slice(0, 5));

        // المدفوعات الأخيرة
        const payments = await window.api.financialTransactions.getByType('income');
        setRecentPayments(payments.slice(0, 5));

      } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        setError('حدث خطأ أثناء تحميل البيانات. يرجى تحديث الصفحة.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return <div className="loading">جاري تحميل البيانات...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="dashboard">
      <h1 className="page-title">لوحة التحكم</h1>

      {/* ترحيب بالمستخدم */}
      <div className="welcome-card">
        <h2>مرحباً، {currentUser?.full_name || currentUser?.username}</h2>
        <p>آخر تسجيل دخول: {new Date().toLocaleDateString('ar-SA')}</p>
      </div>

      {/* الإحصائيات */}
      <div className="stats-container">
        <div className="stats-row">
          {/* إحصائيات الطلبات */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="stat-content">
              <h3>الطلبات</h3>
              <div className="stat-value">{stats.ordersCount}</div>
              <div className="stat-details">
                <span>{stats.pendingOrdersCount} قيد التنفيذ</span>
                <span>{stats.completedOrdersCount} مكتمل</span>
              </div>
            </div>
          </div>

          {/* إحصائيات العملاء */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="stat-content">
              <h3>العملاء</h3>
              <div className="stat-value">{stats.customersCount}</div>
            </div>
          </div>

          {/* إحصائيات العمال */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-hard-hat"></i>
            </div>
            <div className="stat-content">
              <h3>العمال والمصممين</h3>
              <div className="stat-value">{stats.workersCount}</div>
            </div>
          </div>

          {/* إحصائيات المخزون */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-boxes"></i>
            </div>
            <div className="stat-content">
              <h3>المواد منخفضة المخزون</h3>
              <div className="stat-value">{stats.lowStockItems}</div>
              {stats.lowStockItems > 0 && (
                <Link to="/inventory" className="stat-action">عرض التفاصيل</Link>
              )}
            </div>
          </div>
        </div>

        <div className="stats-row">
          {/* إحصائيات المبيعات */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="stat-content">
              <h3>مبيعات الشهر</h3>
              <div className="stat-value">{stats.thisMonthSales.toLocaleString()} ر.س</div>
            </div>
          </div>

          {/* إحصائيات المصروفات */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-chart-pie"></i>
            </div>
            <div className="stat-content">
              <h3>مصروفات الشهر</h3>
              <div className="stat-value">{stats.thisMonthExpenses.toLocaleString()} ر.س</div>
            </div>
          </div>

          {/* إحصائيات الأرباح */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-coins"></i>
            </div>
            <div className="stat-content">
              <h3>أرباح الشهر</h3>
              <div className="stat-value">{stats.thisMonthProfit.toLocaleString()} ر.س</div>
            </div>
          </div>

          {/* إحصائيات الفواتير */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-file-invoice-dollar"></i>
            </div>
            <div className="stat-content">
              <h3>الفواتير غير المدفوعة</h3>
              <div className="stat-value">{stats.unpaidInvoices}</div>
              {stats.unpaidInvoices > 0 && (
                <Link to="/invoices" className="stat-action">عرض التفاصيل</Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* الطلبات الأخيرة والمدفوعات الأخيرة */}
      <div className="dashboard-tables">
        {/* الطلبات الأخيرة */}
        <div className="dashboard-table">
          <div className="table-header">
            <h3>الطلبات الأخيرة</h3>
            <Link to="/orders" className="view-all">عرض الكل</Link>
          </div>

          <table className="table">
            <thead>
              <tr>
                <th>رقم الطلب</th>
                <th>العميل</th>
                <th>التاريخ</th>
                <th>الحالة</th>
                <th>السعر</th>
              </tr>
            </thead>
            <tbody>
              {recentOrders.length > 0 ? (
                recentOrders.map(order => (
                  <tr key={order.id}>
                    <td>
                      <Link to={`/orders/${order.id}`}>{order.order_number}</Link>
                    </td>
                    <td>{order.customer_name}</td>
                    <td>{new Date(order.order_date).toLocaleDateString('ar-SA')}</td>
                    <td>
                      <span className={`status-badge status-${order.status}`}>
                        {order.status}
                      </span>
                    </td>
                    <td>{order.final_price.toLocaleString()} ر.س</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="5" className="text-center">لا توجد طلبات حالياً</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* المدفوعات الأخيرة */}
        <div className="dashboard-table">
          <div className="table-header">
            <h3>المدفوعات الأخيرة</h3>
            <Link to="/invoices" className="view-all">عرض الكل</Link>
          </div>

          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>الوصف</th>
                <th>المبلغ</th>
              </tr>
            </thead>
            <tbody>
              {recentPayments.length > 0 ? (
                recentPayments.map(payment => (
                  <tr key={payment.id}>
                    <td>{new Date(payment.transaction_date).toLocaleDateString('ar-SA')}</td>
                    <td>{payment.description}</td>
                    <td>{payment.amount.toLocaleString()} ر.س</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="3" className="text-center">لا توجد مدفوعات حالياً</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

module.exports = Dashboard;
