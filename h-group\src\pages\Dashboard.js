const React = require('react');

const Dashboard = () => {
  // بيانات وهمية للاختبار
  const stats = {
    ordersCount: 25,
    pendingOrdersCount: 8,
    completedOrdersCount: 17,
    customersCount: 45,
    workersCount: 12,
    lowStockItems: 3,
    unpaidInvoices: 5,
    thisMonthSales: 125000,
    thisMonthExpenses: 85000,
    thisMonthProfit: 40000
  };

  return React.createElement('div', { className: 'dashboard' },
    React.createElement('h1', null, 'لوحة التحكم'),

    React.createElement('div', { className: 'stats-grid' },
      React.createElement('div', { className: 'stat-card' },
        React.createElement('h3', null, 'إجمالي الطلبات'),
        React.createElement('p', { className: 'stat-value' }, stats.ordersCount)
      ),

      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('h3', null, 'طلبات قيد التنفيذ'),
        React.createElement('p', { className: 'stat-value' }, stats.pendingOrdersCount)
      ),

      React.createElement('div', { className: 'stat-card success' },
        React.createElement('h3', null, 'طلبات مكتملة'),
        React.createElement('p', { className: 'stat-value' }, stats.completedOrdersCount)
      ),

      React.createElement('div', { className: 'stat-card' },
        React.createElement('h3', null, 'العملاء'),
        React.createElement('p', { className: 'stat-value' }, stats.customersCount)
      ),

      React.createElement('div', { className: 'stat-card' },
        React.createElement('h3', null, 'العمال'),
        React.createElement('p', { className: 'stat-value' }, stats.workersCount)
      ),

      React.createElement('div', { className: 'stat-card danger' },
        React.createElement('h3', null, 'مخزون منخفض'),
        React.createElement('p', { className: 'stat-value' }, stats.lowStockItems)
      ),

      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('h3', null, 'فواتير غير مدفوعة'),
        React.createElement('p', { className: 'stat-value' }, stats.unpaidInvoices)
      ),

      React.createElement('div', { className: 'stat-card success' },
        React.createElement('h3', null, 'مبيعات هذا الشهر'),
        React.createElement('p', { className: 'stat-value' }, `${stats.thisMonthSales.toLocaleString()} ر.س`)
      )
    ),

    React.createElement('div', { className: 'card' },
      React.createElement('div', { className: 'card-header' },
        React.createElement('h3', null, 'مرحباً بك في نظام إدارة ورشة النجارة')
      ),
      React.createElement('div', { className: 'card-body' },
        React.createElement('p', null, 'يمكنك الآن إدارة جميع عمليات ورشة النجارة من خلال هذا النظام المتكامل.'),
        React.createElement('div', { className: 'dashboard-actions' },
          React.createElement('a', { href: '#/orders/new', className: 'btn btn-primary' }, 'إضافة طلب جديد'),
          React.createElement('a', { href: '#/customers/new', className: 'btn btn-secondary' }, 'إضافة عميل جديد'),
          React.createElement('a', { href: '#/reports', className: 'btn btn-info' }, 'عرض التقارير')
        )
      )
    )
  );
};

module.exports = Dashboard;
