const React = require('react');

const Dashboard = () => {
  // بيانات وهمية للاختبار
  const stats = {
    ordersCount: 25,
    pendingOrdersCount: 8,
    completedOrdersCount: 17,
    customersCount: 45,
    workersCount: 12,
    lowStockItems: 3,
    unpaidInvoices: 5,
    thisMonthSales: 125000,
    thisMonthExpenses: 85000,
    thisMonthProfit: 40000
  };

  return (
    <div className="dashboard">
      <h1 className="page-title">لوحة التحكم</h1>

      {/* ترحيب بالمستخدم */}
      <div className="welcome-card">
        <h2>مرحباً، {currentUser?.full_name || currentUser?.username}</h2>
        <p>آخر تسجيل دخول: {new Date().toLocaleDateString('ar-SA')}</p>
      </div>

      {/* الإحصائيات */}
      <div className="stats-container">
        <div className="stats-row">
          {/* إحصائيات الطلبات */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-shopping-cart"></i>
            </div>
            <div className="stat-content">
              <h3>الطلبات</h3>
              <div className="stat-value">{stats.ordersCount}</div>
              <div className="stat-details">
                <span>{stats.pendingOrdersCount} قيد التنفيذ</span>
                <span>{stats.completedOrdersCount} مكتمل</span>
              </div>
            </div>
          </div>

          {/* إحصائيات العملاء */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="stat-content">
              <h3>العملاء</h3>
              <div className="stat-value">{stats.customersCount}</div>
            </div>
          </div>

          {/* إحصائيات العمال */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-hard-hat"></i>
            </div>
            <div className="stat-content">
              <h3>العمال والمصممين</h3>
              <div className="stat-value">{stats.workersCount}</div>
            </div>
          </div>

          {/* إحصائيات المخزون */}
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-boxes"></i>
            </div>
            <div className="stat-content">
              <h3>المواد منخفضة المخزون</h3>
              <div className="stat-value">{stats.lowStockItems}</div>
              {stats.lowStockItems > 0 && (
                <Link to="/inventory" className="stat-action">عرض التفاصيل</Link>
              )}
            </div>
          </div>
        </div>

        <div className="stats-row">
          {/* إحصائيات المبيعات */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="stat-content">
              <h3>مبيعات الشهر</h3>
              <div className="stat-value">{stats.thisMonthSales.toLocaleString()} ر.س</div>
            </div>
          </div>

          {/* إحصائيات المصروفات */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-chart-pie"></i>
            </div>
            <div className="stat-content">
              <h3>مصروفات الشهر</h3>
              <div className="stat-value">{stats.thisMonthExpenses.toLocaleString()} ر.س</div>
            </div>
          </div>

          {/* إحصائيات الأرباح */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-coins"></i>
            </div>
            <div className="stat-content">
              <h3>أرباح الشهر</h3>
              <div className="stat-value">{stats.thisMonthProfit.toLocaleString()} ر.س</div>
            </div>
          </div>

          {/* إحصائيات الفواتير */}
          <div className="stat-card financial-stat">
            <div className="stat-icon">
              <i className="fas fa-file-invoice-dollar"></i>
            </div>
            <div className="stat-content">
              <h3>الفواتير غير المدفوعة</h3>
              <div className="stat-value">{stats.unpaidInvoices}</div>
              {stats.unpaidInvoices > 0 && (
                <Link to="/invoices" className="stat-action">عرض التفاصيل</Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* الطلبات الأخيرة والمدفوعات الأخيرة */}
      <div className="dashboard-tables">
        {/* الطلبات الأخيرة */}
        <div className="dashboard-table">
          <div className="table-header">
            <h3>الطلبات الأخيرة</h3>
            <Link to="/orders" className="view-all">عرض الكل</Link>
          </div>

          <table className="table">
            <thead>
              <tr>
                <th>رقم الطلب</th>
                <th>العميل</th>
                <th>التاريخ</th>
                <th>الحالة</th>
                <th>السعر</th>
              </tr>
            </thead>
            <tbody>
              {recentOrders.length > 0 ? (
                recentOrders.map(order => (
                  <tr key={order.id}>
                    <td>
                      <Link to={`/orders/${order.id}`}>{order.order_number}</Link>
                    </td>
                    <td>{order.customer_name}</td>
                    <td>{new Date(order.order_date).toLocaleDateString('ar-SA')}</td>
                    <td>
                      <span className={`status-badge status-${order.status}`}>
                        {order.status}
                      </span>
                    </td>
                    <td>{order.final_price.toLocaleString()} ر.س</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="5" className="text-center">لا توجد طلبات حالياً</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* المدفوعات الأخيرة */}
        <div className="dashboard-table">
          <div className="table-header">
            <h3>المدفوعات الأخيرة</h3>
            <Link to="/invoices" className="view-all">عرض الكل</Link>
          </div>

          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>الوصف</th>
                <th>المبلغ</th>
              </tr>
            </thead>
            <tbody>
              {recentPayments.length > 0 ? (
                recentPayments.map(payment => (
                  <tr key={payment.id}>
                    <td>{new Date(payment.transaction_date).toLocaleDateString('ar-SA')}</td>
                    <td>{payment.description}</td>
                    <td>{payment.amount.toLocaleString()} ر.س</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="3" className="text-center">لا توجد مدفوعات حالياً</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

module.exports = Dashboard;
