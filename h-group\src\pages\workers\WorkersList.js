const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const WorkersList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  const [workers, setWorkers] = useState([]);
  const [filteredWorkers, setFilteredWorkers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [roles, setRoles] = useState([]);
  
  // تحميل العمال
  useEffect(() => {
    const fetchWorkers = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // تحميل جميع العمال
        const data = await window.api.workers.getAll();
        setWorkers(data);
        setFilteredWorkers(data);
        
        // استخراج الأدوار الفريدة
        const uniqueRoles = [...new Set(data.map(worker => worker.role))].filter(Boolean);
        setRoles(uniqueRoles);
      } catch (error) {
        console.error('خطأ في تحميل العمال:', error);
        setError('حدث خطأ أثناء تحميل العمال. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchWorkers();
  }, []);
  
  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...workers];
    
    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(worker => 
        worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (worker.phone && worker.phone.includes(searchTerm)) ||
        (worker.address && worker.address.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // تطبيق فلتر الدور
    if (roleFilter !== 'all') {
      result = result.filter(worker => worker.role === roleFilter);
    }
    
    setFilteredWorkers(result);
  }, [workers, searchTerm, roleFilter]);
  
  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
    setRoleFilter('all');
  };
  
  // الانتقال إلى صفحة تفاصيل العامل
  const handleViewWorker = (id) => {
    navigate(`/workers/${id}`);
  };
  
  // إنشاء عامل جديد
  const handleCreateWorker = () => {
    if (!hasPermission('workers_create')) {
      alert('ليس لديك صلاحية لإنشاء عامل جديد');
      return;
    }
    
    navigate('/workers/new');
  };
  
  // تعديل العامل
  const handleEditWorker = (id) => {
    if (!hasPermission('workers_edit')) {
      alert('ليس لديك صلاحية لتعديل العامل');
      return;
    }
    
    navigate(`/workers/edit/${id}`);
  };
  
  // حذف العامل
  const handleDeleteWorker = async (id) => {
    if (!hasPermission('workers_delete')) {
      alert('ليس لديك صلاحية لحذف العامل');
      return;
    }
    
    if (window.confirm('هل أنت متأكد من حذف هذا العامل؟')) {
      try {
        const result = await window.api.workers.delete(id);
        if (result.success) {
          // تحديث القائمة بعد الحذف
          setWorkers(workers.filter(worker => worker.id !== id));
          alert('تم حذف العامل بنجاح');
        } else if (result.error === 'has_orders') {
          alert('لا يمكن حذف العامل لأنه مرتبط بطلبات. يجب إزالة العامل من الطلبات أولاً.');
        } else {
          throw new Error('فشل في حذف العامل');
        }
      } catch (error) {
        console.error('خطأ في حذف العامل:', error);
        alert('حدث خطأ أثناء حذف العامل. يرجى المحاولة مرة أخرى.');
      }
    }
  };
  
  // تصدير العمال إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredWorkers.map(worker => ({
        'الاسم': worker.name,
        'الدور': worker.role || '-',
        'رقم الهاتف': worker.phone || '-',
        'العنوان': worker.address || '-',
        'أجر الطلب': worker.fee_per_order ? `${worker.fee_per_order.toLocaleString()} ر.س` : '-',
        'الراتب الشهري': worker.monthly_salary ? `${worker.monthly_salary.toLocaleString()} ر.س` : '-',
        'ملاحظات': worker.notes || '-'
      }));
      
      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'العمال');
      
      if (result.success) {
        alert(`تم تصدير العمال بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير العمال');
      }
    } catch (error) {
      console.error('خطأ في تصدير العمال:', error);
      alert('حدث خطأ أثناء تصدير العمال. يرجى المحاولة مرة أخرى.');
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل العمال...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  return (
    <div className="workers-list-page">
      <div className="page-header">
        <h2>العمال والمصممين</h2>
        <div className="page-actions">
          {hasPermission('workers_create') && (
            <button className="btn btn-primary" onClick={handleCreateWorker}>
              <i className="fas fa-plus"></i> إضافة عامل جديد
            </button>
          )}
          
          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>
      
      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">فلاتر البحث</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">بحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="اسم العامل أو رقم الهاتف أو العنوان"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">الدور</label>
                <select
                  className="form-control"
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                >
                  <option value="all">جميع الأدوار</option>
                  {roles.map((role, index) => (
                    <option key={index} value={role}>{role}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          
          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>
      
      {/* قائمة العمال */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة العمال والمصممين</h5>
            <span className="badge bg-primary">{filteredWorkers.length} عامل</span>
          </div>
        </div>
        <div className="card-body">
          {filteredWorkers.length === 0 ? (
            <div className="alert alert-info">لا يوجد عمال مطابقين للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>الاسم</th>
                    <th>الدور</th>
                    <th>رقم الهاتف</th>
                    <th>أجر الطلب</th>
                    <th>الراتب الشهري</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredWorkers.map(worker => (
                    <tr key={worker.id}>
                      <td>{worker.name}</td>
                      <td>{worker.role || '-'}</td>
                      <td>{worker.phone || '-'}</td>
                      <td>{worker.fee_per_order ? `${worker.fee_per_order.toLocaleString()} ر.س` : '-'}</td>
                      <td>{worker.monthly_salary ? `${worker.monthly_salary.toLocaleString()} ر.س` : '-'}</td>
                      <td>
                        <div className="btn-group">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => handleViewWorker(worker.id)}
                            title="عرض التفاصيل"
                          >
                            <i className="fas fa-eye"></i>
                          </button>
                          
                          {hasPermission('workers_edit') && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleEditWorker(worker.id)}
                              title="تعديل"
                            >
                              <i className="fas fa-edit"></i>
                            </button>
                          )}
                          
                          {hasPermission('workers_delete') && (
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteWorker(worker.id)}
                              title="حذف"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = WorkersList;
