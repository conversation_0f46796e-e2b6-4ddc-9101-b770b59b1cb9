const React = require('react');
const { useState, useEffect } = React;
const { Link } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const PricingSettings = () => {
  const { hasPermission } = useAuth();
  
  // التحقق من الصلاحيات
  useEffect(() => {
    if (!hasPermission('settings_pricing')) {
      alert('ليس لديك صلاحية للوصول إلى هذه الإعدادات');
      navigate('/settings');
    }
  }, [hasPermission]);
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    defaultWorkerFee: '',
    defaultFactoryFee: '',
    defaultDesignerFee: '',
    defaultOwnerMargin: '',
    taxRate: '',
    discountLimit: '',
    enableAutomaticPricing: true
  });
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // تحميل الإعدادات الحالية
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // استدعاء API لجلب إعدادات التسعير
        const settings = await window.api.settings.getPricingSettings();
        
        setFormData({
          defaultWorkerFee: settings.defaultWorkerFee.toString(),
          defaultFactoryFee: settings.defaultFactoryFee.toString(),
          defaultDesignerFee: settings.defaultDesignerFee.toString(),
          defaultOwnerMargin: settings.defaultOwnerMargin.toString(),
          taxRate: settings.taxRate ? settings.taxRate.toString() : '0',
          discountLimit: settings.discountLimit ? settings.discountLimit.toString() : '0',
          enableAutomaticPricing: settings.enableAutomaticPricing !== false
        });
      } catch (error) {
        console.error('خطأ في تحميل إعدادات التسعير:', error);
        setError('حدث خطأ أثناء تحميل إعدادات التسعير. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchSettings();
  }, []);
  
  // معالجة تغيير قيم الحقول
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  // معالجة تغيير القيم الرقمية (للتأكد من أنها أرقام)
  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    if (value === '' || /^\d+(\.\d{0,2})?$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // حفظ الإعدادات
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);
      
      // تحويل القيم إلى أرقام
      const settings = {
        defaultWorkerFee: parseFloat(formData.defaultWorkerFee) || 0,
        defaultFactoryFee: parseFloat(formData.defaultFactoryFee) || 0,
        defaultDesignerFee: parseFloat(formData.defaultDesignerFee) || 0,
        defaultOwnerMargin: parseFloat(formData.defaultOwnerMargin) || 0,
        taxRate: parseFloat(formData.taxRate) || 0,
        discountLimit: parseFloat(formData.discountLimit) || 0,
        enableAutomaticPricing: formData.enableAutomaticPricing
      };
      
      // استدعاء API لحفظ إعدادات التسعير
      const result = await window.api.settings.savePricingSettings(settings);
      
      if (result.success) {
        setSuccess('تم حفظ إعدادات التسعير بنجاح');
        
        // إخفاء رسالة النجاح بعد 3 ثوانٍ
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        throw new Error('فشل في حفظ إعدادات التسعير');
      }
    } catch (error) {
      console.error('خطأ في حفظ إعدادات التسعير:', error);
      setError('حدث خطأ أثناء حفظ إعدادات التسعير. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };
  
  // إعادة تعيين الإعدادات إلى القيم الافتراضية
  const handleReset = async () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين إعدادات التسعير إلى القيم الافتراضية؟')) {
      try {
        setSubmitting(true);
        setError(null);
        setSuccess(null);
        
        // استدعاء API لإعادة تعيين إعدادات التسعير
        const result = await window.api.settings.resetPricingSettings();
        
        if (result.success) {
          // تحديث النموذج بالقيم الافتراضية
          setFormData({
            defaultWorkerFee: result.settings.defaultWorkerFee.toString(),
            defaultFactoryFee: result.settings.defaultFactoryFee.toString(),
            defaultDesignerFee: result.settings.defaultDesignerFee.toString(),
            defaultOwnerMargin: result.settings.defaultOwnerMargin.toString(),
            taxRate: result.settings.taxRate ? result.settings.taxRate.toString() : '0',
            discountLimit: result.settings.discountLimit ? result.settings.discountLimit.toString() : '0',
            enableAutomaticPricing: result.settings.enableAutomaticPricing !== false
          });
          
          setSuccess('تم إعادة تعيين إعدادات التسعير إلى القيم الافتراضية بنجاح');
          
          // إخفاء رسالة النجاح بعد 3 ثوانٍ
          setTimeout(() => {
            setSuccess(null);
          }, 3000);
        } else {
          throw new Error('فشل في إعادة تعيين إعدادات التسعير');
        }
      } catch (error) {
        console.error('خطأ في إعادة تعيين إعدادات التسعير:', error);
        setError('حدث خطأ أثناء إعادة تعيين إعدادات التسعير. يرجى المحاولة مرة أخرى.');
      } finally {
        setSubmitting(false);
      }
    }
  };
  
  if (loading) {
    return <div className="loading">جاري تحميل إعدادات التسعير...</div>;
  }
  
  return (
    <div className="pricing-settings-page">
      <div className="page-header">
        <h2>إعدادات التسعير</h2>
        <div className="page-actions">
          <Link to="/settings" className="btn btn-secondary">
            <i className="fas fa-arrow-right"></i> العودة للإعدادات
          </Link>
        </div>
      </div>
      
      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}
      
      <div className="card">
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">أجر العامل الافتراضي (ر.س)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="defaultWorkerFee"
                    value={formData.defaultWorkerFee}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                  <small className="form-text text-muted">
                    الأجر الافتراضي للعامل لكل طلب إذا لم يتم تحديده
                  </small>
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">رسوم المصنع الافتراضية (ر.س)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="defaultFactoryFee"
                    value={formData.defaultFactoryFee}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                  <small className="form-text text-muted">
                    رسوم المصنع الافتراضية لكل طلب (تكاليف التشغيل)
                  </small>
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">أجر المصمم الافتراضي (ر.س)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="defaultDesignerFee"
                    value={formData.defaultDesignerFee}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                  <small className="form-text text-muted">
                    الأجر الافتراضي للمصمم لكل طلب إذا لم يتم تحديده
                  </small>
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">هامش ربح المالك الافتراضي (%)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="defaultOwnerMargin"
                    value={formData.defaultOwnerMargin}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                  <small className="form-text text-muted">
                    نسبة هامش الربح الافتراضية للمالك (نسبة مئوية)
                  </small>
                </div>
              </div>
            </div>
            
            <div className="row">
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">نسبة الضريبة (%)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="taxRate"
                    value={formData.taxRate}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                  <small className="form-text text-muted">
                    نسبة ضريبة القيمة المضافة (نسبة مئوية)
                  </small>
                </div>
              </div>
              
              <div className="col-md-6">
                <div className="form-group mb-3">
                  <label className="form-label">الحد الأقصى للخصم (%)</label>
                  <input
                    type="text"
                    className="form-control"
                    name="discountLimit"
                    value={formData.discountLimit}
                    onChange={handleNumberChange}
                    placeholder="0.00"
                  />
                  <small className="form-text text-muted">
                    الحد الأقصى للخصم المسموح به (نسبة مئوية)
                  </small>
                </div>
              </div>
            </div>
            
            <div className="form-group mb-3">
              <div className="form-check">
                <input
                  type="checkbox"
                  className="form-check-input"
                  id="enableAutomaticPricing"
                  name="enableAutomaticPricing"
                  checked={formData.enableAutomaticPricing}
                  onChange={handleChange}
                />
                <label className="form-check-label" htmlFor="enableAutomaticPricing">
                  تفعيل حساب الأسعار تلقائيًا
                </label>
              </div>
              <small className="form-text text-muted">
                حساب سعر الطلب تلقائيًا بناءً على تكلفة المواد وأجور العمال ورسوم المصنع وهامش الربح
              </small>
            </div>
            
            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span className="ms-1">جاري الحفظ...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-save"></i>
                    <span className="ms-1">حفظ الإعدادات</span>
                  </>
                )}
              </button>
              
              <button
                type="button"
                className="btn btn-danger"
                onClick={handleReset}
                disabled={submitting}
              >
                <i className="fas fa-redo"></i> إعادة تعيين الإعدادات
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

module.exports = PricingSettings;
