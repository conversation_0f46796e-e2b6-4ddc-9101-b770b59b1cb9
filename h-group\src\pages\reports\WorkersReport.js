const React = require('react');
const { useState, useEffect } = React;

const WorkersReport = () => {
  const [workers, setWorkers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWorkersData();
  }, []);

  const loadWorkersData = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات
      const mockWorkers = [
        {
          id: 1,
          name: 'أحمد محمد',
          role: 'نجار',
          ordersCount: 15,
          totalEarnings: 12000
        },
        {
          id: 2,
          name: 'محمد علي',
          role: 'مصمم',
          ordersCount: 8,
          totalEarnings: 8000
        }
      ];
      setWorkers(mockWorkers);
    } catch (error) {
      console.error('خطأ في تحميل تقرير العمال:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل التقرير...');
  }

  return React.createElement('div', { className: 'workers-report' },
    React.createElement('h1', null, 'تقرير أداء العمال'),
    React.createElement('table', { className: 'table' },
      React.createElement('thead', null,
        React.createElement('tr', null,
          React.createElement('th', null, 'اسم العامل'),
          React.createElement('th', null, 'التخصص'),
          React.createElement('th', null, 'عدد الطلبات'),
          React.createElement('th', null, 'إجمالي الأرباح')
        )
      ),
      React.createElement('tbody', null,
        workers.map(worker =>
          React.createElement('tr', { key: worker.id },
            React.createElement('td', null, worker.name),
            React.createElement('td', null, worker.role),
            React.createElement('td', null, worker.ordersCount),
            React.createElement('td', null, `${worker.totalEarnings.toLocaleString()} ر.س`)
          )
        )
      )
    )
  );
};

module.exports = WorkersReport;
