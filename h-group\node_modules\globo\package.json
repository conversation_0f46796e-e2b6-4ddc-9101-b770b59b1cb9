{"name": "globo", "main": "index.js", "version": "1.1.0", "description": "Turn identifiers into global lookups that work in Node and the browser", "license": "MIT", "repository": "bendrucker/globo", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "standard && tape test.js"}, "keywords": ["global", "id", "module", "browser", "node"], "dependencies": {"accessory": "~1.1.0", "is-defined": "~1.0.0", "ternary": "~1.0.0"}, "devDependencies": {"tape": "^4.0.0", "standard": "^4.0.0"}, "files": ["index.js", "readme.md"]}