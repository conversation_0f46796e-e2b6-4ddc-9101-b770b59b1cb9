const React = require('react');
const { useState, useEffect } = React;

const ExpensesReport = () => {
  const [expenses, setExpenses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    loadExpenses();
  }, [dateRange]);

  const loadExpenses = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات
      const mockExpenses = [
        {
          id: 1,
          date: '2024-05-01',
          description: 'شراء مواد خام',
          amount: 5000,
          category: 'مواد خام'
        },
        {
          id: 2,
          date: '2024-05-02',
          description: 'رواتب العمال',
          amount: 8000,
          category: 'رواتب'
        }
      ];
      setExpenses(mockExpenses);
    } catch (error) {
      console.error('خطأ في تحميل تقرير المصروفات:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل التقرير...');
  }

  return React.createElement('div', { className: 'expenses-report' },
    React.createElement('h1', null, 'تقرير المصروفات'),
    React.createElement('div', { className: 'date-range' },
      React.createElement('label', null, 'من تاريخ:'),
      React.createElement('input', {
        type: 'date',
        value: dateRange.startDate,
        onChange: (e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))
      }),
      React.createElement('label', null, 'إلى تاريخ:'),
      React.createElement('input', {
        type: 'date',
        value: dateRange.endDate,
        onChange: (e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))
      })
    ),
    React.createElement('table', { className: 'table' },
      React.createElement('thead', null,
        React.createElement('tr', null,
          React.createElement('th', null, 'التاريخ'),
          React.createElement('th', null, 'الوصف'),
          React.createElement('th', null, 'الفئة'),
          React.createElement('th', null, 'المبلغ')
        )
      ),
      React.createElement('tbody', null,
        expenses.map(expense =>
          React.createElement('tr', { key: expense.id },
            React.createElement('td', null, expense.date),
            React.createElement('td', null, expense.description),
            React.createElement('td', null, expense.category),
            React.createElement('td', null, `${expense.amount.toLocaleString()} ر.س`)
          )
        )
      )
    )
  );
};

module.exports = ExpensesReport;
