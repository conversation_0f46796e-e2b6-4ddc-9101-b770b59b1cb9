/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { pagespeedonline_v5 } from './v5';
export declare const VERSIONS: {
    v5: typeof pagespeedonline_v5.Pagespeedonline;
};
export declare function pagespeedonline(version: 'v5'): pagespeedonline_v5.Pagespeedonline;
export declare function pagespeedonline(options: pagespeedonline_v5.Options): pagespeedonline_v5.Pagespeedonline;
declare const auth: AuthPlus;
export { auth };
export { pagespeedonline_v5 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
