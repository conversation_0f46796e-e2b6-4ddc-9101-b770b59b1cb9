# سكريبت لإصلاح جميع ملفات import في مجلد src
$files = @(
    "src\components\invoices\InstallmentsForm.js",
    "src\components\invoices\InstallmentsList.js", 
    "src\components\invoices\InvoiceActions.js",
    "src\pages\expenses\ExpenseForm.js",
    "src\pages\installments\DueInstallments.js",
    "src\pages\installments\EditInstallment.js",
    "src\pages\inventory\InventoryForm.js",
    "src\pages\inventory\InventoryList.js",
    "src\pages\invoices\CreateInvoice.js",
    "src\pages\invoices\InvoiceDetails.js",
    "src\pages\invoices\InvoicesList.js",
    "src\pages\materials\MaterialDetails.js",
    "src\pages\materials\MaterialForm.js",
    "src\pages\materials\MaterialsList.js",
    "src\pages\products\ProductDetails.js",
    "src\pages\products\ProductForm.js",
    "src\pages\products\ProductsList.js",
    "src\pages\reports\ReportsList.js",
    "src\pages\reports\SalesReport.js",
    "src\pages\settings\PricingSettings.js",
    "src\pages\settings\SettingsList.js",
    "src\pages\workers\WorkerDetails.js",
    "src\pages\workers\WorkerForm.js",
    "src\pages\workers\WorkersList.js"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "إصلاح ملف: $file"
        
        # قراءة محتوى الملف
        $content = Get-Content $file -Raw
        
        # استبدال import statements
        $content = $content -replace "import React, \{ ([^}]+) \} from 'react';", "const React = require('react');`nconst { `$1 } = React;"
        $content = $content -replace "import React from 'react';", "const React = require('react');"
        $content = $content -replace "import \{ ([^}]+) \} from 'react';", "const React = require('react');`nconst { `$1 } = React;"
        $content = $content -replace "import \{ ([^}]+) \} from 'react-router-dom';", "const { `$1 } = require('react-router-dom');"
        $content = $content -replace "import \{ ([^}]+) \} from '([^']+)';", "const { `$1 } = require('`$2');"
        $content = $content -replace "import ([^\s]+) from '([^']+)';", "const `$1 = require('`$2');"
        
        # استبدال export statements
        $content = $content -replace "export default ([^;]+);", "module.exports = `$1;"
        
        # كتابة المحتوى المحدث
        Set-Content $file $content -Encoding UTF8
        
        Write-Host "تم إصلاح: $file"
    } else {
        Write-Host "الملف غير موجود: $file"
    }
}

Write-Host "تم الانتهاء من إصلاح جميع الملفات"
