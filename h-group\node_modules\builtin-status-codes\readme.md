# builtin-status-codes [![Build Status](https://travis-ci.org/bendrucker/builtin-status-codes.svg?branch=master)](https://travis-ci.org/bendrucker/builtin-status-codes)

> The map of HTTP status codes from the builtin http module. Exposes the latest directly from `http` in Node, with a zero-dependencies version for the browser.


## Install

```
$ npm install --save builtin-status-codes
```


## Usage

```js
var codes = require('builtin-status-codes')
codes[100]
//=> Continue
```

## Build

To create a new browser build:

```sh
$ npm run build
```

## License

MIT © [<PERSON>](http://bendrucker.me)
