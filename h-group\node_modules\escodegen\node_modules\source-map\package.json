{"name": "source-map", "description": "Generates and consumes source maps", "version": "0.1.43", "homepage": "https://github.com/mozilla/source-map", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "usrbincc <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> Smith <<EMAIL>>", "<PERSON> <<EMAIL>>", "azu <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@walmartlabs.com>", "<PERSON> <jeff<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "directories": {"lib": "./lib"}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}}