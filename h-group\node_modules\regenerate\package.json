{"name": "regenerate", "version": "1.4.2", "description": "Generate JavaScript-compatible regular expressions based on a given set of Unicode symbols or code points.", "homepage": "https://mths.be/regenerate", "main": "regenerate.js", "keywords": ["regex", "regexp", "javascript", "unicode", "generator", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate.git"}, "bugs": "https://github.com/mathiasbynens/regenerate/issues", "scripts": {"cover": "istanbul cover --report html --verbose --dir coverage tests/tests.js", "test": "node tests/tests.js"}, "devDependencies": {"codecov": "^1.0.1", "grunt": "^0.4.5", "grunt-shell": "^1.1.1", "istanbul": "^0.4.3", "qunit-extras": "^1.1.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.15"}}