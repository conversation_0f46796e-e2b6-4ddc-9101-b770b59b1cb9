{"name": "transformify", "version": "0.1.2", "description": "Takes a synchronous function that transforms a string and converts it into a transform compatible with browserify, catw and mutiny.", "main": "index.js", "scripts": {"test-main": "tap test/*.js", "test-0.8": "nave use 0.8 npm run test-main", "test-0.10": "nave use 0.10 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/transformify.git"}, "homepage": "https://github.com/thlorenz/transformify", "dependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nave": "~0.4.3", "tap": "~0.4.3", "apply-transform": "~0.1.3"}, "keywords": ["transform", "stream", "browserify", "browserify-transform", "catw", "mutiny"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "license": {"type": "MIT", "url": "https://github.com/thlorenz/transformify/blob/master/LICENSE"}, "engine": {"node": ">=0.6"}}