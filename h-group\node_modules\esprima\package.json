{"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "version": "1.0.4", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://ariya.ofilabs.com"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}}