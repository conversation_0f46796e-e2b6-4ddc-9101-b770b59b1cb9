# undeclared-identifiers change log

All notable changes to this project will be documented in this file.

This project adheres to [Semantic Versioning](http://semver.org/).

## 1.1.3
*  Do not count class names and method names as undeclared. ([#1](https://github.com/goto-bus-stop/undeclared-identifiers/pull/1))

## 1.1.2
* Fix wildcard use not being detected after property use. ([349d998](https://github.com/goto-bus-stop/undeclared-identifiers/commit/349d998559f83976ccd3b3d091e2b06f00ce4189))

## 1.1.1
* Fix standard property access being detected as wildcards. ([029a0b7](https://github.com/goto-bus-stop/undeclared-identifiers/commit/029a0b773a7a4d2402a6de19c8c8693407f8da63))

## 1.1.0
* Accept an AST. ([1605b88](https://github.com/goto-bus-stop/undeclared-identifiers/commit/1605b881cd567894fab1ee2727961dd715a38820))
* Add `opts.wildcard`. ([cdabd70](https://github.com/goto-bus-stop/undeclared-identifiers/commit/cdabd70e000b2fa976c7f4118757736e023b93f2))

## 1.0.0

* initial release.
