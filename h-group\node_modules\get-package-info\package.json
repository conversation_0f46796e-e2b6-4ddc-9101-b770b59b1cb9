{"name": "get-package-info", "description": "Gets properties from package.json files in parent directories.", "version": "1.0.0", "license": "MIT", "main": "lib/index.js", "author": "<PERSON><PERSON>", "repository": "https://github.com/rahatarmanahmed/get-package-info", "scripts": {"lint": "standard", "test": "mocha --compilers js:babel-register,es6:babel-register,es6.js:babel-register test/", "build": "babel -d lib/ src/", "pretest": "npm run lint", "prebuild": "npm run test", "watch": "onchange src/ -- npm run build && echo Done", "dev": "npm run watch", "prepublish": "npm run build"}, "devDependencies": {"babel-cli": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "chai": "^3.4.1", "mocha": "^3.0.0", "onchange": "^3.0.0", "standard": "^8.4.0"}, "dependencies": {"bluebird": "^3.1.1", "debug": "^2.2.0", "lodash.get": "^4.0.0", "read-pkg-up": "^2.0.0"}, "engines": {"node": ">= 4.0"}}