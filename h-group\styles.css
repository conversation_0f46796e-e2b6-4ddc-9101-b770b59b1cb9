/* الخطوط والألوان الأساسية */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  direction: rtl;
  text-align: right;
}

/* التطبيق الرئيسي */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex: 1;
  min-height: 100vh;
}

/* شريط التنقل العلوي */
.navbar {
  background-color: #1e3a8a;
  color: white;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navbar-item {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.navbar-item:hover {
  background-color: rgba(255,255,255,0.1);
}

/* الشريط الجانبي */
.sidebar {
  width: 250px;
  background-color: #1f2937;
  color: white;
  min-height: 100vh;
  padding: 1rem 0;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s;
  border-right: 3px solid transparent;
}

.sidebar-item:hover {
  background-color: #374151;
  color: white;
}

.sidebar-item.active {
  background-color: #3b82f6;
  color: white;
  border-right-color: #f97316;
}

.sidebar-item i {
  margin-left: 0.75rem;
  width: 20px;
  text-align: center;
}

/* المحتوى الرئيسي */
.content-container {
  display: flex;
  flex: 1;
}

.main-content {
  flex: 1;
  padding: 2rem;
  background-color: #f9fafb;
  min-height: 100vh;
}

/* لوحة التحكم */
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard h1 {
  color: #1f2937;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border-right: 4px solid #3b82f6;
}

.stat-card h3 {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
}

.stat-card.warning {
  border-right-color: #f59e0b;
}

.stat-card.danger {
  border-right-color: #ef4444;
}

.stat-card.success {
  border-right-color: #10b981;
}

/* الأزرار */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover {
  background-color: #059669;
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

/* النماذج */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* الجداول */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table th,
.table td {
  padding: 1rem;
  text-align: right;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.table tbody tr:hover {
  background-color: #f9fafb;
}

/* التحميل */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.125rem;
  color: #6b7280;
}

/* التنبيهات */
.alert {
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.alert-danger {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.alert-warning {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

/* صفحة تسجيل الدخول */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  color: #1e3a8a;
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
}

.login-header h2 {
  color: #6b7280;
  margin: 0;
  font-size: 1rem;
  font-weight: normal;
}

.login-form {
  margin-bottom: 1.5rem;
}

.login-button {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
}

.login-footer {
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
}

/* صفحة 404 */
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
}

.not-found-content {
  text-align: center;
  max-width: 400px;
}

.not-found-content h1 {
  font-size: 6rem;
  color: #3b82f6;
  margin: 0;
}

.not-found-content h2 {
  color: #1f2937;
  margin: 1rem 0;
}

.not-found-content p {
  color: #6b7280;
  margin-bottom: 2rem;
}

/* أنماط إضافية للوحة التحكم */
.dashboard-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.dashboard-actions .btn {
  flex: 1;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .main-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-actions {
    flex-direction: column;
  }
}
