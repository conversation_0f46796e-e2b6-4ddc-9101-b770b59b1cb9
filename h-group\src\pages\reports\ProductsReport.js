const React = require('react');
const { useState, useEffect } = React;

const ProductsReport = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProductsData();
  }, []);

  const loadProductsData = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات
      const mockProducts = [
        {
          id: 1,
          name: 'طاولة خشبية',
          ordersCount: 25,
          totalRevenue: 75000,
          averagePrice: 3000
        },
        {
          id: 2,
          name: 'كرسي مكتب',
          ordersCount: 40,
          totalRevenue: 60000,
          averagePrice: 1500
        }
      ];
      setProducts(mockProducts);
    } catch (error) {
      console.error('خطأ في تحميل تقرير المنتجات:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return React.createElement('div', { className: 'loading' }, 'جاري تحميل التقرير...');
  }

  return React.createElement('div', { className: 'products-report' },
    React.createElement('h1', null, 'تقرير أداء المنتجات'),
    React.createElement('table', { className: 'table' },
      React.createElement('thead', null,
        React.createElement('tr', null,
          React.createElement('th', null, 'اسم المنتج'),
          React.createElement('th', null, 'عدد الطلبات'),
          React.createElement('th', null, 'إجمالي الإيرادات'),
          React.createElement('th', null, 'متوسط السعر')
        )
      ),
      React.createElement('tbody', null,
        products.map(product =>
          React.createElement('tr', { key: product.id },
            React.createElement('td', null, product.name),
            React.createElement('td', null, product.ordersCount),
            React.createElement('td', null, `${product.totalRevenue.toLocaleString()} ر.س`),
            React.createElement('td', null, `${product.averagePrice.toLocaleString()} ر.س`)
          )
        )
      )
    )
  );
};

module.exports = ProductsReport;
