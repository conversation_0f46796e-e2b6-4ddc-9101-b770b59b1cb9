{"version": 3, "file": "GraphemeBreak.js", "sourceRoot": "", "sources": ["../../src/GraphemeBreak.ts"], "names": [], "mappings": ";;;AAAA,6DAAyD;AACzD,+BAA2C;AAE3C,IAAM,KAAK,GAAG,CAAC,CAAC;AAChB,IAAM,OAAO,GAAG,CAAC,CAAC;AAClB,IAAM,EAAE,GAAG,CAAC,CAAC;AACb,IAAM,EAAE,GAAG,CAAC,CAAC;AACb,IAAM,OAAO,GAAG,CAAC,CAAC;AAClB,IAAM,MAAM,GAAG,CAAC,CAAC;AACjB,IAAM,kBAAkB,GAAG,CAAC,CAAC;AAC7B,IAAM,WAAW,GAAG,CAAC,CAAC;AACtB,IAAM,CAAC,GAAG,CAAC,CAAC;AACZ,IAAM,CAAC,GAAG,CAAC,CAAC;AACZ,IAAM,CAAC,GAAG,EAAE,CAAC;AACb,IAAM,EAAE,GAAG,EAAE,CAAC;AACd,IAAM,GAAG,GAAG,EAAE,CAAC;AACf,IAAM,GAAG,GAAG,EAAE,CAAC;AACf,IAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,IAAM,EAAE,GAAG,EAAE,CAAC;AAED,QAAA,OAAO,GAA4B;IAC5C,KAAK,OAAA;IACL,OAAO,SAAA;IACP,EAAE,IAAA;IACF,EAAE,IAAA;IACF,OAAO,SAAA;IACP,MAAM,QAAA;IACN,kBAAkB,oBAAA;IAClB,WAAW,aAAA;IACX,CAAC,GAAA;IACD,CAAC,GAAA;IACD,CAAC,GAAA;IACD,EAAE,IAAA;IACF,GAAG,KAAA;IACH,GAAG,KAAA;IACH,qBAAqB,uBAAA;IACrB,EAAE,IAAA;CACL,CAAC;AAEK,IAAM,YAAY,GAAG,UAAC,GAAW;IACpC,IAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,OAAO,CAAC,GAAG,MAAM,EAAE;QACf,IAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE;YAClD,IAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE;gBAC7B,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;aACxE;iBAAM;gBACH,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,EAAE,CAAC;aACP;SACJ;aAAM;YACH,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;KACJ;IACD,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC;AAnBW,QAAA,YAAY,gBAmBvB;AAEK,IAAM,aAAa,GAAG;IAAC,oBAAuB;SAAvB,UAAuB,EAAvB,qBAAuB,EAAvB,IAAuB;QAAvB,+BAAuB;;IACjD,IAAI,MAAM,CAAC,aAAa,EAAE;QACtB,OAAO,MAAM,CAAC,aAAa,OAApB,MAAM,EAAkB,UAAU,EAAE;KAC9C;IAED,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IACjC,IAAI,CAAC,MAAM,EAAE;QACT,OAAO,EAAE,CAAC;KACb;IAED,IAAM,SAAS,GAAG,EAAE,CAAC;IAErB,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;IACf,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE;QACrB,IAAI,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,SAAS,IAAI,MAAM,EAAE;YACrB,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC7B;aAAM;YACH,SAAS,IAAI,OAAO,CAAC;YACrB,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;SAC5E;QACD,IAAI,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,MAAM,EAAE;YACnD,MAAM,IAAI,MAAM,CAAC,YAAY,OAAnB,MAAM,EAAiB,SAAS,CAAC,CAAC;YAC5C,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SACxB;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AA5BW,QAAA,aAAa,iBA4BxB;AAEW,QAAA,WAAW,GAAG,4BAAoB,CAAC,4BAAM,EAAE,gCAAU,CAAC,CAAC;AAEvD,QAAA,iBAAiB,GAAG,GAAG,CAAC;AACxB,QAAA,aAAa,GAAG,GAAG,CAAC;AAI1B,IAAM,gBAAgB,GAAG,UAAC,SAAiB,IAAa,OAAA,mBAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAA1B,CAA0B,CAAC;AAA7E,QAAA,gBAAgB,oBAA6D;AAE1F,IAAM,qBAAqB,GAAG,UAAC,WAAqB,EAAE,UAAoB,EAAE,KAAa;IACrF,IAAI,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;IAC1B,IAAI,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;IACjC,IAAM,OAAO,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACtC,IAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,uCAAuC;IACvC,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,OAAO,yBAAiB,CAAC;KAC5B;IAED,kDAAkD;IAClD,IAAI,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,OAAO,EAAE;QACzD,OAAO,qBAAa,CAAC;KACxB;IAED,MAAM;IACN,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;QAChD,OAAO,qBAAa,CAAC;KACxB;IAED,0CAA0C;IAC1C,MAAM;IACN,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACvD,OAAO,yBAAiB,CAAC;KAC5B;IAED,MAAM;IACN,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;QACjE,OAAO,yBAAiB,CAAC;KAC5B;IAED,MAAM;IACN,IAAI,CAAC,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;QAClD,OAAO,yBAAiB,CAAC;KAC5B;IAED,uDAAuD;IACvD,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,MAAM,EAAE;QACjC,OAAO,yBAAiB,CAAC;KAC5B;IACD,iEAAiE;IACjE,OAAO;IACP,IAAI,IAAI,KAAK,WAAW,EAAE;QACtB,OAAO,yBAAiB,CAAC;KAC5B;IAED,OAAO;IACP,IAAI,OAAO,KAAK,OAAO,EAAE;QACrB,OAAO,yBAAiB,CAAC;KAC5B;IAED,4EAA4E;IAC5E,IAAI,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,qBAAqB,EAAE;QACnD,OAAO,IAAI,KAAK,MAAM,EAAE;YACpB,IAAI,GAAG,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC;SAClC;QACD,IAAI,IAAI,KAAK,qBAAqB,EAAE;YAChC,OAAO,yBAAiB,CAAC;SAC5B;KACJ;IAED,iDAAiD;IACjD,gEAAgE;IAChE,qEAAqE;IACrE,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;QAC/B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,OAAO,IAAI,KAAK,EAAE,EAAE;YAChB,OAAO,EAAE,CAAC;YACV,IAAI,GAAG,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC;SAClC;QACD,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;YACnB,OAAO,yBAAiB,CAAC;SAC5B;KACJ;IAED,OAAO,qBAAa,CAAC;AACzB,CAAC,CAAC;AAEK,IAAM,oBAAoB,GAAG,UAAC,UAAoB,EAAE,KAAa;IACpE,oEAAoE;IACpE,IAAI,KAAK,KAAK,CAAC,EAAE;QACb,OAAO,qBAAa,CAAC;KACxB;IAED,MAAM;IACN,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;QAC5B,OAAO,qBAAa,CAAC;KACxB;IAED,IAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,wBAAgB,CAAC,CAAC;IACpD,OAAO,qBAAqB,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAChE,CAAC,CAAC;AAbW,QAAA,oBAAoB,wBAa/B;AAEK,IAAM,eAAe,GAAG,UAAC,GAAW;IACvC,IAAM,UAAU,GAAG,oBAAY,CAAC,GAAG,CAAC,CAAC;IACrC,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IACjC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,wBAAgB,CAAC,CAAC;IAEpD,OAAO;QACH,IAAI,EAAE;YACF,IAAI,KAAK,IAAI,MAAM,EAAE;gBACjB,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;aACpC;YAED,IAAI,aAAa,GAAG,yBAAiB,CAAC;YACtC,OACI,KAAK,GAAG,MAAM;gBACd,CAAC,aAAa,GAAG,qBAAqB,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC,KAAK,yBAAiB,EAChG,GAAE;YAEJ,IAAI,aAAa,KAAK,yBAAiB,IAAI,KAAK,KAAK,MAAM,EAAE;gBACzD,IAAM,KAAK,GAAG,qBAAa,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC1E,OAAO,GAAG,KAAK,CAAC;gBAChB,OAAO,EAAC,KAAK,OAAA,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;aAC/B;YAED,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;YACjC,OAAO,KAAK,GAAG,MAAM,EAAE,GAAE;YAEzB,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;QACrC,CAAC;KACJ,CAAC;AACN,CAAC,CAAC;AA/BW,QAAA,eAAe,mBA+B1B;AAEK,IAAM,cAAc,GAAG,UAAC,GAAW;IACtC,IAAM,OAAO,GAAG,uBAAe,CAAC,GAAG,CAAC,CAAC;IAErC,IAAM,SAAS,GAAG,EAAE,CAAC;IACrB,IAAI,EAAE,CAAC;IAEP,OAAO,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;QAChC,IAAI,EAAE,CAAC,KAAK,EAAE;YACV,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;SACpC;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAbW,QAAA,cAAc,kBAazB"}