{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../../../src/css/layout/text.ts"], "names": [], "mappings": ";;;AAEA,iDAAwE;AACxE,uDAAiD;AACjD,mCAA6C;AAC7C,gDAA6C;AAG7C;IAII,oBAAY,IAAY,EAAE,MAAc;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IACL,iBAAC;AAAD,CAAC,AARD,IAQC;AARY,gCAAU;AAUhB,IAAM,eAAe,GAAG,UAC3B,OAAgB,EAChB,KAAa,EACb,MAA4B,EAC5B,IAAU;IAEV,IAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC1C,IAAM,UAAU,GAAiB,EAAE,CAAC;IACpC,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,QAAQ,CAAC,OAAO,CAAC,UAAC,IAAI;QAClB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5D,IAAI,mBAAQ,CAAC,oBAAoB,EAAE;gBAC/B,IAAM,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,CAAC;gBAC5E,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxB,IAAM,WAAW,GAAG,wBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC3C,IAAI,WAAS,GAAG,CAAC,CAAC;oBAClB,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU;wBAC3B,UAAU,CAAC,IAAI,CACX,IAAI,UAAU,CACV,UAAU,EACV,eAAM,CAAC,eAAe,CAClB,OAAO,EACP,WAAW,CAAC,IAAI,EAAE,WAAS,GAAG,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,CAC5E,CACJ,CACJ,CAAC;wBACF,WAAS,IAAI,UAAU,CAAC,MAAM,CAAC;oBACnC,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,eAAM,CAAC,eAAe,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;iBACvF;aACJ;iBAAM;gBACH,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpD,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvE,IAAI,GAAG,eAAe,CAAC;aAC1B;SACJ;aAAM,IAAI,CAAC,mBAAQ,CAAC,oBAAoB,EAAE;YACvC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtC;QACD,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC;AA3CW,QAAA,eAAe,mBA2C1B;AAEF,IAAM,gBAAgB,GAAG,UAAC,OAAgB,EAAE,IAAU;IAClD,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;IACzC,IAAI,aAAa,EAAE;QACf,IAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAClE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1C,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvC,IAAM,MAAM,GAAG,oBAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,IAAI,OAAO,CAAC,UAAU,EAAE;gBACpB,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;aACxD;YACD,OAAO,MAAM,CAAC;SACjB;KACJ;IAED,OAAO,eAAM,CAAC,KAAK,CAAC;AACxB,CAAC,CAAC;AAEF,IAAM,WAAW,GAAG,UAAC,IAAU,EAAE,MAAc,EAAE,MAAc;IAC3D,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;IACzC,IAAI,CAAC,aAAa,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KACjD;IACD,IAAM,KAAK,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;IAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7B,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IACpC,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEK,IAAM,gBAAgB,GAAG,UAAC,KAAa;IAC1C,IAAI,mBAAQ,CAAC,gCAAgC,EAAE;QAC3C,8DAA8D;QAC9D,IAAM,SAAS,GAAG,IAAK,IAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAC,WAAW,EAAE,UAAU,EAAC,CAAC,CAAC;QACjF,8DAA8D;QAC9D,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAC,OAAY,IAAK,OAAA,OAAO,CAAC,OAAO,EAAf,CAAe,CAAC,CAAC;KACtF;IAED,OAAO,kCAAc,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC,CAAC;AATW,QAAA,gBAAgB,oBAS3B;AAEF,IAAM,YAAY,GAAG,UAAC,KAAa,EAAE,MAA4B;IAC7D,IAAI,mBAAQ,CAAC,gCAAgC,EAAE;QAC3C,8DAA8D;QAC9D,IAAM,SAAS,GAAG,IAAK,IAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAClD,WAAW,EAAE,MAAM;SACtB,CAAC,CAAC;QACH,8DAA8D;QAC9D,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAC,OAAY,IAAK,OAAA,OAAO,CAAC,OAAO,EAAf,CAAe,CAAC,CAAC;KACtF;IAED,OAAO,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF,IAAM,SAAS,GAAG,UAAC,KAAa,EAAE,MAA4B;IAC1D,OAAO,MAAM,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,wBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC9F,CAAC,CAAC;AAEF,oDAAoD;AACpD,IAAM,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAElF,IAAM,UAAU,GAAG,UAAC,GAAW,EAAE,MAA4B;IACzD,IAAM,OAAO,GAAG,4BAAW,CAAC,GAAG,EAAE;QAC7B,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,SAAS,EAAE,MAAM,CAAC,YAAY,kCAA6B,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS;KAChG,CAAC,CAAC;IAEH,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAI,EAAE,CAAC;;QAGH,IAAI,EAAE,CAAC,KAAK,EAAE;YACV,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAM,UAAU,GAAG,6BAAY,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,MAAI,GAAG,EAAE,CAAC;YACd,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS;gBACzB,IAAI,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC1C,MAAI,IAAI,8BAAa,CAAC,SAAS,CAAC,CAAC;iBACpC;qBAAM;oBACH,IAAI,MAAI,CAAC,MAAM,EAAE;wBACb,KAAK,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC;qBACpB;oBACD,KAAK,CAAC,IAAI,CAAC,8BAAa,CAAC,SAAS,CAAC,CAAC,CAAC;oBACrC,MAAI,GAAG,EAAE,CAAC;iBACb;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,MAAI,CAAC,MAAM,EAAE;gBACb,KAAK,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC;aACpB;SACJ;;IApBL,OAAO,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;;KAqBjC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC"}