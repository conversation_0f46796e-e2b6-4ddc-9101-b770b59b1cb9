const React = require('react');
const { useState, useEffect } = React;

const Settings = () => {
  const [settings, setSettings] = useState({
    defaultWorkerFee: 0,
    defaultFactoryFee: 0,
    defaultDesignerFee: 0,
    defaultOwnerMargin: 0,
    showLowStockNotifications: true,
    showDueInvoicesNotifications: true
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل الإعدادات
      setSettings({
        defaultWorkerFee: 100,
        defaultFactoryFee: 50,
        defaultDesignerFee: 75,
        defaultOwnerMargin: 25,
        showLowStockNotifications: true,
        showDueInvoicesNotifications: true
      });
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      // محاكاة حفظ الإعدادات
      setMessage('تم حفظ الإعدادات بنجاح');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      setMessage('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return React.createElement('div', { className: 'settings' },
    React.createElement('h1', null, 'الإعدادات العامة'),
    
    message && React.createElement('div', { 
      className: `alert ${message.includes('خطأ') ? 'alert-danger' : 'alert-success'}` 
    }, message),

    React.createElement('form', { onSubmit: handleSave },
      React.createElement('div', { className: 'settings-section' },
        React.createElement('h3', null, 'إعدادات التسعير الافتراضية'),
        
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'أجر العامل الافتراضي (ر.س)'),
          React.createElement('input', {
            type: 'number',
            value: settings.defaultWorkerFee,
            onChange: (e) => handleChange('defaultWorkerFee', parseFloat(e.target.value) || 0)
          })
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'تكلفة المصنع الافتراضية (ر.س)'),
          React.createElement('input', {
            type: 'number',
            value: settings.defaultFactoryFee,
            onChange: (e) => handleChange('defaultFactoryFee', parseFloat(e.target.value) || 0)
          })
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'أجر المصمم الافتراضي (ر.س)'),
          React.createElement('input', {
            type: 'number',
            value: settings.defaultDesignerFee,
            onChange: (e) => handleChange('defaultDesignerFee', parseFloat(e.target.value) || 0)
          })
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, 'هامش ربح المالك الافتراضي (%)'),
          React.createElement('input', {
            type: 'number',
            value: settings.defaultOwnerMargin,
            onChange: (e) => handleChange('defaultOwnerMargin', parseFloat(e.target.value) || 0)
          })
        )
      ),

      React.createElement('div', { className: 'settings-section' },
        React.createElement('h3', null, 'إعدادات الإشعارات'),
        
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null,
            React.createElement('input', {
              type: 'checkbox',
              checked: settings.showLowStockNotifications,
              onChange: (e) => handleChange('showLowStockNotifications', e.target.checked)
            }),
            ' إشعارات المخزون المنخفض'
          )
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null,
            React.createElement('input', {
              type: 'checkbox',
              checked: settings.showDueInvoicesNotifications,
              onChange: (e) => handleChange('showDueInvoicesNotifications', e.target.checked)
            }),
            ' إشعارات الفواتير المستحقة'
          )
        )
      ),

      React.createElement('div', { className: 'form-actions' },
        React.createElement('button', {
          type: 'submit',
          className: 'btn btn-primary',
          disabled: loading
        }, loading ? 'جاري الحفظ...' : 'حفظ الإعدادات')
      )
    )
  );
};

module.exports = Settings;
