const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const InstallmentsList = ({ invoiceId }) => {
  const [installments, setInstallments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { hasPermission } = useAuth();
  const navigate = useNavigate();

  // تحميل الأقساط عند تحميل المكون
  useEffect(() => {
    const fetchInstallments = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const data = await window.api.installments.getByInvoiceId(invoiceId);
        setInstallments(data);
      } catch (error) {
        console.error('خطأ في تحميل الأقساط:', error);
        setError('حدث خطأ أثناء تحميل الأقساط. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };
    
    if (invoiceId) {
      fetchInstallments();
    }
  }, [invoiceId]);

  // دفع قسط
  const handlePayInstallment = async (installmentId) => {
    try {
      if (!hasPermission('invoices_edit')) {
        alert('ليس لديك صلاحية لدفع الأقساط');
        return;
      }
      
      const paymentData = {
        payment_date: new Date().toISOString(),
        payment_method: 'نقدي',
        notes: 'دفع قسط'
      };
      
      const result = await window.api.installments.pay(installmentId, paymentData);
      
      if (result.success) {
        // تحديث قائمة الأقساط
        const updatedInstallments = installments.map(installment => 
          installment.id === installmentId 
            ? { ...installment, status: 'مدفوع', payment_date: paymentData.payment_date } 
            : installment
        );
        
        setInstallments(updatedInstallments);
        alert('تم دفع القسط بنجاح');
      }
    } catch (error) {
      console.error('خطأ في دفع القسط:', error);
      alert('حدث خطأ أثناء دفع القسط. يرجى المحاولة مرة أخرى.');
    }
  };

  // تعديل قسط
  const handleEditInstallment = (installmentId) => {
    if (!hasPermission('invoices_edit')) {
      alert('ليس لديك صلاحية لتعديل الأقساط');
      return;
    }
    
    // الانتقال إلى صفحة تعديل القسط
    navigate(`/installments/edit/${installmentId}`);
  };

  if (loading) {
    return <div className="loading">جاري تحميل الأقساط...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (installments.length === 0) {
    return <div className="alert alert-info">لا توجد أقساط لهذه الفاتورة</div>;
  }

  return (
    <div className="installments-container">
      <h3>الأقساط</h3>
      
      {installments.map(installment => (
        <div key={installment.id} className="installment-card">
          <div className="installment-info">
            <div className="installment-number">القسط رقم {installment.installment_number}</div>
            <div className="installment-amount">{installment.amount.toLocaleString()} ر.س</div>
            <div className="installment-date">
              تاريخ الاستحقاق: {new Date(installment.due_date).toLocaleDateString('ar-SA')}
            </div>
            {installment.payment_date && (
              <div className="installment-date">
                تاريخ الدفع: {new Date(installment.payment_date).toLocaleDateString('ar-SA')}
              </div>
            )}
          </div>
          
          <div className={`installment-status ${installment.status === 'مدفوع' ? 'paid' : installment.status === 'متأخر' ? 'late' : 'unpaid'}`}>
            {installment.status}
          </div>
          
          <div className="installment-actions">
            {installment.status !== 'مدفوع' && hasPermission('invoices_edit') && (
              <button 
                className="btn btn-success"
                onClick={() => handlePayInstallment(installment.id)}
              >
                <i className="fas fa-money-bill-wave"></i> دفع
              </button>
            )}
            
            {hasPermission('invoices_edit') && (
              <button 
                className="btn btn-primary"
                onClick={() => handleEditInstallment(installment.id)}
              >
                <i className="fas fa-edit"></i> تعديل
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

module.exports = InstallmentsList;
