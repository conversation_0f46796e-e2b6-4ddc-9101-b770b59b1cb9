const React = require('react');
const { useState, useEffect } = React;
const { useNavigate } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');

const CustomersList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();

  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // فلاتر البحث
  const [searchTerm, setSearchTerm] = useState('');

  // تحميل العملاء
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل جميع العملاء
        const data = await window.api.customers.getAll();
        setCustomers(data);
        setFilteredCustomers(data);
      } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
        setError('حدث خطأ أثناء تحميل العملاء. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  // تطبيق الفلاتر
  useEffect(() => {
    let result = [...customers];

    // تطبيق فلتر البحث
    if (searchTerm) {
      result = result.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (customer.phone && customer.phone.includes(searchTerm)) ||
        (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.address && customer.address.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredCustomers(result);
  }, [customers, searchTerm]);

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    setSearchTerm('');
  };

  // الانتقال إلى صفحة تفاصيل العميل
  const handleViewCustomer = (id) => {
    navigate(`/customers/${id}`);
  };

  // إنشاء عميل جديد
  const handleCreateCustomer = () => {
    if (!hasPermission('customers_create')) {
      alert('ليس لديك صلاحية لإنشاء عميل جديد');
      return;
    }

    navigate('/customers/new');
  };

  // تعديل العميل
  const handleEditCustomer = (id) => {
    if (!hasPermission('customers_edit')) {
      alert('ليس لديك صلاحية لتعديل العميل');
      return;
    }

    navigate(`/customers/edit/${id}`);
  };

  // حذف العميل
  const handleDeleteCustomer = async (id) => {
    if (!hasPermission('customers_delete')) {
      alert('ليس لديك صلاحية لحذف العميل');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        const result = await window.api.customers.delete(id);
        if (result.success) {
          // تحديث القائمة بعد الحذف
          setCustomers(customers.filter(customer => customer.id !== id));
          alert('تم حذف العميل بنجاح');
        } else {
          throw new Error('فشل في حذف العميل');
        }
      } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        alert('حدث خطأ أثناء حذف العميل. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // تصدير العملاء إلى Excel
  const handleExportToExcel = async () => {
    try {
      // تحضير البيانات للتصدير
      const dataToExport = filteredCustomers.map(customer => ({
        'الاسم': customer.name,
        'رقم الهاتف': customer.phone || '-',
        'البريد الإلكتروني': customer.email || '-',
        'العنوان': customer.address || '-',
        'ملاحظات': customer.notes || '-'
      }));

      // استدعاء وظيفة التصدير إلى Excel
      const result = await window.api.exportToExcel(dataToExport, 'العملاء');

      if (result.success) {
        alert(`تم تصدير العملاء بنجاح إلى: ${result.path}`);
      } else {
        throw new Error('فشل في تصدير العملاء');
      }
    } catch (error) {
      console.error('خطأ في تصدير العملاء:', error);
      alert('حدث خطأ أثناء تصدير العملاء. يرجى المحاولة مرة أخرى.');
    }
  };

  if (loading) {
    return <div className="loading">جاري تحميل العملاء...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="customers-list-page">
      <div className="page-header">
        <h2>العملاء</h2>
        <div className="page-actions">
          {hasPermission('customers_create') && (
            <button className="btn btn-primary" onClick={handleCreateCustomer}>
              <i className="fas fa-plus"></i> إضافة عميل جديد
            </button>
          )}

          <button className="btn btn-success" onClick={handleExportToExcel}>
            <i className="fas fa-file-excel"></i> تصدير إلى Excel
          </button>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="card mb-4">
        <div className="card-header">فلاتر البحث</div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">بحث</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="اسم العميل أو رقم الهاتف أو البريد الإلكتروني أو العنوان"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="filter-actions">
            <button className="btn btn-secondary" onClick={handleResetFilters}>
              <i className="fas fa-redo"></i> إعادة تعيين الفلاتر
            </button>
          </div>
        </div>
      </div>

      {/* قائمة العملاء */}
      <div className="card">
        <div className="card-header">
          <div className="d-flex justify-content-between align-items-center">
            <h5>قائمة العملاء</h5>
            <span className="badge bg-primary">{filteredCustomers.length} عميل</span>
          </div>
        </div>
        <div className="card-body">
          {filteredCustomers.length === 0 ? (
            <div className="alert alert-info">لا يوجد عملاء مطابقين للفلاتر المحددة</div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>الاسم</th>
                    <th>رقم الهاتف</th>
                    <th>البريد الإلكتروني</th>
                    <th>العنوان</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCustomers.map(customer => (
                    <tr key={customer.id}>
                      <td>{customer.name}</td>
                      <td>{customer.phone || '-'}</td>
                      <td>{customer.email || '-'}</td>
                      <td>{customer.address || '-'}</td>
                      <td>
                        <div className="btn-group">
                          <button
                            className="btn btn-sm btn-info"
                            onClick={() => handleViewCustomer(customer.id)}
                            title="عرض التفاصيل"
                          >
                            <i className="fas fa-eye"></i>
                          </button>

                          {hasPermission('customers_edit') && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleEditCustomer(customer.id)}
                              title="تعديل"
                            >
                              <i className="fas fa-edit"></i>
                            </button>
                          )}

                          {hasPermission('customers_delete') && (
                            <button
                              className="btn btn-sm btn-danger"
                              onClick={() => handleDeleteCustomer(customer.id)}
                              title="حذف"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

module.exports = CustomersList;
