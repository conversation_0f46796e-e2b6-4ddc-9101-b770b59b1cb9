const fs = require('fs');

// قائمة المكونات المتبقية
const files = [
    'src/components/invoices/InstallmentsForm.js',
    'src/components/invoices/InstallmentsList.js',
    'src/components/invoices/InvoiceActions.js',
    'src/pages/installments/DueInstallments.js',
    'src/pages/installments/EditInstallment.js'
];

files.forEach(filePath => {
    if (fs.existsSync(filePath)) {
        console.log(`إصلاح ملف: ${filePath}`);
        
        let content = fs.readFileSync(filePath, 'utf8');
        
        // استبدال import statements
        content = content.replace(/import React, \{ ([^}]+) \} from 'react';/g, "const React = require('react');\nconst { $1 } = React;");
        content = content.replace(/import React from 'react';/g, "const React = require('react');");
        content = content.replace(/import \{ ([^}]+) \} from 'react';/g, "const React = require('react');\nconst { $1 } = React;");
        content = content.replace(/import \{ ([^}]+) \} from 'react-router-dom';/g, "const { $1 } = require('react-router-dom');");
        content = content.replace(/import \{ ([^}]+) \} from '([^']+)';/g, "const { $1 } = require('$2');");
        content = content.replace(/import ([^\s]+) from '([^']+)';/g, "const $1 = require('$2');");
        
        // استبدال export statements
        content = content.replace(/export default ([^;]+);/g, "module.exports = $1;");
        
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`تم إصلاح: ${filePath}`);
    } else {
        console.log(`الملف غير موجود: ${filePath}`);
    }
});

console.log('تم الانتهاء من إصلاح جميع المكونات');
